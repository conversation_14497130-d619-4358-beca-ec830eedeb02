#!/bin/sh

# Define payload for obtaining access token
TOKEN_PAYLOAD="{\"app_id\": \"cli_a573f8fd787ad010\", \"app_secret\": \"${LARK_APP_SECRET}\"}"

# Make API request to obtain tenant access token
TOKEN_RESPONSE=$(curl -X POST -H "Content-Type:application/json; charset=utf-8" https://open.larksuite.com/open-apis/auth/v3/tenant_access_token/internal --data "${TOKEN_PAYLOAD}")

# Extract the token using jq
TOKEN=$(echo $TOKEN_RESPONSE | jq -r '.tenant_access_token')

# Dynamically construct the JSON content and serialize it to a string
CONTENT_JSON=$(jq -n \
                --arg message "${MESSAGE}" \
                '{text: $message}')

# Use jq to create the final payload, including the serialized JSON content
PAYLOAD=$(jq -n \
                --arg content "$CONTENT_JSON" \
                --arg receive_id "oc_b383b6c4655fb12e645f413aec3cac1a" \
                '{content: $content, msg_type: "text", receive_id: $receive_id}')

# Send the message using the constructed payload
curl -i -X POST https://open.larksuite.com/open-apis/im/v1/messages?receive_id_type=chat_id \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer ${TOKEN}" \
     -d "$PAYLOAD"
