name: snapbox Api
on:
  push:
    branches:
      - staging
env:
  GITLAB_REGISTRY_URL: registry.gitlab.com/vinaco/ecr/snapbox/snapbox-api

jobs:
  build:
    name: Build and push docker image to registry
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v4
      - name: Cache npm dependencies
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: npm-${{ hashFiles('package-lock.json') }}
          restore-keys: npm-

      # Add Docker layer caching
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - uses: ./.github/build-action
        with:
          GITLAB_REGISTRY_URL: ${{ env.GITLAB_REGISTRY_URL }}
          GITLAB_USERNAME: ${{ secrets.GITLAB_USERNAME }}
          GITLAB_PASSWORD: ${{ secrets.GITLAB_PASSWORD }}
  deploy:
    name: Deploy the image to server
    needs: build
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v4
      # Deploy Docker image to server
      - name: Deploy to Server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.STAGING_SERVER_IP }}
          username: ${{ secrets.STAGING_SERVER_USERNAME }}
          key: ${{ secrets.STAGING_SSH_PASSPHRASE }}
          script: |
            sudo docker pull ${{ env.GITLAB_REGISTRY_URL }}:${{ github.sha }}
            # Determine current active container with improved detection
            CURRENT_CONTAINER=$(docker ps --filter "name=snapbox-api-" --format "{{.Names}}" | head -n 1)

            if [ "$CURRENT_CONTAINER" = "snapbox-api-blue" ]; then
              NEW_CONTAINER="snapbox-api-green"
              PORT=30044
            else
              NEW_CONTAINER="snapbox-api-blue"
              PORT=30045
            fi

            echo "Current container: $CURRENT_CONTAINER"
            echo "New container: $NEW_CONTAINER"
            echo "Port: $PORT"

            # Stop and remove container if it already exists but is not running
            if docker ps -a --filter "name=$NEW_CONTAINER" --format "{{.Names}}" | grep -q "$NEW_CONTAINER"; then
              echo "Container $NEW_CONTAINER exists, removing it first"
              sudo docker rm -f $NEW_CONTAINER || true
            fi

            # Start new container
            sudo docker run -d \
              --name $NEW_CONTAINER \
              --publish $PORT:3000 \
              --env-file /mnt/nfs/vinaco/snapbox-api/.env \
              --volume /mnt/nfs/vinaco/snapbox-api/uploads:/usr/src/app/public/uploads \
              --volume /mnt/nfs/vinaco/snapbox-api/firestore-service.json:/usr/src/app/firestore-key.json \
              --volume /mnt/nfs/vinaco/snapbox-api/gcp-service.json:/usr/src/app/gcp-service.json \
              --volume /mnt/nfs/vinaco/snapbox-api/tmp:/tmp \
              --volume /etc/nginx/ssl/snapbox.cert:/usr/src/app/snapbox.cert \
              --volume /etc/nginx/ssl/snapbox.key:/usr/src/app/snapbox.key \
              ${{ env.GITLAB_REGISTRY_URL }}:${{ github.sha }}

            # Health check with retry logic
            echo "Waiting for application to start up..."
            MAX_RETRIES=10
            RETRY_INTERVAL=15
            RETRY_COUNT=0
            HEALTH_CHECK_PASSED=false

            while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
              echo "Health check attempt $(($RETRY_COUNT + 1))/$MAX_RETRIES..."
              if curl -s -f http://0.0.0.0:$PORT/health | grep -q "status.*ok"; then
                HEALTH_CHECK_PASSED=true
                echo "Health check passed!"
                break
              fi
              RETRY_COUNT=$(($RETRY_COUNT + 1))
              if [ $RETRY_COUNT -lt $MAX_RETRIES ]; then
                echo "Health check failed, retrying in $RETRY_INTERVAL seconds..."
                sleep $RETRY_INTERVAL
              fi
            done

            if [ "$HEALTH_CHECK_PASSED" = true ]; then
              # Check if nginx config file exists
              if [ -f "/etc/nginx/sites-enabled/snapbox-api.nginx.conf" ]; then
                echo "Updating Nginx configuration..."
                # Update nginx config to point to new container
                sudo sed -i "s/127.0.0.1:[0-9]\+/127.0.0.1:$PORT/" /etc/nginx/sites-enabled/snapbox-api.nginx.conf
                sudo nginx -s reload
              else
                echo "Warning: Nginx config file not found at /etc/nginx/sites-enabled/snapbox-api.nginx.conf"
                echo "Please check the path and create the file if needed."
                # Continue deployment even if nginx config is missing
              fi

              # Remove old container
              if [ ! -z "$CURRENT_CONTAINER" ]; then
                echo "Removing old container: $CURRENT_CONTAINER"
                sudo docker rm -f $CURRENT_CONTAINER
              fi
              echo "Deployment successful"
            else
              # Rollback if health check fails
              echo "Health check failed after $MAX_RETRIES attempts. Rolling back..."
              sudo docker logs $NEW_CONTAINER
              sudo docker rm -f $NEW_CONTAINER
              echo "Deployment failed - health check did not pass"
              exit 1
            fi
  clean:
    name: Clean the builded image on runner
    needs: deploy
    runs-on: self-hosted
    steps:
      - uses: actions/checkout@v4
      # Clean server backend
      - name: Clean server
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.STAGING_SERVER_IP }}
          username: ${{ secrets.STAGING_SERVER_USERNAME }}
          key: ${{ secrets.STAGING_SSH_PASSPHRASE }}
          script: |
            echo "y" | sudo docker system prune -a 2> /dev/null || true
      - uses: ./.github/clean-action
