# Build stage
FROM node:20-alpine AS builder

# Thêm ARG và ENV để quản lý môi trường
ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}
ENV PATH=/usr/src/app/node_modules/.bin:$PATH

RUN apk add --no-cache python3 make g++ bash

WORKDIR /usr/src/app

COPY package.json package-lock.json ./
COPY public/default-frame-assets ./public/default-frame-assets

RUN npm ci

COPY . .

RUN npm run build

# Tăng timeout cho npm
RUN npm config set fetch-retry-maxtimeout 600000 -g

# Production stage
FROM node:20-alpine

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

WORKDIR /usr/src/app

COPY --from=builder /usr/src/app/dist ./dist
COPY --from=builder /usr/src/app/node_modules ./node_modules
COPY --from=builder /usr/src/app/public ./public

EXPOSE 3000

CMD ["node", "dist/main"]
