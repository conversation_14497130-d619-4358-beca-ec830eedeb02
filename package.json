{"name": "snapbox-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migrate:generate": "ts-node ./node_modules/typeorm/cli.js migration:generate -d ./src/configs/typeorm.config.ts", "migrate:create": "ts-node ./node_modules/typeorm/cli.js migration:create", "migrate:run": "ts-node ./node_modules/typeorm/cli.js migration:run -d ./src/configs/typeorm.config.ts", "migrate:revert": "ts-node ./node_modules/typeorm/cli.js migration:revert -d ./src/configs/typeorm.config.ts", "migrate:show": "ts-node ./node_modules/typeorm/cli.js migration:show -d ./src/configs/typeorm.config.ts", "seed": "npm run build && ts-node dist/seeds/seed.js"}, "dependencies": {"@google-cloud/storage": "^7.14.0", "@nestjs-modules/ioredis": "^2.0.2", "@nestjs/apollo": "^12.2.1", "@nestjs/bull": "^11.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/graphql": "^12.2.1", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^6.0.0", "@nestjs/typeorm": "^10.0.2", "@types/graphql-upload": "^8.0.12", "adm-zip": "^0.5.16", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bull": "^4.16.5", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto-js": "^4.2.0", "csv-parser": "^3.1.0", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "exceljs": "^4.4.0", "firebase-admin": "^13.0.2", "graphql": "^16.9.0", "graphql-upload": "^13.0.0", "ioredis": "^5.4.1", "lodash": "^4.17.21", "log4js": "^6.9.1", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "morgan": "^1.10.0", "mysql2": "^3.11.4", "passport": "^0.7.0", "qs": "^6.13.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "typeorm": "^0.3.20", "typeorm-extension": "^3.6.3", "uuid": "^11.0.5", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0", "@nestjs/cli": "^10.0.0"}, "devDependencies": {"@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^5.0.0", "@types/jest": "^29.5.2", "@types/node": "^20.17.50", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "5.7.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}