import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAngleToFrameItem1743438735122 implements MigrationInterface {
  name = 'AddAngleToFrameItem1743438735122';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`frame_item\` ADD \`angle\` float NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`frame_item\` DROP COLUMN \`angle\``);
  }
}
