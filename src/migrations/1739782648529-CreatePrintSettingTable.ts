import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePrintSettingTable1739782648529
  implements MigrationInterface
{
  name = 'CreatePrintSettingTable1739782648529';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
            CREATE TABLE \`print_setting\` (
                \`id\` varchar(36) NOT NULL,
                \`adjustColor\` tinyint NULL COMMENT '0: None, 1: Handed by <PERSON>, 2: ICM, 3: ICM & Driver',
                \`printRetry\` tinyint NULL COMMENT '0: Off, 1: On',
                \`border\` tinyint NULL COMMENT '0: Off, 1: On',
                \`sharpness\` tinyint NULL COMMENT '0: Blur more (-2) to 10: Hard (+8)',
                \`overcoatFinish\` tinyint NULL COMMENT '0: Glossy, 1: Matte',
                \`printerQuality\` int NULL,
                \`yResolution\` int NULL,
                \`adjGammaR\` int NULL,
                \`adjGammaG\` int NULL,
                \`adjGammaB\` int NULL,
                \`adjBrightnessR\` int NULL,
                \`adjBrightnessG\` int NULL,
                \`adjBrightnessB\` int NULL,
                \`adjContrastR\` int NULL,
                \`adjContrastG\` int NULL,
                \`adjContrastB\` int NULL,
								\`clientId\` varchar(255) NOT NULL,
								\`machineIds\` varchar(10000) NULL,
								\`type\` varchar(255) NOT NULL,
                \`adjChroma\` int NULL,
                \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
                \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
                PRIMARY KEY (\`id\`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`print_setting\``);
  }
}
