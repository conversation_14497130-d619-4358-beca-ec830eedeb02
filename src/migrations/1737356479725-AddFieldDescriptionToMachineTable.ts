import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFieldDescriptionToMachineTable1737356479725
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    const table = await queryRunner.getTable('machine');
    if (!table?.findColumnByName('totalPaper')) {
      await queryRunner.query(
        'ALTER TABLE `machine` ADD `totalPaper` int NULL',
      );
    }

    if (!table?.findColumnByName('hasPayment')) {
      await queryRunner.query(
        'ALTER TABLE `machine` ADD `hasPayment` tinyint NULL',
      );
    }

    if (!table?.findColumnByName('description')) {
      await queryRunner.query(
        'ALTER TABLE `machine` ADD `description` varchar(255) NULL',
      );
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query('ALTER TABLE `machine` DROP COLUMN `description`');
    await queryRunner.query('ALTER TABLE `machine` DROP COLUMN `hasPayment`');
    await queryRunner.query('ALTER TABLE `machine` DROP COLUMN `totalPaper`');
  }
}
