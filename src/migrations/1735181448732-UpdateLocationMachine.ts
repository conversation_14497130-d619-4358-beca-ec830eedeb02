import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateLocationMachine1735181448732 implements MigrationInterface {
    name = 'UpdateLocationMachine1735181448732'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine\` CHANGE \`location\` \`location\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine\` CHANGE \`location\` \`location\` varchar(255) NOT NULL`);
    }

}
