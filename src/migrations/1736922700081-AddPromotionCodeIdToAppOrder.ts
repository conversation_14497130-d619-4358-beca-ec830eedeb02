import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPromotionCodeIdToAppOrder1736922700081
  implements MigrationInterface
{
  name = 'AddPromotionCodeIdToAppOrder1736922700081';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` ADD \`promotionCodeId\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` DROP COLUMN \`promotionCodeId\``,
    );
  }
}
