import { MigrationInterface, QueryRunner } from "typeorm";

export class AddSecondaryTextColor21739156729308 implements MigrationInterface {
    name = 'AddSecondaryTextColor21739156729308'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`appearance_setting\` ADD \`secondary_text_color_2\` varchar(7) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`appearance_setting\` DROP COLUMN \`secondary_text_color_2\``);
    }

}
