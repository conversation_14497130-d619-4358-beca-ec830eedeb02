import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDateColorToFrame1740067222204 implements MigrationInterface {
  name = 'AddDateColorToFrame1740067222204';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`frame\` ADD \`dateColor\` varchar(20) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`frame\` DROP COLUMN \`dateColor\``);
  }
}
