import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddExpiryDateToMachine1734315933056 implements MigrationInterface {
  name = 'AddExpiryDateToMachine1734315933056';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` ADD \`expiryDate\` varchar(255) NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` DROP COLUMN \`expiryDate\``,
    );
  }
}
