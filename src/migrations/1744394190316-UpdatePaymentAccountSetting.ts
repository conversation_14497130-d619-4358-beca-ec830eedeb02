import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdatePaymentAccountSetting1744394190316
  implements MigrationInterface
{
  name = 'UpdatePaymentAccountSetting1744394190316';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` ADD \`type\` VARCHAR(255) NOT NULL DEFAULT 'PAYOS'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` CHANGE \`apiId\` \`apiId\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` CHANGE \`apiKey\` \`apiKey\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` CHANGE \`checksum\` \`checksum\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` ADD \`mBankId\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` ADD \`bankOwnerName\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` ADD \`bankAccountNumber\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` DROP COLUMN \`type\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` CHANGE \`apiId\` \`apiId\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` CHANGE \`apiKey\` \`apiKey\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` CHANGE \`checksum\` \`checksum\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` DROP COLUMN \`mBankId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` DROP COLUMN \`bankOwnerName\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` DROP COLUMN \`bankAccountNumber\``,
    );
  }
}
