import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateLayoutItem1735783410849 implements MigrationInterface {
    name = 'UpdateLayoutItem1735783410849'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`layout_item\` CHANGE \`position\` \`position\` int NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`layout_item\` CHANGE \`position\` \`position\` int NOT NULL`);
    }

}
