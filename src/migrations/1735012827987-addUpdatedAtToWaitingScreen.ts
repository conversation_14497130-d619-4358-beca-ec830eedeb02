import { MigrationInterface, QueryRunner } from "typeorm";

export class AddUpdatedAtToWaitingScreen1735012827987 implements MigrationInterface {
    name = 'AddUpdatedAtToWaitingScreen1735012827987'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`waiting_screen\` ADD \`updatedAt\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`waiting_screen\` DROP COLUMN \`updatedAt\``);
    }

}
