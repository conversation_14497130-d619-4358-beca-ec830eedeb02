import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdatePromotionCode1735541924971 implements MigrationInterface {
    name = 'UpdatePromotionCode1735541924971'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_8deb5fd6d198679071eb1ea21b\` ON \`promotion_code\``);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_8deb5fd6d198679071eb1ea21b\` ON \`promotion_code\` (\`code\`)`);
    }

}
