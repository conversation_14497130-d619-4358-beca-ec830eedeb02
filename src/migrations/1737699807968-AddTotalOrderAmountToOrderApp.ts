import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTotalOrderAmountToOrderApp1737699807968
  implements MigrationInterface
{
  name = 'AddTotalOrderAmountToOrderApp1737699807968';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` ADD \`totalOrderNumber\` decimal(10,2) NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` DROP COLUMN \`totalOrderNumber\``,
    );
  }
}
