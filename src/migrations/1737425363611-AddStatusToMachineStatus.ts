import { MigrationInterface, QueryRunner } from "typeorm";

export class AddStatusToMachineStatus1737425363611 implements MigrationInterface {
    name = 'AddStatusToMachineStatus1737425363611'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine_status\` ADD \`status\` varchar(255) NOT NULL DEFAULT 'WAITING'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine_status\` DROP COLUMN \`status\``);
    }

}
