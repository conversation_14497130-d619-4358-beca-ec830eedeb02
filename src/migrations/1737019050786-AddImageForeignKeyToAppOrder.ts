import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddImageForeignKeyToAppOrder1737019050786
  implements MigrationInterface
{
  name = 'AddImageForeignKeyToAppOrder1737019050786';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD CONSTRAINT \`FK_720f456b2b2aeb13b78c6abf0cf\` FOREIGN KEY (\`orderId\`) REFERENCES \`app_order\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP FOREIGN KEY \`FK_720f456b2b2aeb13b78c6abf0cf\``,
    );
  }
}
