import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTablePromotions1735055356852 implements MigrationInterface {
    name = 'CreateTablePromotions1735055356852'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`promotion\` (\`id\` varchar(36) NOT NULL, \`clientId\` varchar(255) NOT NULL, \`name\` varchar(255) NOT NULL, \`machineIds\` varchar(255) NULL, \`startDate\` varchar(255) NOT NULL, \`endDate\` varchar(255) NOT NULL, \`usageLimitPerCode\` int NOT NULL DEFAULT '1', \`maxPromotionCodes\` int NOT NULL DEFAULT '1', \`promotionCode\` varchar(255) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`promotion_code\` (\`id\` varchar(36) NOT NULL, \`promotionId\` varchar(255) NOT NULL, \`code\` varchar(8) NOT NULL, \`isUsed\` tinyint NOT NULL DEFAULT 0, \`numberUsed\` int NOT NULL DEFAULT '0', \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), UNIQUE INDEX \`IDX_8deb5fd6d198679071eb1ea21b\` (\`code\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`promotion_code\` ADD CONSTRAINT \`FK_ba9b13f8df94bcbb47661b1c75f\` FOREIGN KEY (\`promotionId\`) REFERENCES \`promotion\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`promotion_code\` DROP FOREIGN KEY \`FK_ba9b13f8df94bcbb47661b1c75f\``);
        await queryRunner.query(`DROP INDEX \`IDX_8deb5fd6d198679071eb1ea21b\` ON \`promotion_code\``);
        await queryRunner.query(`DROP TABLE \`promotion_code\``);
        await queryRunner.query(`DROP TABLE \`promotion\``);
    }

}
