import { MigrationInterface, QueryRunner } from "typeorm";

export class AddIcmMethodToPrintSetting1739783608634 implements MigrationInterface {
    name = 'AddIcmMethodToPrintSetting1739783608634'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`print_setting\` ADD \`icmMethod\` int NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`print_setting\` DROP COLUMN \`icmMethod\``);
    }

}
