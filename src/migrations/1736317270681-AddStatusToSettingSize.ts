import { MigrationInterface, QueryRunner } from "typeorm";

export class AddStatusToSettingSize1736317270681 implements MigrationInterface {
    name = 'AddStatusToSettingSize1736317270681'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`setting_size\` ADD \`status\` varchar(255) NULL DEFAULT 'ACTIVE'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`setting_size\` DROP COLUMN \`status\``);
    }

}
