import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateBackgroundImageTable1748000000000
  implements MigrationInterface
{
  name = 'CreateBackgroundImageTable1748000000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`background_image\` (
        \`id\` varchar(36) NOT NULL, 
        \`fileName\` varchar(255) NOT NULL, 
        \`fileUrl\` varchar(500) NOT NULL, 
        \`description\` varchar(100) NULL, 
        \`clientId\` varchar(36) NOT NULL, 
        \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), 
        \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), 
        PRIMARY KEY (\`id\`),
        INDEX \`IDX_background_image_clientId\` (\`clientId\`),
        CONSTRAINT \`FK_background_image_client\` FOREIGN KEY (\`clientId\`) REFERENCES \`client\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION
      ) ENGINE=InnoDB`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`background_image\``);
  }
}
