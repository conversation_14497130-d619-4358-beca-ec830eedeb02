import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateMachineIds1735022311120 implements MigrationInterface {
    name = 'UpdateMachineIds1735022311120'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`waiting_screen\` CHANGE \`machineIds\` \`machineIds\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` CHANGE \`smallSizePrice\` \`smallSizePrice\` int NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` CHANGE \`largeSizePrice\` \`largeSizePrice\` int NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`setting_size\` CHANGE \`largeSizePrice\` \`largeSizePrice\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` CHANGE \`smallSizePrice\` \`smallSizePrice\` int NULL`);
        await queryRunner.query(`ALTER TABLE \`waiting_screen\` CHANGE \`machineIds\` \`machineIds\` varchar(255) NOT NULL`);
    }

}
