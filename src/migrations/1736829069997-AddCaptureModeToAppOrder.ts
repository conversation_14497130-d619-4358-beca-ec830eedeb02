import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCaptureModeToAppOrder1736829069997
  implements MigrationInterface
{
  name = 'AddCaptureModeToAppOrder1736829069997';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` ADD \`captureMode\` enum ('AUTO', 'MANUAL') NOT NULL DEFAULT 'AUTO'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` DROP COLUMN \`captureMode\``,
    );
  }
}
