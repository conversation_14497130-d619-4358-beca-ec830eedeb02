import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateMachineStatus1736959503974 implements MigrationInterface {
    name = 'CreateMachineStatus1736959503974'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`machine_status\` (\`id\` varchar(36) NOT NULL, \`machineId\` varchar(255) NOT NULL, \`isUpdated\` tinyint NOT NULL DEFAULT 0, \`syncDate\` varchar(255) NOT NULL, \`clientId\` varchar(255) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE \`machine_status\``);
    }

}
