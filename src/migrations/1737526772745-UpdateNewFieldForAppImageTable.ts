import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateNewFieldForAppImageTable1737526772745
  implements MigrationInterface
{
  name = 'UpdateNewFieldForAppImageTable1737526772745';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP FOREIGN KEY \`FK_720f456b2b2aeb13b78c6abf0cf\``,
    );

    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP COLUMN \`imageName\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP COLUMN \`imageUrl\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP COLUMN \`videoName\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP COLUMN \`videoUrl\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`fileUrl\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`fileName\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`fileType\` enum ('IMAGE', 'IMAGE_FINAL', 'VIDEO') NOT NULL DEFAULT 'IMAGE'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP COLUMN \`fileType\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP COLUMN \`fileName\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP COLUMN \`fileUrl\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`videoUrl\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`videoName\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`imageUrl\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`imageName\` varchar(255) NOT NULL`,
    );
    
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD CONSTRAINT \`FK_720f456b2b2aeb13b78c6abf0cf\` FOREIGN KEY (\`orderId\`) REFERENCES \`order\` (\`id\`) ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }
}
