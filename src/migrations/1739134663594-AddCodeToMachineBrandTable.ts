import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCodeToMachineBrandTable1739134663594
  implements MigrationInterface
{
  name = 'AddCodeToMachineBrandTable1739134663594';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine_brand\` ADD \`code\` varchar(255) NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine_brand\` DROP COLUMN \`code\``,
    );
  }
}
