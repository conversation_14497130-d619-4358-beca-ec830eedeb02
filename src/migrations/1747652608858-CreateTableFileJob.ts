import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableFileJob1747652608858 implements MigrationInterface {
  name = 'CreateTableFileJob1747652608858';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE \`file_job\` (
        \`id\` varchar(36) NOT NULL,
        \`orderId\` varchar(255) NOT NULL,
        \`jobId\` varchar(255) NOT NULL,
        \`status\` enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED') NOT NULL DEFAULT 'PENDING',
        \`progress\` int NULL,
        \`result\` text NULL,
        \`error\` text NULL,
        \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
        \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
        <PERSON><PERSON><PERSON><PERSON> KEY (\`id\`)
      ) ENGINE=InnoDB
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`file_job\``);
  }
}
