import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableLayout1734413883860 implements MigrationInterface {
    name = 'CreateTableLayout1734413883860'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`layout\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NULL, \`clientId\` varchar(255) NOT NULL, \`machineIds\` varchar(255) NULL, \`status\` varchar(255) NOT NULL DEFAULT 'ACTIVE', \`layoutType\` varchar(255) NOT NULL DEFAULT 'Default', \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE \`layout\``);
    }

}
