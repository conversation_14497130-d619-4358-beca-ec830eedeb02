import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateSettingSize1736305343874 implements MigrationInterface {
    name = 'UpdateSettingSize1736305343874'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`setting_size\` ADD \`smallSizePrice2\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` ADD \`smallSizePrice4\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` ADD \`smallSizePrice6\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` ADD \`smallSizePrice8\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` ADD \`smallSizePrice10\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` ADD \`largeSizePrice2\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` ADD \`largeSizePrice3\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` ADD \`largeSizePrice4\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` ADD \`largeSizePrice5\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` ADD \`largeSizePrice6\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`order\` CHANGE \`machineIds\` \`machineIds\` varchar(10000) NOT NULL`);
        await queryRunner.query(`CREATE UNIQUE INDEX \`IDX_e93b08f6413e6df0d1619f01b3\` ON \`setting_size\` (\`name\`, \`clientId\`)`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX \`IDX_e93b08f6413e6df0d1619f01b3\` ON \`setting_size\``);
        await queryRunner.query(`ALTER TABLE \`order\` CHANGE \`machineIds\` \`machineIds\` varchar(10000) NULL`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` DROP COLUMN \`largeSizePrice6\``);
        await queryRunner.query(`ALTER TABLE \`setting_size\` DROP COLUMN \`largeSizePrice5\``);
        await queryRunner.query(`ALTER TABLE \`setting_size\` DROP COLUMN \`largeSizePrice4\``);
        await queryRunner.query(`ALTER TABLE \`setting_size\` DROP COLUMN \`largeSizePrice3\``);
        await queryRunner.query(`ALTER TABLE \`setting_size\` DROP COLUMN \`largeSizePrice2\``);
        await queryRunner.query(`ALTER TABLE \`setting_size\` DROP COLUMN \`smallSizePrice10\``);
        await queryRunner.query(`ALTER TABLE \`setting_size\` DROP COLUMN \`smallSizePrice8\``);
        await queryRunner.query(`ALTER TABLE \`setting_size\` DROP COLUMN \`smallSizePrice6\``);
        await queryRunner.query(`ALTER TABLE \`setting_size\` DROP COLUMN \`smallSizePrice4\``);
        await queryRunner.query(`ALTER TABLE \`setting_size\` DROP COLUMN \`smallSizePrice2\``);
    }

}
