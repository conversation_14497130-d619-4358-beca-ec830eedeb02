import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPositonToFrameItemTable1738575166189
  implements MigrationInterface
{
  name = 'AddPositonToFrameItemTable1738575166189';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`frame_item\` ADD \`position\` int NOT NULL DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`frame_item\` DROP COLUMN \`position\``,
    );
  }
}
