import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStateToMachineTable1739166971374 implements MigrationInterface {
  name = 'AddStateToMachineTable1739166971374';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` DROP COLUMN \`totalPaper\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`machine\` ADD \`pendingPrints\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`machine\` ADD \`remainingMedia\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`machine\` ADD \`remainingInk\` tinyint NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`machine\` ADD \`state\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`machine\` DROP COLUMN \`state\``);
    await queryRunner.query(
      `ALTER TABLE \`machine\` DROP COLUMN \`remainingInk\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`machine\` DROP COLUMN \`remainingMedia\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`machine\` DROP COLUMN \`pendingPrints\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`machine\` ADD \`totalPaper\` int NULL`,
    );
  }
}
