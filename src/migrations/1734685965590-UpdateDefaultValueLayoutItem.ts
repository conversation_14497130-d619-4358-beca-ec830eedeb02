import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateDefaultValueLayoutItem1734685965590 implements MigrationInterface {
    name = 'UpdateDefaultValueLayoutItem1734685965590'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`frame\` ADD \`clientId\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`frame\` CHANGE \`orientation\` \`orientation\` varchar(255) NOT NULL DEFAULT 'Horizontal'`);
        await queryRunner.query(`ALTER TABLE \`layout_item\` DROP FOREIGN KEY \`FK_b448b7b79bf8980cf5807c7c86f\``);
        await queryRunner.query(`ALTER TABLE \`layout_item\` CHANGE \`imageUrl\` \`imageUrl\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`layout_item\` CHANGE \`topicId\` \`topicId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`layout_item\` ADD CONSTRAINT \`FK_b448b7b79bf8980cf5807c7c86f\` FOREIGN KEY (\`topicId\`) REFERENCES \`topic\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`layout_item\` DROP FOREIGN KEY \`FK_b448b7b79bf8980cf5807c7c86f\``);
        await queryRunner.query(`ALTER TABLE \`layout_item\` CHANGE \`topicId\` \`topicId\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`layout_item\` CHANGE \`imageUrl\` \`imageUrl\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`layout_item\` ADD CONSTRAINT \`FK_b448b7b79bf8980cf5807c7c86f\` FOREIGN KEY (\`topicId\`) REFERENCES \`topic\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`frame\` CHANGE \`orientation\` \`orientation\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`frame\` DROP COLUMN \`clientId\``);
    }

}
