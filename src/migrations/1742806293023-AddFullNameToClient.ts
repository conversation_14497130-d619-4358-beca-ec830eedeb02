import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFullNameToClient1742806293023 implements MigrationInterface {
  name = 'AddFullNameToClient1742806293023';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`client\` ADD \`ownerName\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`client\` DROP COLUMN \`ownerName\``);
  }
}
