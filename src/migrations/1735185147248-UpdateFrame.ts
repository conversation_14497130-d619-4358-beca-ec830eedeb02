import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateFrame1735185147248 implements MigrationInterface {
    name = 'UpdateFrame1735185147248'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`frame_item\` DROP FOREIGN KEY \`FK_bc05af732bb71722be0890edb4c\``);
        await queryRunner.query(`ALTER TABLE \`frame_item\` ADD \`itemId\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`frame_item\` ADD CONSTRAINT \`FK_bc05af732bb71722be0890edb4c\` FOREIGN KEY (\`frameId\`) REFERENCES \`frame\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`frame_item\` DROP FOREIGN KEY \`FK_bc05af732bb71722be0890edb4c\``);
        await queryRunner.query(`ALTER TABLE \`frame_item\` DROP COLUMN \`itemId\``);
        await queryRunner.query(`ALTER TABLE \`frame_item\` ADD CONSTRAINT \`FK_bc05af732bb71722be0890edb4c\` FOREIGN KEY (\`frameId\`) REFERENCES \`frame\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
