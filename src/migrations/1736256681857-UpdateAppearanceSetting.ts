import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateAppearanceSetting1736256681857 implements MigrationInterface {
    name = 'UpdateAppearanceSetting1736256681857'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`appearance_setting\` CHANGE \`logo\` \`logo\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`appearance_setting\` CHANGE \`background\` \`background\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`appearance_setting\` CHANGE \`background\` \`background\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`appearance_setting\` CHANGE \`logo\` \`logo\` varchar(255) NOT NULL`);
    }

}
