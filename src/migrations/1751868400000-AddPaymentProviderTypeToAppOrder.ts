import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPaymentProviderTypeToAppOrder1751868400000
  implements MigrationInterface
{
  name = 'AddPaymentProviderTypeToAppOrder1751868400000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` ADD \`paymentProviderType\` enum ('AUTOBANK', 'PAYOS') NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` DROP COLUMN \`paymentProviderType\``,
    );
  }
}
