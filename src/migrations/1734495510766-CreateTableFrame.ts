import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateTableFrame1734495510766 implements MigrationInterface {
    name = 'CreateTableFrame1734495510766'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`layout_item\` (\`id\` varchar(36) NOT NULL, \`layoutFormatId\` varchar(255) NOT NULL, \`imageUrl\` varchar(255) NOT NULL, \`topicId\` varchar(255) NOT NULL, \`position\` int NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`layout_format\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(50) NOT NULL, \`imageCount\` int NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`layoutId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`)
        await queryRunner.query(`CREATE TABLE \`frame_item\` (\`id\` varchar(36) NOT NULL, \`frameId\` varchar(255) NOT NULL, \`itemType\` varchar(255) NOT NULL, \`itemId\` int NOT NULL, \`x_coordinate\` int NOT NULL, \`parentId\` int NOT NULL, \`y_coordinate\` int NOT NULL, \`width\` int NOT NULL, \`height\` int NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`frame\` (\`id\` varchar(36) NOT NULL, \`description\` varchar(255) NOT NULL, \`imageUrl\` varchar(255) NOT NULL, \`frameSize\` varchar(255) NOT NULL, \`numberImage\` int NOT NULL, \`numberPicture\` int NOT NULL, \`topicId\` varchar(255) NOT NULL, \`orientation\` varchar(255) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`layout_format\` ADD CONSTRAINT \`FK_fa9f163de8fdd92b0c4658ca3e1\` FOREIGN KEY (\`layoutId\`) REFERENCES \`layout\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`layout_item\` ADD CONSTRAINT \`FK_837cff221ff2daf89718a132c40\` FOREIGN KEY (\`layoutFormatId\`) REFERENCES \`layout_format\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`layout_item\` ADD CONSTRAINT \`FK_b448b7b79bf8980cf5807c7c86f\` FOREIGN KEY (\`topicId\`) REFERENCES \`topic\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`frame_item\` ADD CONSTRAINT \`FK_bc05af732bb71722be0890edb4c\` FOREIGN KEY (\`frameId\`) REFERENCES \`frame\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`frame\` ADD CONSTRAINT \`FK_639f04f3f7f6dc8ae3f19ad81d9\` FOREIGN KEY (\`topicId\`) REFERENCES \`topic\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`frame\` DROP FOREIGN KEY \`FK_639f04f3f7f6dc8ae3f19ad81d9\``);
        await queryRunner.query(`ALTER TABLE \`frame_item\` DROP FOREIGN KEY \`FK_bc05af732bb71722be0890edb4c\``);
        await queryRunner.query(`ALTER TABLE \`layout_item\` DROP FOREIGN KEY \`FK_b448b7b79bf8980cf5807c7c86f\``);
        await queryRunner.query(`ALTER TABLE \`layout_item\` DROP FOREIGN KEY \`FK_837cff221ff2daf89718a132c40\``);
        await queryRunner.query(`ALTER TABLE \`layout_format\` DROP FOREIGN KEY \`FK_fa9f163de8fdd92b0c4658ca3e1\``);
        await queryRunner.query(`DROP TABLE \`frame\``);
        await queryRunner.query(`DROP TABLE \`frame_item\``);
        await queryRunner.query(`DROP TABLE \`layout_item\``);
        await queryRunner.query(`DROP TABLE \`layout_format\``);
    }

}
