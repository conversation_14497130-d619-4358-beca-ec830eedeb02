import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddVideoUrlToAppImage1736822578598 implements MigrationInterface {
  name = 'AddVideoUrlToAppImage1736822578598';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`app_image\` DROP COLUMN \`name\``);
    await queryRunner.query(`ALTER TABLE \`app_image\` DROP COLUMN \`url\``);
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`imageName\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`imageUrl\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`videoName\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`videoUrl\` varchar(255) NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP COLUMN \`videoUrl\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP COLUMN \`videoName\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP COLUMN \`imageUrl\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` DROP COLUMN \`imageName\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`url\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_image\` ADD \`name\` varchar(255) NOT NULL`,
    );
  }
}
