import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateForeignKeyMachine1736960777828 implements MigrationInterface {
    name = 'UpdateForeignKeyMachine1736960777828'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine_status\` CHANGE \`syncDate\` \`syncDate\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine_status\` CHANGE \`syncDate\` \`syncDate\` varchar(255) NOT NULL`);
    }

}
