import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddModelKeyToMachineBrand1742227501531
  implements MigrationInterface
{
  name = 'AddModelKeyToMachineBrand1742227501531';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine_brand\` ADD \`model_key\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine_brand\` DROP COLUMN \`model_key\``,
    );
  }
}
