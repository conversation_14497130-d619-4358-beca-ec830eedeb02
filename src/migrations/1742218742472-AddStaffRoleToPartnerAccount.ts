import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStaffRoleToPartnerAccount1742218742472
  implements MigrationInterface
{
  name = 'AddStaffRoleToPartnerAccount1742218742472';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`client_account\` ADD \`role\` varchar(255) DEFAULT 'STAFF'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`client_account\` DROP COLUMN \`role\``,
    );
  }
}
