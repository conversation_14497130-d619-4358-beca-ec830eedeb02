import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddChangeTyperemainingInkOnMachineTable1739172270136
  implements MigrationInterface
{
  name = 'AddChangeTyperemainingInkOnMachineTable1739172270136';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` DROP COLUMN \`remainingInk\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`machine\` ADD \`remainingInk\` int NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` DROP COLUMN \`remainingInk\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`machine\` ADD \`remainingInk\` tinyint NULL`,
    );
  }
}
