import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateWaitingScreen1735612159241 implements MigrationInterface {
    name = 'UpdateWaitingScreen1735612159241'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`waiting_screen_image\` DROP FOREIGN KEY \`FK_e28434a1ea1e3e4fa11f80c06b6\``);
        await queryRunner.query(`ALTER TABLE \`waiting_screen_image\` ADD CONSTRAINT \`FK_e28434a1ea1e3e4fa11f80c06b6\` FOREIGN KEY (\`waitingScreenId\`) REFERENCES \`waiting_screen\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`waiting_screen_image\` DROP FOREIGN KEY \`FK_e28434a1ea1e3e4fa11f80c06b6\``);
        await queryRunner.query(`ALTER TABLE \`waiting_screen_image\` ADD CONSTRAINT \`FK_e28434a1ea1e3e4fa11f80c06b6\` FOREIGN KEY (\`waitingScreenId\`) REFERENCES \`waiting_screen\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
