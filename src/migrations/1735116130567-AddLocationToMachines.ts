import { MigrationInterface, QueryRunner } from "typeorm";

export class AddLocationToMachines1735116130567 implements MigrationInterface {
    name = 'AddLocationToMachines1735116130567'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine\` ADD \`location\` varchar(255) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine\` DROP COLUMN \`location\``);
    }

}
