import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemoveMachineIdForPrintSetting1739935093054
  implements MigrationInterface
{
  name = 'RemoveMachineIdForPrintSetting1739935093054';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`print_setting\` DROP COLUMN \`machineIds\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`print_setting\` ADD \`machineId\` varchar(36) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`print_setting\` ADD \`machineIds\` varchar(10000) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`print_setting\` DROP COLUMN \`machineId\``,
    );
  }
}
