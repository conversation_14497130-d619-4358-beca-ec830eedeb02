import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateMasterBanks1744392345109 implements MigrationInterface {
  name = 'CreateMasterBanks1744392345109';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`m_bank\` (\`id\` varchar(36) NOT NULL, \`code\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`shortName\` varchar(255) NOT NULL, \`logo\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`m_bank\``);
  }
}
