import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePartnerAccount1742217441359 implements MigrationInterface {
  name = 'CreatePartnerAccount1742217441359';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`client_account\` (\`id\` varchar(36) NOT NULL, \`clientId\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`email\` varchar(255) NOT NULL, \`password\` varchar(255) NOT NULL, \`phone\` varchar(255) NOT NULL, \`createdAt\` varchar(255) NOT NULL, \`updatedAt\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`client_account\``);
  }
}
