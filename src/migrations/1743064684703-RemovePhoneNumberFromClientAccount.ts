import { MigrationInterface, QueryRunner } from 'typeorm';

export class RemovePhoneNumberFromClientAccount1743064684703
  implements MigrationInterface
{
  name = 'RemovePhoneNumberFromClientAccount1743064684703';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`client_account\` DROP COLUMN \`phone\``,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`client_account\` ADD \`phone\` varchar(255) NOT NULL`,
    );
  }
}
