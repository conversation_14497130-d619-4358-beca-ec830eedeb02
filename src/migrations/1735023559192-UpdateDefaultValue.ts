import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateDefaultValue1735023559192 implements MigrationInterface {
    name = 'UpdateDefaultValue1735023559192'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`setting_size\` CHANGE \`machineIds\` \`machineIds\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` CHANGE \`smallSizePrice\` \`smallSizePrice\` int NULL DEFAULT '0'`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` CHANGE \`largeSizePrice\` \`largeSizePrice\` int NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`setting_size\` CHANGE \`largeSizePrice\` \`largeSizePrice\` int NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` CHANGE \`smallSizePrice\` \`smallSizePrice\` int NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` CHANGE \`machineIds\` \`machineIds\` varchar(255) NOT NULL`);
    }

}
