import { MigrationInterface, QueryRunner, Table } from 'typeorm';

export class CreateCostReconciliationTable1751868322732
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'cost_reconciliation',
        columns: [
          {
            name: 'id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: '(UUID())',
          },
          {
            name: 'clientId',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'revenue',
            type: 'decimal',
            precision: 10,
            scale: 2,
            isNullable: false,
          },
          {
            name: 'discountAmount',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['PAID', 'UNPAID', 'NOT_RECONCILED'],
            default: "'NOT_RECONCILED'",
          },

          {
            name: 'totalOrders',
            type: 'int',
            default: 0,
            isNullable: false,
          },
          {
            name: 'reconciliationDate',
            type: 'datetime',
            isNullable: false,
            comment: 'Stores first day of the reconciliation month (e.g., 2025-01-01 for January 2025)',
          },
          {
            name: 'createdAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'createdBy',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'updatedBy',
            type: 'varchar',
            length: '255',
            isNullable: true,
          },
          {
            name: 'notes',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'processedOrderIds',
            type: 'longtext',
            isNullable: true,
            comment:
              'JSON array of all orderApp IDs that have been processed and added to this reconciliation',
          },
          {
            name: 'reconciliationCode',
            type: 'varchar',
            length: '50',
            isNullable: false,
            isUnique: true,
            comment:
              'Unique code generated for this cost reconciliation record',
          },
        ],
        indices: [
          {
            name: 'IDX_COST_RECONCILIATION_CLIENT_ID',
            columnNames: ['clientId'],
          },
          {
            name: 'IDX_COST_RECONCILIATION_STATUS',
            columnNames: ['status'],
          },
          {
            name: 'IDX_COST_RECONCILIATION_CLIENT_MONTH',
            columnNames: ['clientId', 'reconciliationDate'],
            isUnique: true,
          },
          {
            name: 'IDX_COST_RECONCILIATION_DATE',
            columnNames: ['reconciliationDate'],
          },
        ],
        foreignKeys: [
          {
            name: 'FK_COST_RECONCILIATION_CLIENT',
            columnNames: ['clientId'],
            referencedTableName: 'client',
            referencedColumnNames: ['id'],
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
          },
        ],
      }),
      true,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('cost_reconciliation');
  }
}
