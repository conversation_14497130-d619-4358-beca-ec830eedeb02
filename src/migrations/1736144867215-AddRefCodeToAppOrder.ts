import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRefCodeToAppOrder1736144867215 implements MigrationInterface {
  name = 'AddRefCodeToAppOrder1736144867215';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` ADD \`refCode\` varchar(255) NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` DROP COLUMN \`refCode\``,
    );
  }
}
