import { MigrationInterface, QueryRunner } from "typeorm";

export class InitialMigration1734196488383 implements MigrationInterface {
    name = 'InitialMigration1734196488383'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`waiting_screen\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NULL, \`machineIds\` varchar(255) NOT NULL, \`type\` varchar(255) NOT NULL, \`createdAt\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`waiting_screen_image\` (\`id\` varchar(36) NOT NULL, \`url\` varchar(255) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`waitingScreenId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`topic\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`extraFee\` float NULL, \`isActive\` tinyint NOT NULL DEFAULT 1, \`clientId\` varchar(255) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`setting_size\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`settingSizeType\` varchar(255) NOT NULL DEFAULT 'Default', \`machineIds\` varchar(255) NOT NULL, \`clientId\` varchar(255) NOT NULL, \`smallSizePrice\` int NOT NULL, \`largeSizePrice\` int NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`auth\` (\`id\` varchar(36) NOT NULL, \`email\` varchar(255) NOT NULL, \`token\` longtext NOT NULL, \`createdAt\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`machine_brand\` (\`id\` varchar(36) NOT NULL, \`brandCode\` varchar(255) NOT NULL, \`name\` varchar(255) NOT NULL, \`description\` varchar(255) NOT NULL, \`supported\` tinyint NOT NULL DEFAULT 0, \`createdAt\` varchar(255) NOT NULL, \`createdBy\` varchar(255) NOT NULL, \`updatedAt\` varchar(255) NULL, \`updatedBy\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`client\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`email\` varchar(255) NOT NULL, \`password\` varchar(255) NOT NULL, \`phone\` varchar(255) NOT NULL, \`province\` varchar(255) NOT NULL, \`address\` varchar(255) NOT NULL, \`createdAt\` varchar(255) NOT NULL, \`createdBy\` varchar(255) NOT NULL, \`updatedAt\` varchar(255) NULL, \`updatedBy\` varchar(255) NULL, \`totalOrders\` int NOT NULL DEFAULT '0', \`totalMachines\` int NOT NULL DEFAULT '0', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`machine\` (\`id\` varchar(36) NOT NULL, \`price\` int NOT NULL, \`machineId\` varchar(255) NOT NULL, \`machineCode\` varchar(255) NOT NULL, \`machinePin\` varchar(255) NOT NULL, \`userId\` varchar(255) NULL, \`orderId\` varchar(255) NULL, \`rentalDate\` varchar(255) NULL, \`renewalDate\` varchar(255) NULL, \`havePayScreen\` tinyint NULL, \`status\` varchar(255) NULL, \`createdAt\` varchar(255) NOT NULL, \`createdBy\` varchar(255) NOT NULL, \`updatedAt\` varchar(255) NULL, \`updatedBy\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`appearance_setting\` (\`id\` varchar(36) NOT NULL, \`logo\` varchar(255) NOT NULL, \`background\` varchar(255) NOT NULL, \`primary_color\` varchar(7) NOT NULL, \`secondary_color\` varchar(7) NOT NULL, \`background_color\` varchar(7) NOT NULL, \`primary_text_color\` varchar(7) NOT NULL, \`secondary_text_color\` varchar(7) NOT NULL, \`clientId\` varchar(255) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`order\` (\`id\` varchar(36) NOT NULL, \`orderId\` varchar(255) NOT NULL, \`userId\` varchar(255) NOT NULL, \`paymentIds\` int NULL, \`machineIds\` varchar(255) NOT NULL, \`discount\` int NOT NULL DEFAULT '0', \`totalAmount\` int NOT NULL DEFAULT '0', \`advancePayment\` int NOT NULL DEFAULT '0', \`appointedDate\` varchar(255) NOT NULL, \`status\` varchar(255) NOT NULL DEFAULT 'IN_PROGRESS', \`editContent\` longtext NULL, \`createdAt\` varchar(255) NOT NULL, \`createdBy\` varchar(255) NOT NULL, \`updatedAt\` varchar(255) NULL, \`updatedBy\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`CREATE TABLE \`user\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL, \`email\` varchar(255) NOT NULL, \`password\` varchar(255) NOT NULL, \`role\` varchar(255) NOT NULL, \`createdAt\` varchar(255) NOT NULL, \`updatedAt\` varchar(255) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`waiting_screen_image\` ADD CONSTRAINT \`FK_e28434a1ea1e3e4fa11f80c06b6\` FOREIGN KEY (\`waitingScreenId\`) REFERENCES \`waiting_screen\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`waiting_screen_image\` DROP FOREIGN KEY \`FK_e28434a1ea1e3e4fa11f80c06b6\``);
        await queryRunner.query(`DROP TABLE \`user\``);
        await queryRunner.query(`DROP TABLE \`order\``);
        await queryRunner.query(`DROP TABLE \`appearance_setting\``);
        await queryRunner.query(`DROP TABLE \`machine\``);
        await queryRunner.query(`DROP TABLE \`client\``);
        await queryRunner.query(`DROP TABLE \`machine_brand\``);
        await queryRunner.query(`DROP TABLE \`auth\``);
        await queryRunner.query(`DROP TABLE \`setting_size\``);
        await queryRunner.query(`DROP TABLE \`topic\``);
        await queryRunner.query(`DROP TABLE \`waiting_screen_image\``);
        await queryRunner.query(`DROP TABLE \`waiting_screen\``);
    }

}
