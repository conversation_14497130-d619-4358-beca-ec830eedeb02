import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDenominationsToAppOrder1747860123456
  implements MigrationInterface
{
  name = 'AddDenominationsToAppOrder1747860123456';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` ADD \`denominations\` json NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` DROP COLUMN \`denominations\``,
    );
  }
}
