import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddNameToPaymentAccountSettingTable1739963490714
  implements MigrationInterface
{
  name = 'AddNameToPaymentAccountSettingTable1739963490714';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` ADD \`name\` varchar(255) NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`payment_account_setting\` DROP COLUMN \`name\``,
    );
  }
}
