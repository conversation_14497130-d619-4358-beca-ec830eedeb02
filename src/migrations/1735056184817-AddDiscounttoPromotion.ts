import { MigrationInterface, QueryRunner } from "typeorm";

export class AddDiscounttoPromotion1735056184817 implements MigrationInterface {
    name = 'AddDiscounttoPromotion1735056184817'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`promotion\` ADD \`discountValue\` int NOT NULL DEFAULT '0'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`promotion\` DROP COLUMN \`discountValue\``);
    }

}
