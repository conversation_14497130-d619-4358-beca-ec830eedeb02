import { MigrationInterface, QueryRunner } from 'typeorm';

export class AllowDescriptionOnFrameIsNull1739787252894
  implements MigrationInterface
{
  name = 'AllowDescriptionOnFrameIsNull1739787252894';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`frame\` CHANGE \`description\` \`description\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`frame\` CHANGE \`description\` \`description\` varchar(255) NOT NULL`,
    );
  }
}
