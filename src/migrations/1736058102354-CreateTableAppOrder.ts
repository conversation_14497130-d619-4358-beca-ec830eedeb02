import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTableAppOrder1736058102354 implements MigrationInterface {
  name = 'CreateTableAppOrder1736058102354';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`app_order\` (\`id\` varchar(36) NOT NULL, \`orderCode\` varchar(255) NOT NULL, \`amount\` decimal(10,2) NOT NULL, \`receivedAmount\` decimal(10,2) NOT NULL DEFAULT '0.00', \`description\` text NULL, \`status\` enum ('PENDING', 'ACCEPTED', 'REJECTED', 'DELIVERED') NOT NULL DEFAULT 'PENDING', \`paymentMethod\` enum ('ONLINE', 'OFFLINE') NOT NULL DEFAULT 'OFFLINE', \`machineId\` varchar(255) NOT NULL, \`clientId\` varchar(255) NOT NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), UNIQUE INDEX \`IDX_7d7f83bf8e36b8a3f509a8ea00\` (\`orderCode\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_7d7f83bf8e36b8a3f509a8ea00\` ON \`app_order\``,
    );
    await queryRunner.query(`DROP TABLE \`app_order\``);
  }
}
