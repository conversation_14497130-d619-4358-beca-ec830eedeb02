import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSoftDeleteToFrame1741102763747 implements MigrationInterface {
  name = 'AddSoftDeleteToFrame1741102763747';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`frame\` ADD \`deletedAt\` datetime NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`frame\` DROP COLUMN \`deletedAt\``);
  }
}
