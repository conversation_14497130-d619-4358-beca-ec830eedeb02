import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeMachineDefaultStatus1740498969409
  implements MigrationInterface
{
  name = 'ChangeMachineDefaultStatus1740498969409';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` ALTER COLUMN \`status\` SET DEFAULT 'ACTIVE'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` ALTER COLUMN \`status\` SET DEFAULT 'INACTIVE'`,
    );
  }
}
