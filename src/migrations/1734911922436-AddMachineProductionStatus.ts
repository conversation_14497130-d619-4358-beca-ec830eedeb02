import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMachineProductionStatus1734911922436
  implements MigrationInterface
{
  name = 'AddMachineProductionStatus1734911922436';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` ADD \`productionStatus\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`machine\` CHANGE \`status\` \`status\` varchar(255) NULL DEFAULT 'INACTIVE'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` CHANGE \`status\` \`status\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`machine\` DROP COLUMN \`productionStatus\``,
    );
  }
}
