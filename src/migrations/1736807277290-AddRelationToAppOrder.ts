import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRelationToAppOrder1736807277290 implements MigrationInterface {
  name = 'AddRelationToAppOrder1736807277290';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` ADD \`imageNumber\` int NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_order\` ADD \`settingSizeId\` varchar(255) NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_order\` ADD \`promotionId\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_order\` ADD \`topicId\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_order\` ADD \`frameId\` varchar(255) NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_order\` CHANGE \`machineId\` \`machineId\` varchar(255) NOT NULL DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_order\` CHANGE \`clientId\` \`clientId\` varchar(255) NOT NULL DEFAULT ''`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`app_order\` CHANGE \`clientId\` \`clientId\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_order\` CHANGE \`machineId\` \`machineId\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_order\` DROP COLUMN \`frameId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_order\` DROP COLUMN \`topicId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_order\` DROP COLUMN \`promotionId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_order\` DROP COLUMN \`settingSizeId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`app_order\` DROP COLUMN \`imageNumber\``,
    );
  }
}
