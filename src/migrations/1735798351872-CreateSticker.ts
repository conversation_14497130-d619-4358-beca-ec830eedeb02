import { MigrationInterface, QueryRunner } from "typeorm";

export class CreateSticker1735798351872 implements MigrationInterface {
    name = 'CreateSticker1735798351872'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE \`sticker\` (\`id\` varchar(36) NOT NULL, \`clientId\` varchar(255) NOT NULL, \`image\` varchar(255) NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` MODIFY \`machineIds\` varchar(10000) NULL`);
        await queryRunner.query(`ALTER TABLE \`waiting_screen\` MODIFY \`machineIds\` varchar(10000) NULL`);
        await queryRunner.query(`ALTER TABLE \`layout\` MODIFY \`machineIds\` varchar(10000) NULL`);
        await queryRunner.query(`ALTER TABLE \`promotion\` MODIFY \`machineIds\` varchar(10000) NULL`);
        await queryRunner.query(`ALTER TABLE \`order\` MODIFY \`machineIds\` varchar(10000) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`order\` MODIFY \`machineIds\` varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`promotion\` MODIFY \`machineIds\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`layout\` MODIFY \`machineIds\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`waiting_screen\` MODIFY \`machineIds\` varchar(255) NULL`);
        await queryRunner.query(`ALTER TABLE \`setting_size\` MODIFY \`machineIds\` varchar(255) NULL`);
        await queryRunner.query(`DROP TABLE \`sticker\``);
    }
}
