import { MigrationInterface, QueryRunner } from "typeorm";

export class AddStatusLayoutFormat1736353157009 implements MigrationInterface {
    name = 'AddStatusLayoutFormat1736353157009'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`layout_format\` ADD \`status\` varchar(255) NOT NULL DEFAULT 'INACTIVE'`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`layout_format\` DROP COLUMN \`status\``);
    }

}
