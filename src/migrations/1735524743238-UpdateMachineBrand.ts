import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateMachineBrand1735524743238 implements MigrationInterface {
    name = 'UpdateMachineBrand1735524743238'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine\` ADD \`machineBrandId\` varchar(36) NULL`);
        await queryRunner.query(`ALTER TABLE \`machine\` ADD CONSTRAINT \`FK_ab01ccf59d4a1ef1e51c141f314\` FOREIGN KEY (\`machineBrandId\`) REFERENCES \`machine_brand\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine\` DROP FOREIGN KEY \`FK_ab01ccf59d4a1ef1e51c141f314\``);
        await queryRunner.query(`ALTER TABLE \`machine\` DROP COLUMN \`machineBrandId\``);
    }

}
