import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateForeignKeyMachineStatus1737341412092 implements MigrationInterface {
    name = 'UpdateForeignKeyMachineStatus1737341412092'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine_status\` ADD CONSTRAINT \`FK_f7cfeab12660f5ef98847bc2adf\` FOREIGN KEY (\`machineId\`) REFERENCES \`machine\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine_status\` DROP INDEX \`IDX_f7cfeab12660f5ef98847bc2ad\``);
    }

}
