import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFilePathToFileJob1747859891760 implements MigrationInterface {
  name = 'AddFilePathToFileJob1747859891760';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`file_job\` ADD \`filePath\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`file_job\` DROP COLUMN \`filePath\``,
    );
  }
}
