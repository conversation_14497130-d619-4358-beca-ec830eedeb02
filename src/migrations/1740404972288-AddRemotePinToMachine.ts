import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRemotePinToMachine1740404972288 implements MigrationInterface {
  name = 'AddRemotePinToMachine1740404972288';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` ADD \`remotePin\` varchar(20) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` DROP COLUMN \`remotePin\``,
    );
  }
}
