import { MigrationInterface, QueryRunner } from "typeorm";

export class AddLastPingAtToMachine1736394004970 implements MigrationInterface {
    name = 'AddLastPingAtToMachine1736394004970'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine\` ADD \`lastPingAt\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`machine\` DROP COLUMN \`lastPingAt\``);
    }

}
