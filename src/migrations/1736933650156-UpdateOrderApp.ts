import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateOrderApp1736933650156 implements MigrationInterface {
    name = 'UpdateOrderApp1736933650156'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`app_order\` ADD CONSTRAINT \`FK_5784d722b187069378859ac514f\` FOREIGN KEY (\`promotionCodeId\`) REFERENCES \`promotion_code\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`app_order\` ADD CONSTRAINT \`FK_902a57be2fcf2b9ec40c04e97d6\` FOREIGN KEY (\`frameId\`) REFERENCES \`frame\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`app_order\` ADD CONSTRAINT \`FK_e64929a2563db81418b05ac20d8\` FOREIGN KEY (\`topicId\`) REFERENCES \`topic\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`app_order\` ADD CONSTRAINT \`FK_80083625c8df1a78d52b6d83e79\` FOREIGN KEY (\`settingSizeId\`) REFERENCES \`setting_size\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`app_order\` ADD CONSTRAINT \`FK_90c621ae8743be12527c5df66a1\` FOREIGN KEY (\`machineId\`) REFERENCES \`machine\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`app_order\` DROP FOREIGN KEY \`FK_90c621ae8743be12527c5df66a1\``);
        await queryRunner.query(`ALTER TABLE \`app_order\` DROP FOREIGN KEY \`FK_80083625c8df1a78d52b6d83e79\``);
        await queryRunner.query(`ALTER TABLE \`app_order\` DROP FOREIGN KEY \`FK_e64929a2563db81418b05ac20d8\``);
        await queryRunner.query(`ALTER TABLE \`app_order\` DROP FOREIGN KEY \`FK_902a57be2fcf2b9ec40c04e97d6\``);
        await queryRunner.query(`ALTER TABLE \`app_order\` DROP FOREIGN KEY \`FK_5784d722b187069378859ac514f\``);
    }

}
