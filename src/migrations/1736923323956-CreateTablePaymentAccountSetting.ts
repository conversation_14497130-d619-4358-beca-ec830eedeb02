import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTablePaymentAccountSetting1736923323956
  implements MigrationInterface
{
  name = 'CreateTablePaymentAccountSetting1736923323956';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`payment_account_setting\` (\`id\` varchar(36) NOT NULL, \`bankName\` varchar(255) NOT NULL, \`apiId\` varchar(255) NOT NULL, \`apiKey\` varchar(255) NOT NULL, \`checkSum\` varchar(255) NOT NULL, \`isActive\` tinyint NOT NULL DEFAULT 0, \`clientId\` varchar(255) NOT NULL DEFAULT '', \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`payment_account_setting\``);
  }
}
