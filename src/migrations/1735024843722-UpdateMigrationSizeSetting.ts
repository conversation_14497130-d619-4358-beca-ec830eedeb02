import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateMigrationSizeSetting1735024843722 implements MigrationInterface {
    name = 'UpdateMigrationSizeSetting1735024843722'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`setting_size\` CHANGE \`name\` \`name\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`setting_size\` CHANGE \`name\` \`name\` varchar(255) NOT NULL`);
    }

}
