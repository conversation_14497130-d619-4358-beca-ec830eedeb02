import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddModelKeyToMachineBrand1742229565139
  implements MigrationInterface
{
  name = 'AddModelKeyToMachineBrand1742229565139';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine_brand\` ADD \`modelKey\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine_brand\` DROP COLUMN \`modelKey\``,
    );
  }
}
