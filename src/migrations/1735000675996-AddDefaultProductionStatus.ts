import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddDefaultProductionStatus1735000675996
  implements MigrationInterface
{
  name = 'AddDefaultProductionStatus1735000675996';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` CHANGE \`productionStatus\` \`productionStatus\` varchar(255) NULL DEFAULT 'IN_PRODUCTION'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`machine\` CHANGE \`productionStatus\` \`productionStatus\` varchar(255) NULL`,
    );
  }
}
