import { MigrationInterface, QueryRunner } from "typeorm";

export class AddClientIdToWaitingScreen1734938875308 implements MigrationInterface {
    name = 'AddClientIdToWaitingScreen1734938875308'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`waiting_screen\` ADD \`clientId\` varchar(255) NOT NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`waiting_screen\` DROP COLUMN \`clientId\``);
    }

}
