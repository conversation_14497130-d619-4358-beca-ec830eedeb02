import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateFrameItem1735056125238 implements MigrationInterface {
    name = 'UpdateFrameItem1735056125238'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`frame_item\` DROP COLUMN \`itemId\``);
        await queryRunner.query(`ALTER TABLE \`frame_item\` DROP COLUMN \`parentId\``);
        await queryRunner.query(`ALTER TABLE \`frame_item\` ADD \`parentId\` varchar(255) NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`frame_item\` DROP COLUMN \`parentId\``);
        await queryRunner.query(`ALTER TABLE \`frame_item\` ADD \`parentId\` int NOT NULL`);
        await queryRunner.query(`ALTER TABLE \`frame_item\` ADD \`itemId\` int NOT NULL`);
    }

}
