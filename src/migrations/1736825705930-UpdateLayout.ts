import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateLayout1736825705930 implements MigrationInterface {
    name = 'UpdateLayout1736825705930'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`layout_format\` DROP FOREIGN KEY \`FK_fa9f163de8fdd92b0c4658ca3e1\``);
        await queryRunner.query(`ALTER TABLE \`layout_item\` DROP FOREIGN KEY \`FK_837cff221ff2daf89718a132c40\``);
        await queryRunner.query(`ALTER TABLE \`layout_format\` ADD CONSTRAINT \`FK_fa9f163de8fdd92b0c4658ca3e1\` FOREIGN KEY (\`layoutId\`) REFERENCES \`layout\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`layout_item\` ADD CONSTRAINT \`FK_837cff221ff2daf89718a132c40\` FOREIGN KEY (\`layoutFormatId\`) REFERENCES \`layout_format\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE \`layout_item\` DROP FOREIGN KEY \`FK_837cff221ff2daf89718a132c40\``);
        await queryRunner.query(`ALTER TABLE \`layout_format\` DROP FOREIGN KEY \`FK_fa9f163de8fdd92b0c4658ca3e1\``);
        await queryRunner.query(`ALTER TABLE \`layout_item\` ADD CONSTRAINT \`FK_837cff221ff2daf89718a132c40\` FOREIGN KEY (\`layoutFormatId\`) REFERENCES \`layout_format\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE \`layout_format\` ADD CONSTRAINT \`FK_fa9f163de8fdd92b0c4658ca3e1\` FOREIGN KEY (\`layoutId\`) REFERENCES \`layout\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
