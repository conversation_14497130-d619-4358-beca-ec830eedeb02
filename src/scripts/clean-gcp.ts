import { Storage } from '@google-cloud/storage';

const FOLDER_NAME = 'uploads/appImage'; // Ensure it ends with '/'

// Tính ngày threshold là 7 ngày trước thời điểm hiện tại
const THRESHOLD_DATE = new Date();
THRESHOLD_DATE.setDate(THRESHOLD_DATE.getDate() - 5);

// Khởi tạo Storage với credentials từ file
const storage = new Storage({
    keyFilename: './gcp-service.json' // Đường dẫn tới file credentials
});
const bucket = storage.bucket("prd-booth");

async function deleteOldFiles() {
    try {
        console.log(`Started cleanup process at: ${new Date().toISOString()}`);
        console.log(`Threshold date: ${THRESHOLD_DATE.toISOString()}`);
        console.log(`Deleting files in ${FOLDER_NAME}`);
        
        const batchSize = 1000;
        let pageToken: string | undefined;
        let totalProcessed = 0;
        let totalDeleted = 0;
        let hasMore = true;
        let batchNumber = 0;

        while (hasMore) {
            batchNumber++;
            console.log(`\n[Batch ${batchNumber}] Fetching files with pageToken: ${pageToken || 'initial'}`);
            
            const [files, options] = await bucket.getFiles({ 
                prefix: FOLDER_NAME, 
                maxResults: batchSize,
                pageToken: pageToken 
            });

            pageToken = options?.pageToken;
            hasMore = !!pageToken;

            console.log(`[Batch ${batchNumber}] Retrieved ${files.length} files`);
            console.log(`[Batch ${batchNumber}] Next pageToken: ${pageToken || 'none'}`);
            
            if (files.length === 0) {
                console.log('No more files to process');
                hasMore = false;
                break;
            }

            let batchDeleteCount = 0;
            for (const file of files) {
                try {
                    const [metadata] = await file.getMetadata();
                    const createdDate = new Date(metadata.timeCreated);
                    console.log("preparing to check the file was created before threshold date", file.name)

                    if (createdDate < THRESHOLD_DATE) {
                        console.log(`Deleting: ${file.name} (Created: ${metadata.timeCreated})`);
                        await file.delete();
                        batchDeleteCount++;
                        totalDeleted++;
                    }
                } catch (err) {
                    console.error(`Error processing file ${file.name}:`, err);
                }
            }

            totalProcessed += files.length;
            console.log(`[Batch ${batchNumber}] Batch stats:`);
            console.log(`- Files processed in this batch: ${files.length}`);
            console.log(`- Files deleted in this batch: ${batchDeleteCount}`);
            console.log(`- Total files processed: ${totalProcessed}`);
            console.log(`- Total files deleted: ${totalDeleted}`);
            console.log(`- Time: ${new Date().toISOString()}`);
        }

        console.log('\nCleanup Summary:');
        console.log(`- Total batches processed: ${batchNumber}`);
        console.log(`- Total files processed: ${totalProcessed}`);
        console.log(`- Total files deleted: ${totalDeleted}`);
        console.log(`- Completed at: ${new Date().toISOString()}`);
    } catch (error) {
        console.error('Fatal error:', error);
    }
}

deleteOldFiles();