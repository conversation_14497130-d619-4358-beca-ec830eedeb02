/* eslint-disable @typescript-eslint/no-unused-vars */
import { Logger as TypeOrmLogger, QueryRunner } from 'typeorm';
import { Logger } from '@nestjs/common';
import { getCurrentRequestId } from '../middlewares/request-id.middleware';

// ANSI color codes for terminal output
const colors = {
  cyan: '\x1b[36m',
  reset: '\x1b[0m',
};

/**
 * Custom TypeORM logger that integrates with NestJS logger and adds request ID context
 */
export class CustomTypeOrmLogger implements TypeOrmLogger {
  private readonly logger = new Logger('SQL');

  /**
   * Log query
   * @param query - The SQL query string
   * @param parameters - Query parameters (optional)
   * @param _queryRunner - TypeORM QueryRunner instance (unused but required by interface)
   */
  logQuery(query: string, parameters?: any[], _queryRunner?: QueryRunner) {
    // Bỏ qua log SQL trong môi trường production
    if (process.env.NODE_ENV === 'production') {
      return;
    }

    const requestId = getCurrentRequestId();
    const sql = this.buildSqlString(query, parameters);

    // Add cyan color to SQL logs
    this.logger.log(`[reqId:${requestId}] ${colors.cyan}${sql}${colors.reset}`);
  }

  /**
   * Log query error
   * @param error - Error object or string
   * @param query - The SQL query string
   * @param parameters - Query parameters (optional)
   * @param _queryRunner - TypeORM QueryRunner instance (unused but required by interface)
   */
  logQueryError(
    error: string | Error,
    query: string,
    parameters?: any[],
    _queryRunner?: QueryRunner,
  ) {
    const requestId = getCurrentRequestId();
    const sql = this.buildSqlString(query, parameters);

    this.logger.error(
      `[reqId:${requestId}] Query failed: ${colors.cyan}${sql}${colors.reset}`,
      error instanceof Error ? error.stack : error,
    );
  }

  /**
   * Log query that is slow
   * @param time - Execution time in milliseconds
   * @param query - The SQL query string
   * @param parameters - Query parameters (optional)
   * @param _queryRunner - TypeORM QueryRunner instance (unused but required by interface)
   */
  logQuerySlow(
    time: number,
    query: string,
    parameters?: any[],
    _queryRunner?: QueryRunner,
  ) {
    // Trong môi trường production, chỉ log các truy vấn rất chậm (> 2 giây)
    if (process.env.NODE_ENV === 'production' && time < 2000) {
      return;
    }

    const requestId = getCurrentRequestId();
    const sql = this.buildSqlString(query, parameters);

    this.logger.warn(
      `[reqId:${requestId}] Slow query (${time}ms): ${colors.cyan}${sql}${colors.reset}`,
    );
  }

  /**
   * Log schema build process
   * @param message - Schema build message
   * @param _queryRunner - TypeORM QueryRunner instance (unused but required by interface)
   */
  logSchemaBuild(message: string, _queryRunner?: QueryRunner) {
    const requestId = getCurrentRequestId();
    this.logger.log(`[reqId:${requestId}] ${message}`);
  }

  /**
   * Log migration process
   * @param message - Migration message
   * @param _queryRunner - TypeORM QueryRunner instance (unused but required by interface)
   */
  logMigration(message: string, _queryRunner?: QueryRunner) {
    const requestId = getCurrentRequestId();
    this.logger.log(`[reqId:${requestId}] ${message}`);
  }

  /**
   * Log general messages
   * @param level - Log level
   * @param message - Message to log
   * @param _queryRunner - TypeORM QueryRunner instance (unused but required by interface)
   */
  log(
    level: 'log' | 'info' | 'warn',
    message: any,
    _queryRunner?: QueryRunner,
  ) {
    const requestId = getCurrentRequestId();

    switch (level) {
      case 'log':
      case 'info':
        this.logger.log(`[reqId:${requestId}] ${message}`);
        break;
      case 'warn':
        this.logger.warn(`[reqId:${requestId}] ${message}`);
        break;
    }
  }

  /**
   * Build SQL string with parameters for better readability
   */
  private buildSqlString(query: string, parameters?: any[]): string {
    if (!parameters || !parameters.length) {
      return query;
    }

    // Simple parameter replacement for logging
    // This is not for execution, just for logging readability
    let sql = query;
    try {
      // Replace positional parameters (?) with actual values
      if (sql.includes('?') && Array.isArray(parameters)) {
        parameters.forEach((param) => {
          sql = sql.replace('?', this.stringifyParameter(param));
        });
      }
      // Replace named parameters (:name) with actual values
      else if (typeof parameters === 'object') {
        Object.keys(parameters).forEach((key) => {
          const value = parameters[key];
          sql = sql.replace(
            new RegExp(`:${key}\\b`, 'g'),
            this.stringifyParameter(value),
          );
        });
      }
    } catch (_error) {
      // If parameter replacement fails, return original query with parameters as JSON
      return `${query} -- Parameters: ${JSON.stringify(parameters)}`;
    }

    return sql;
  }

  /**
   * Convert parameter to string for SQL logging
   */
  private stringifyParameter(param: any): string {
    if (param === null || param === undefined) {
      return 'NULL';
    } else if (typeof param === 'string') {
      return `'${param.replace(/'/g, "''")}'`; // Escape single quotes
    } else if (param instanceof Date) {
      return `'${param.toISOString()}'`;
    } else if (typeof param === 'object') {
      try {
        return `'${JSON.stringify(param)}'`;
      } catch (_error) {
        return "'[Complex Object]'";
      }
    }
    return String(param);
  }
}
