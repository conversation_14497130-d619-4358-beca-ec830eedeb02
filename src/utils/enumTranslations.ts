import {
  EPaymentMethod,
  EOrderStatus,
  EOrderAppStatus,
  EMachineStatus,
  EProductionStatus,
  EWaitingScreenType,
  ESettingSizeType,
  ELayoutStatus,
  ELayoutType,
  EFrameSize,
  EFrameItemType,
  EFrameOrientation,
} from '../enum';
import { ERoles } from '../enum/role';

// Bản dịch cho các vai trò
export const roleTranslations = {
  [ERoles.ADMIN]: 'Quản trị viên',
  [ERoles.CLIENT]: 'Khách hàng',
  [ERoles.CUSTOMER]: 'Người dùng',
  [ERoles.MOD]: 'Điều hành viên',
};

// Bản dịch cho trạng thái máy
export const machineStatusTranslations = {
  [EMachineStatus.ACTIVE]: 'Hoạt động',
  [EMachineStatus.INACTIVE]: 'Không hoạt động',
  [EMachineStatus.WAITING]: '<PERSON><PERSON> chờ',
  [EMachineStatus.PROCESSING]: '<PERSON><PERSON> xử lý',
  [EMachineStatus.COMPLETED]: 'Hoàn thành',
  [EMachineStatus.FAILED]: 'Thất bại',
};

// Bản dịch cho trạng thái sản xuất
export const productionStatusTranslations = {
  [EProductionStatus.IN_PRODUCTION]: 'Đang sản xuất',
  [EProductionStatus.COMPLETED]: 'Đã hoàn thành',
  [EProductionStatus.DELIVERED]: 'Hoàn thành',
};

// Bản dịch cho trạng thái đơn hàng ứng dụng
export const orderAppStatusTranslations = {
  [EOrderAppStatus.PENDING]: 'Đang chờ',
  [EOrderAppStatus.ACCEPTED]: 'Đã chấp nhận',
  [EOrderAppStatus.REJECTED]: 'Đã từ chối',
  [EOrderAppStatus.DELIVERED]: 'Hoàn thành',
};

// Bản dịch cho phương thức thanh toán
export const paymentMethodTranslations = {
  [EPaymentMethod.ONLINE]: 'Chuyển khoản',
  [EPaymentMethod.OFFLINE]: 'Tiền mặt',
};

// Bản dịch cho trạng thái đơn hàng
export const orderStatusTranslations = {
  [EOrderStatus.IN_PROGRESS]: 'Đang xử lý',
  [EOrderStatus.COMPLETED]: 'Hoàn thành',
  [EOrderStatus.CANCELED]: 'Đã hủy',
};

// Bản dịch cho loại màn hình chờ
export const waitingScreenTypeTranslations = {
  [EWaitingScreenType.Default]: 'Mặc định',
  [EWaitingScreenType.Custom]: 'Tùy chỉnh',
};

// Bản dịch cho loại kích thước cài đặt
export const settingSizeTypeTranslations = {
  [ESettingSizeType.Default]: 'Mặc định',
  [ESettingSizeType.Custom]: 'Tùy chỉnh',
};

// Bản dịch cho trạng thái bố cục
export const layoutStatusTranslations = {
  [ELayoutStatus.ACTIVE]: 'Hoạt động',
  [ELayoutStatus.INACTIVE]: 'Không hoạt động',
};

// Bản dịch cho loại bố cục
export const layoutTypeTranslations = {
  [ELayoutType.Default]: 'Mặc định',
  [ELayoutType.Custom]: 'Tùy chỉnh',
};

// Bản dịch cho kích thước khung
export const frameSizeTranslations = {
  [EFrameSize.Big]: 'Lớn',
  [EFrameSize.Small]: 'Nhỏ',
};

// Bản dịch cho loại mục khung
export const frameItemTypeTranslations = {
  [EFrameItemType.SubFrame]: 'Khung phụ',
  [EFrameItemType.DubFrame]: 'Khung kép',
  [EFrameItemType.QrCode]: 'Mã QR',
  [EFrameItemType.Date]: 'Ngày tháng',
};

// Bản dịch cho hướng khung
export const frameOrientationTranslations = {
  [EFrameOrientation.Horizontal]: 'Ngang',
  [EFrameOrientation.Vertical]: 'Dọc',
};

// Hàm helper để dịch enum
export function translateEnum(
  value: string,
  translationMap: Record<string, string>,
): string {
  return translationMap[value] || value;
}
