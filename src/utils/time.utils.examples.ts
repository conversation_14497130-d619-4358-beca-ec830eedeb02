/**
 * Examples of how to use the time utility functions
 * This file demonstrates common usage patterns for Unix timestamp conversion
 */

import {
  unixTimestampToDate,
  dateToUnixTimestamp,
  unixTimestampToString,
  validateUnixTimestamp,
  convertDateRangeFromUnix,
} from './time.utils';

// Example 1: Convert Unix timestamp to Date
console.log('=== Example 1: Unix Timestamp to Date ===');
const unixTimestamp = '1746464400'; // January 5, 2025 00:00:00 UTC
const date = unixTimestampToDate(unixTimestamp);
console.log(`Unix timestamp: ${unixTimestamp}`);
console.log(`Converted date: ${date}`);
console.log(`ISO string: ${date?.toISOString()}`);

// Example 2: Convert Date to Unix timestamp
console.log('\n=== Example 2: Date to Unix Timestamp ===');
const currentDate = new Date();
const currentUnixTimestamp = dateToUnixTimestamp(currentDate);
console.log(`Current date: ${currentDate.toISOString()}`);
console.log(`Unix timestamp: ${currentUnixTimestamp}`);

// Example 3: Format Unix timestamp to different string formats
console.log('\n=== Example 3: Format Unix Timestamp ===');
const timestamp = '1748365199'; // January 27, 2025 23:59:59 UTC
console.log(`Unix timestamp: ${timestamp}`);
console.log(`ISO format: ${unixTimestampToString(timestamp, 'iso')}`);
console.log(`Local format: ${unixTimestampToString(timestamp, 'local')}`);
console.log(`UTC format: ${unixTimestampToString(timestamp, 'utc')}`);

// Example 4: Validate Unix timestamp
console.log('\n=== Example 4: Validate Unix Timestamp ===');
const validTimestamp = '1746464400';
const invalidTimestamp = 'invalid';
const negativeTimestamp = '-1000';

console.log(`Validating "${validTimestamp}":`, validateUnixTimestamp(validTimestamp));
console.log(`Validating "${invalidTimestamp}":`, validateUnixTimestamp(invalidTimestamp));
console.log(`Validating "${negativeTimestamp}":`, validateUnixTimestamp(negativeTimestamp));

// Example 5: Convert date range for database queries
console.log('\n=== Example 5: Convert Date Range ===');
const startTimestamp = '1746464400'; // January 5, 2025
const endTimestamp = '1748365199';   // January 27, 2025

const dateRange = convertDateRangeFromUnix(startTimestamp, endTimestamp);
console.log('Date range conversion result:', dateRange);

// Example 6: Handle invalid date range
console.log('\n=== Example 6: Handle Invalid Date Range ===');
const invalidStartTimestamp = 'invalid';
const invalidEndTimestamp = '1746464400';

const invalidDateRange = convertDateRangeFromUnix(invalidStartTimestamp, invalidEndTimestamp);
console.log('Invalid date range result:', invalidDateRange);

// Example 7: Usage in GraphQL resolver context
console.log('\n=== Example 7: GraphQL Usage Pattern ===');
function exampleGraphQLResolver(input: { startDate?: string; endDate?: string }) {
  try {
    const { startDate, endDate, errors } = convertDateRangeFromUnix(
      input.startDate,
      input.endDate
    );

    if (errors && errors.length > 0) {
      throw new Error(`Invalid date range: ${errors.join(', ')}`);
    }

    console.log('Converted dates for database query:');
    console.log('Start date:', startDate);
    console.log('End date:', endDate);

    // Use startDate and endDate in your TypeORM query
    return { startDate, endDate };
  } catch (error) {
    console.error('Error in resolver:', error);
    throw error;
  }
}

// Test the resolver example
const graphqlInput = {
  startDate: '1746464400',
  endDate: '1748365199'
};

console.log('GraphQL input:', graphqlInput);
exampleGraphQLResolver(graphqlInput);
