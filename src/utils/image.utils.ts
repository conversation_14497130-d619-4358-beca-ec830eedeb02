import { AppImage } from 'src/modules/clientApp/uploadImage/image.entity';
import { Frame } from 'src/modules/common/entity/frame.entity';
import { EImageFileType } from 'src/enum';

/**
 * Interface for the result of image validation
 */
export interface ImageValidationResult {
  isValid: boolean;
  reason?: string;
  message?: string;
}

/**
 * Check if reupload is allowed based on image count and frame requirements
 * @param images List of images for the order
 * @param frame Frame information with numberPicture requirement
 * @param orderId Order ID for logging purposes
 * @returns Validation result with isValid flag and reason/message
 */
export function canReuploadImages(
  images: AppImage[],
  frame: Frame,
  orderId: string,
): ImageValidationResult {
  // If there are no images, allow reupload immediately
  if (images.length === 0) {
    return {
      isValid: true,
      message: `Allowing reupload for order ${orderId} because there are no images yet.`,
    };
  }

  // Optimize: Count all image types in a single pass
  let imageCount = 0;
  let hasVideo = false;
  let hasFinalImage = false;

  // Process all images in a single loop
  for (const img of images) {
    if (img.fileType === EImageFileType.IMAGE) {
      imageCount++;
    } else if (img.fileType === EImageFileType.VIDEO) {
      hasVideo = true;
    } else if (img.fileType === EImageFileType.IMAGE_FINAL) {
      hasFinalImage = true;
    }

    // Early termination check - if we already know we can't allow reupload
    // This is an optimization for large image collections
    if (hasVideo && hasFinalImage && imageCount >= frame.numberPicture) {
      break;
    }
  }

  // Fast path: Check if all conditions are met to deny reupload
  if (hasVideo && hasFinalImage && imageCount >= frame.numberPicture) {
    return {
      isValid: false,
      reason:
        'Cannot reupload images. The order already has all required components: sufficient regular images, final image, and video.',
    };
  }

  // Determine the specific reason for allowing reupload
  if (imageCount < frame.numberPicture) {
    return {
      isValid: true,
      message: `Allowing reupload for order ${orderId} because image count (${imageCount}) is less than numberPicture (${frame.numberPicture}).`,
    };
  }

  if (!hasFinalImage) {
    return {
      isValid: true,
      message: `Allowing reupload for order ${orderId} because there is no final image.`,
    };
  }

  // Must be !hasVideo at this point
  return {
    isValid: true,
    message: `Allowing reupload for order ${orderId} because there is no video.`,
  };
}
