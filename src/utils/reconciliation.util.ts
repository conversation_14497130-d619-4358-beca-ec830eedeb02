/**
 * Utility functions for cost reconciliation operations
 */

/**
 * Generate a unique reconciliation code
 * Format: REC-YYYYMMDD-XXXXXX (where X is random alphanumeric)
 */
export function generateReconciliationCode(): string {
  const now = new Date();
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD
  const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `REC-${dateStr}-${randomStr}`;
}

/**
 * Parse order IDs from JSON string
 * @param orderIdsString JSON string of order IDs
 * @returns Array of order IDs
 */
export function parseOrderIds(orderIdsString?: string): string[] {
  if (!orderIdsString) {
    return [];
  }

  try {
    const parsed = JSON.parse(orderIdsString);
    return Array.isArray(parsed) ? parsed : [];
  } catch (error) {
    console.error('Error parsing order IDs:', error);
    return [];
  }
}

/**
 * Convert array of order IDs to JSON string
 * @param orderIds Array of order IDs
 * @returns JSON string
 */
export function stringifyOrderIds(orderIds: string[]): string {
  return JSON.stringify(orderIds);
}

/**
 * Merge two arrays of order IDs, removing duplicates
 * @param existingIds Existing order IDs
 * @param newIds New order IDs to add
 * @returns Merged array without duplicates
 */
export function mergeOrderIds(
  existingIds: string[],
  newIds: string[],
): string[] {
  const combined = [...existingIds, ...newIds];
  return [...new Set(combined)]; // Remove duplicates
}

/**
 * Calculate discount amount based on revenue and total orders
 * Logic: under 150 orders = 1.5% of revenue, over 150 orders = 1% of revenue
 * @param revenue Total revenue
 * @param totalOrders Total number of orders
 * @returns Discount amount
 */
export function calculateDiscountAmount(
  revenue: number,
  totalOrders: number,
): number {
  const discountRate = totalOrders < 150 ? 0.015 : 0.01; // 1.5% or 1%
  return Math.round(revenue * discountRate * 100) / 100; // Round to 2 decimal places
}

/**
 * Create month key from date in format YYYY-MM
 * @param date Date object
 * @returns Month key string (e.g., "2025-01")
 */
export function createMonthKeyFromDate(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  return `${year}-${month}`;
}

/**
 * Validate month format (YYYY-MM)
 * @param month Month string to validate
 * @returns True if valid format
 */
export function isValidMonthFormat(month: string): boolean {
  const monthRegex = /^\d{4}-\d{2}$/;
  return monthRegex.test(month);
}

/**
 * Get first day of month from month string
 * @param month Month string in format YYYY-MM
 * @returns Date object representing first day of the month
 */
export function getFirstDayOfMonth(month: string): Date {
  if (!isValidMonthFormat(month)) {
    throw new Error('Invalid month format. Expected YYYY-MM');
  }

  const [year, monthNum] = month.split('-').map(Number);
  return new Date(year, monthNum - 1, 1);
}

/**
 * Get month range (start and end dates) from month string
 * @param month Month string in format YYYY-MM
 * @returns Object with startDate and endDate
 */
export function getMonthRange(month: string): {
  startDate: Date;
  endDate: Date;
} {
  const startDate = getFirstDayOfMonth(month);
  const endDate = new Date(
    startDate.getFullYear(),
    startDate.getMonth() + 1,
    0,
  ); // Last day of month

  return { startDate, endDate };
}

/**
 * Format reconciliation status for display
 * @param status ECostReconciliationStatus
 * @returns Formatted status string
 */
export function formatReconciliationStatus(status: string): string {
  switch (status) {
    case 'PAID':
      return 'Paid';
    case 'UNPAID':
      return 'Unpaid';
    case 'NOT_RECONCILED':
      return 'Not Reconciled';
    default:
      return status;
  }
}
