/**
 * Time utility functions for calculating time differences and validations
 * Optimized for performance with direct calculations
 */

/**
 * Constants for time conversions to improve readability and performance
 */
const MS_PER_MINUTE = 60 * 1000;
const MS_PER_HOUR = 60 * MS_PER_MINUTE;

/**
 * Convert any date representation to milliseconds timestamp
 * @param date Date object or timestamp
 * @returns Timestamp in milliseconds
 */
function toMilliseconds(date: Date | number): number {
  return date instanceof Date ? date.getTime() : date;
}

/**
 * Calculate minutes elapsed since a given date
 * @param startDate The start date (can be Date object or timestamp)
 * @param endDate Optional end date (defaults to current time)
 * @returns Number of minutes elapsed
 */
export function getMinutesElapsed(
  startDate: Date | number,
  endDate: Date | number = Date.now(),
): number {
  const startTime = toMilliseconds(startDate);
  const endTime = toMilliseconds(endDate);

  return (endTime - startTime) / MS_PER_MINUTE;
}

/**
 * Calculate hours elapsed since a given date
 * @param startDate The start date (can be Date object or timestamp)
 * @param endDate Optional end date (defaults to current time)
 * @returns Number of hours elapsed
 */
export function getHoursElapsed(
  startDate: Date | number,
  endDate: Date | number = Date.now(),
): number {
  const startTime = toMilliseconds(startDate);
  const endTime = toMilliseconds(endDate);

  // Calculate directly without calling getMinutesElapsed to avoid extra function calls
  return (endTime - startTime) / MS_PER_HOUR;
}

/**
 * Check if a given time is within a specified range
 * @param startDate The reference date to check from
 * @param minMinutes Minimum minutes that must have elapsed
 * @param maxHours Maximum hours that can elapse
 * @param currentTime Optional current time (defaults to now)
 * @returns Object with isValid flag and reason if invalid
 */
export function isTimeInRange(
  startDate: Date | number,
  minMinutes: number,
  maxHours: number,
  currentTime: Date | number = Date.now(),
): { isValid: boolean; reason?: string } {
  const startTimeMs = toMilliseconds(startDate);
  const currentTimeMs = toMilliseconds(currentTime);
  const elapsedMs = currentTimeMs - startTimeMs;

  // Direct comparison with milliseconds for better performance
  const minMs = minMinutes * MS_PER_MINUTE;
  const maxMs = maxHours * MS_PER_HOUR;

  // Check minimum time constraint
  if (elapsedMs < minMs) {
    return {
      isValid: false,
      reason: `Cannot perform this action within ${minMinutes} minutes of creation. Please try again later.`,
    };
  }

  // Check maximum time constraint
  if (elapsedMs > maxMs) {
    return {
      isValid: false,
      reason: `Cannot perform this action after ${maxHours} hours of creation. The time limit has expired.`,
    };
  }

  return { isValid: true };
}

/**
 * Convert Unix timestamp string to Date object
 * @param unixTimestamp Unix timestamp as string (seconds since epoch)
 * @returns Date object or null if invalid
 */
export function unixTimestampToDate(unixTimestamp: string): Date | null {
  try {
    const timestamp = parseInt(unixTimestamp);
    if (isNaN(timestamp) || timestamp < 0) {
      return null;
    }
    return new Date(timestamp * 1000);
  } catch (error) {
    return null;
  }
}

/**
 * Convert Date object to Unix timestamp string
 * @param date Date object
 * @returns Unix timestamp as string (seconds since epoch)
 */
export function dateToUnixTimestamp(date: Date): string {
  return Math.floor(date.getTime() / 1000).toString();
}

/**
 * Convert Unix timestamp string to formatted date string
 * @param unixTimestamp Unix timestamp as string
 * @param format Optional format ('iso' | 'local' | 'utc'), defaults to 'iso'
 * @returns Formatted date string or null if invalid
 */
export function unixTimestampToString(
  unixTimestamp: string,
  format: 'iso' | 'local' | 'utc' = 'iso',
): string | null {
  const date = unixTimestampToDate(unixTimestamp);
  if (!date) {
    return null;
  }

  switch (format) {
    case 'iso':
      return date.toISOString();
    case 'local':
      return date.toLocaleString();
    case 'utc':
      return date.toUTCString();
    default:
      return date.toISOString();
  }
}

/**
 * Validate if a Unix timestamp string is valid
 * @param unixTimestamp Unix timestamp as string
 * @returns Object with isValid flag and parsed date if valid
 */
export function validateUnixTimestamp(unixTimestamp: string): {
  isValid: boolean;
  date?: Date;
  error?: string;
} {
  if (!unixTimestamp || typeof unixTimestamp !== 'string') {
    return {
      isValid: false,
      error: 'Unix timestamp must be a non-empty string',
    };
  }

  const timestamp = parseInt(unixTimestamp);
  if (isNaN(timestamp)) {
    return {
      isValid: false,
      error: 'Unix timestamp must be a valid number',
    };
  }

  if (timestamp < 0) {
    return {
      isValid: false,
      error: 'Unix timestamp cannot be negative',
    };
  }

  // Check if timestamp is reasonable (not too far in the future)
  const maxTimestamp = Math.floor(Date.now() / 1000) + 365 * 24 * 60 * 60 * 10; // 10 years from now
  if (timestamp > maxTimestamp) {
    return {
      isValid: false,
      error: 'Unix timestamp is too far in the future',
    };
  }

  const date = new Date(timestamp * 1000);
  return {
    isValid: true,
    date,
  };
}

/**
 * Convert date range from Unix timestamps to Date objects for database queries
 * @param startTimestamp Start Unix timestamp string
 * @param endTimestamp End Unix timestamp string
 * @returns Object with converted dates or null if invalid
 */
export function convertDateRangeFromUnix(
  startTimestamp?: string,
  endTimestamp?: string,
): {
  startDate?: Date;
  endDate?: Date;
  errors?: string[];
} {
  const errors: string[] = [];
  let startDate: Date | undefined;
  let endDate: Date | undefined;

  if (startTimestamp) {
    const startValidation = validateUnixTimestamp(startTimestamp);
    if (startValidation.isValid && startValidation.date) {
      startDate = startValidation.date;
    } else {
      errors.push(`Invalid start date: ${startValidation.error}`);
    }
  }

  if (endTimestamp) {
    const endValidation = validateUnixTimestamp(endTimestamp);
    if (endValidation.isValid && endValidation.date) {
      endDate = endValidation.date;
    } else {
      errors.push(`Invalid end date: ${endValidation.error}`);
    }
  }

  // Validate date range logic
  if (startDate && endDate && startDate > endDate) {
    errors.push('Start date cannot be after end date');
  }

  return {
    startDate,
    endDate,
    errors: errors.length > 0 ? errors : undefined,
  };
}
