import { getLogger, configure } from 'log4js';

const config = {
  appenders: {
    inboundLogging: {
      type: 'console',
      layout: {
        type: 'pattern',
        pattern: '%[[%X{requestType}][%d][reqId:%X{requestId}]%] %m',
      },
    },
    headerLogging: {
      type: 'console',
      layout: {
        type: 'pattern',
        pattern: '%[[%X{requestType}][Headers][reqId:%X{requestId}]%] %m',
      },
    },
    operationNameLogging: {
      type: 'console',
      layout: {
        type: 'pattern',
        pattern:
          '%[[%X{requestType}][GraphQL Operation Name][reqId:%X{requestId}]%] %m',
      },
    },
    parameterLogging: {
      type: 'console',
      layout: {
        type: 'pattern',
        pattern:
          '%[[%X{requestType}][GraphQL Parameters][reqId:%X{requestId}]%] %m',
      },
    },
    errorLogging: {
      type: 'console',
      layout: {
        type: 'pattern',
        pattern: '%[[%X{requestType}][%p][reqId:%X{requestId}]%] %m',
      },
    },
    // Add basic appender for structured logging (we'll handle JSON formatting ourselves)
    jsonLogging: {
      type: 'console',
      layout: {
        type: 'pattern',
        pattern: '%m',
      },
    },
  },
  categories: {
    default: { appenders: ['inboundLogging'], level: 'info' },
    inboundLogging: { appenders: ['inboundLogging'], level: 'info' },
    headerLogging: { appenders: ['headerLogging'], level: 'info' },
    operationNameLogging: {
      appenders: ['operationNameLogging'],
      level: 'info',
    },
    parameterLogging: { appenders: ['parameterLogging'], level: 'info' },
    errorLogging: { appenders: ['errorLogging'], level: 'error' },
    jsonLogging: { appenders: ['jsonLogging'], level: 'info' },
  },
};

configure(config);

export const getConsoleLogger = (category: any) => getLogger(category);

// Helper function to mask sensitive data in logs
export const maskSensitiveData = (data: any): any => {
  if (!data) return data;

  // Create a deep copy to avoid modifying the original object
  const maskedData = JSON.parse(JSON.stringify(data));

  // List of fields to mask
  const sensitiveFields = [
    'authorization',
    'password',
    'token',
    'accessToken',
    'refreshToken',
    'secret',
    'apiKey',
  ];

  // Function to recursively mask fields
  const maskFields = (obj: any) => {
    if (typeof obj !== 'object' || obj === null) return;

    Object.keys(obj).forEach((key) => {
      const lowerKey = key.toLowerCase();

      if (sensitiveFields.some((field) => lowerKey.includes(field))) {
        if (typeof obj[key] === 'string') {
          // Mask the value but keep the first and last 4 characters if long enough
          const value = obj[key];
          if (value.length > 8) {
            obj[key] =
              `${value.substring(0, 4)}...${value.substring(value.length - 4)}`;
          } else {
            obj[key] = '********';
          }
        } else {
          obj[key] = '********';
        }
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        // Recursively process nested objects
        maskFields(obj[key]);
      }
    });
  };

  maskFields(maskedData);
  return maskedData;
};
