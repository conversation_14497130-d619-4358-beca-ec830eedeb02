import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { DataSource, DataSourceOptions } from 'typeorm';
import { join } from 'path';
import 'dotenv/config';
import { SeederOptions } from 'typeorm-extension';
import { CustomTypeOrmLogger } from '../utils/typeorm-logger';

export const getDatabaseDataSourceOptions = ({
  port,
  host,
  username,
  database,
  password,
  migrations,
}): DataSourceOptions & SeederOptions => {
  return {
    type: 'mysql',
    port,
    host,
    username,
    database,
    password: password,
    entities: [join(__dirname, '../', '**', '*.entity.{ts,js}')],
    migrations,
    seeds: [join(__dirname, '../', 'seeds', '*.{ts,js}')],
  };
};

export const typeOrmConfig: TypeOrmModuleOptions = {
  type: 'mysql',
  host: process.env.DB_HOST ?? 'localhost',
  port: process.env.DB_PORT ? parseInt(process.env.DB_PORT) : 3306,
  username: process.env.DB_USERNAME ?? 'root',
  password: process.env.DB_PASSWORD ?? '',
  database: process.env.DB_DATABASE ?? 'snapbox-development',
  entities: [join(__dirname, '../', '**', '*.entity.{ts,js}')],
  migrations: [join(__dirname, '../', 'migrations', '*.{ts,js}')],
  synchronize: false,

  // Enable SQL query logging based on environment variable and context
  logging: (() => {
    // Trong môi trường development, log tất cả
    if (process.env.NODE_ENV !== 'production') {
      return ['query', 'error', 'schema', 'warn', 'info', 'log', 'migration'];
    }

    // Trong môi trường production, kiểm tra biến môi trường SQL_LOGGING
    if (process.env.SQL_LOGGING === 'true') {
      return ['query', 'error', 'schema', 'warn', 'info', 'log', 'migration'];
    }

    // Mặc định trong production: chỉ log lỗi, schema và cảnh báo
    return ['error', 'schema', 'warn'];
  })(),
  logger: new CustomTypeOrmLogger(), // Use our custom logger

  // Log all queries that take more than the specified time as slow queries
  maxQueryExecutionTime: process.env.SQL_SLOW_THRESHOLD
    ? parseInt(process.env.SQL_SLOW_THRESHOLD)
    : 1000, // Default to 1000ms if not specified
};

// This is used by TypeORM migration scripts
export const DatabaseSource = new DataSource({
  ...getDatabaseDataSourceOptions(typeOrmConfig as any),
  logging: (() => {
    // Trong môi trường development, log tất cả
    if (process.env.NODE_ENV !== 'production') {
      return ['query', 'error', 'schema', 'warn', 'info', 'log', 'migration'];
    }

    // Trong môi trường production, kiểm tra biến môi trường SQL_LOGGING
    if (process.env.SQL_LOGGING === 'true') {
      return ['query', 'error', 'schema', 'warn', 'info', 'log', 'migration'];
    }

    // Mặc định trong production: chỉ log lỗi, schema và cảnh báo
    return ['error', 'schema', 'warn'];
  })(),
  logger: new CustomTypeOrmLogger(),
  maxQueryExecutionTime: process.env.SQL_SLOW_THRESHOLD
    ? parseInt(process.env.SQL_SLOW_THRESHOLD)
    : 1000,
});
