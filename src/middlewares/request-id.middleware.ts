/* eslint-disable @typescript-eslint/no-namespace */
import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

// Create a global namespace for request context
declare global {
  namespace NodeJS {
    interface Global {
      requestContext: {
        [key: string]: any;
      };
    }
  }
}

// Initialize the global request context
(global as any).requestContext = {};

/**
 * Get the current request ID from the global context
 * This function can be called from anywhere in the application
 */
export function getCurrentRequestId(): string {
  return (global as any).requestContext.requestId || 'no-request-id';
}

/**
 * Set the current request ID in the global context
 * This function can be called from anywhere in the application
 */
export function setCurrentRequestId(requestId: string): void {
  (global as any).requestContext.requestId = requestId;
}

@Injectable()
export class RequestIdMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction) {
    try {
      // Generate a unique request ID if not already present
      // First check if it's in the headers
      let requestId = req.headers['x-request-id'] as string;

      // If not in headers, check if it's in the query parameters (useful for debugging)
      if (!requestId && req.query && req.query.requestId) {
        requestId = req.query.requestId as string;
      }

      // If still not found, generate a new one
      if (!requestId) {
        requestId = uuidv4();
      }

      // Store the request ID in the request object
      req['requestId'] = requestId;

      // Make requestId available globally for error handling
      setCurrentRequestId(requestId);

      // Also set it in the old location for backward compatibility
      (global as any).requestId = requestId;

      // Add the request ID to the response headers (only if headers haven't been sent)
      if (!res.headersSent) {
        res.setHeader('X-Request-ID', requestId);
      }

      // Store the request start time for performance tracking
      req['startTime'] = Date.now();
    } catch (error) {
      console.error('Error in RequestIdMiddleware:', error);
    }

    next();
  }
}
