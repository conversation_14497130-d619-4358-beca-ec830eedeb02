import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { SBLogger } from '../modules/logger/logger.service';
import {
  getCurrentRequestId,
  setCurrentRequestId,
} from './request-id.middleware';

@Injectable()
export class LoggerContextMiddleware implements NestMiddleware {
  constructor(private readonly logger: SBLogger) {}

  use(req: Request, res: Response, next: NextFunction) {
    try {
      // Get the request ID from the request object (set by RequestIdMiddleware)
      // or from the global context
      const requestId = req['requestId'] || getCurrentRequestId();

      // Ensure the request ID is set in the global context
      setCurrentRequestId(requestId);

      // Set request ID in logger
      this.logger.setRequestId(requestId);

      // Set user ID in logger if authenticated
      if (req['user']?.id) {
        this.logger.setUserId(req['user'].id);
      }

      // Log the start of the request
      this.logger.log('Request', {
        method: req.method,
        url: req.originalUrl,
        ip: req.ip,
        requestId, // Include request ID in the log payload for better context
      });

      // Log when the response is finished
      res.on('finish', () => {
        // Get the current request ID (it might have changed during the request)
        const currentRequestId = getCurrentRequestId();
        const responseTime = res.locals?.responseTime || 0;

        this.logger.log('Response', {
          statusCode: res.statusCode,
          responseTime,
          requestId: currentRequestId, // Use the current request ID
        });
      });

      // Add error handling for uncaught exceptions in this request
      res.on('close', () => {
        if (!res.writableEnded) {
          // The response was closed without being sent - likely an error or timeout
          // Get the current request ID (it might have changed during the request)
          const currentRequestId = getCurrentRequestId();

          this.logger.error('Connection closed unexpectedly', {
            method: req.method,
            url: req.originalUrl,
            requestId: currentRequestId,
          });
        }
      });
    } catch (error) {
      console.error('Error in LoggerContextMiddleware:', error);
    }

    next();
  }
}
