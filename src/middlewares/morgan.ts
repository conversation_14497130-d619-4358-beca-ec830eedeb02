import { URL } from 'url';
import * as morgan from 'morgan';
import { Request, Response } from 'express';
import { getConsoleLogger, maskSensitiveData } from '../utils/consoleLogger';
import { getCurrentRequestId } from './request-id.middleware';

// Create loggers with a consistent category prefix for better filtering
const inboundLogger = getConsoleLogger('inboundLogging');
const headerLogger = getConsoleLogger('headerLogging');
const parameterLogger = getConsoleLogger('parameterLogging');
const operationNameLogger = getConsoleLogger('operationNameLogging');
const jsonLogger = getConsoleLogger('jsonLogging');

// Add context to loggers
inboundLogger.addContext('requestType', 'HttpLogging');
headerLogger.addContext('requestType', 'HttpLogging');
parameterLogger.addContext('requestType', 'HttpLogging');
operationNameLogger.addContext('requestType', 'HttpLogging');

// Define log formats
const requestFormat =
  '[:method :url HTTP/:http-version] Started for :remote-addr';
const headerFormat = ':req-headers';
const parameterFormat = ':params';
const responseFormat = 'Completed :status in :response-time[1] ms';

// Add custom tokens
morgan.token('params', (req: any) => {
  if (req.method === 'GET') {
    const params: { [k: string]: string } = {};
    try {
      new URL(
        req.protocol + '://' + req.get('host') + req.originalUrl,
      ).searchParams.forEach((v, k) => (params[k] = v));
      return JSON.stringify(params);
    } catch (error: any) {
      return JSON.stringify({
        error: 'Failed to parse URL parameters',
        message: error,
        originalUrl: req.originalUrl,
      });
    }
  }

  // Skip introspection queries to reduce noise
  if (req.body?.query?.includes('IntrospectionQuery')) {
    return;
  }

  // Special handling for GraphQL requests
  if (req.body?.query) {
    try {
      // Extract GraphQL operation type and name using regex
      const operationMatch = req.body.query.match(
        /^\s*(query|mutation|subscription)\s+(\w+)?/m,
      );
      const operationType = operationMatch ? operationMatch[1] : 'unknown';
      const operationName =
        operationMatch && operationMatch[2]
          ? operationMatch[2]
          : req.body.operationName || 'anonymous';

      // Extract variables
      const variables = req.body.variables || {};

      // Create a structured representation of the GraphQL request
      const graphqlData = {
        operation: `${operationType} ${operationName}`,
        variables: maskSensitiveData(variables),
        // Include the full query for complete context
        query: req.body.query,
      };

      return JSON.stringify(graphqlData);
    } catch (error: any) {
      return JSON.stringify({
        error: 'Failed to parse GraphQL parameters',
        backtrace: error,
        body: maskSensitiveData(req.body),
      });
    }
  }

  // Handle regular POST requests
  // Mask sensitive data before logging
  const maskedBody = maskSensitiveData({ ...req.body, ...req.fields });

  // Don't log file content, just metadata
  const files = req.files
    ? Object.keys(req.files).reduce((acc, key) => {
        acc[key] = {
          name: req.files[key].name,
          size: req.files[key].size,
          mimetype: req.files[key].mimetype,
        };
        return acc;
      }, {})
    : {};

  return JSON.stringify({ ...maskedBody, files });
});

morgan.token('req-headers', (req: Request) => {
  // Mask sensitive headers before logging
  const maskedHeaders = maskSensitiveData(req.headers);
  return JSON.stringify(maskedHeaders);
});

morgan.token('request-id', () => {
  return getCurrentRequestId();
});

morgan.token('user-id', (req: any) => {
  return req.user?.id || 'anonymous';
});

// Add structured logging token
morgan.token('structured-log', (req: any, res: Response) => {
  const maskedHeaders = maskSensitiveData(req.headers);
  let params = {};

  if (req.method === 'GET') {
    try {
      new URL(
        req.protocol + '://' + req.get('host') + req.originalUrl,
      ).searchParams.forEach((v, k) => (params[k] = v));
    } catch {
      params = { error: 'Failed to parse URL parameters' };
    }
  } else if (req.body?.query) {
    // Special handling for GraphQL requests
    try {
      // Extract GraphQL operation type and name using regex
      const operationMatch = req.body.query.match(
        /^\s*(query|mutation|subscription)\s+(\w+)?/m,
      );
      const operationType = operationMatch ? operationMatch[1] : 'unknown';
      const operationName =
        operationMatch && operationMatch[2]
          ? operationMatch[2]
          : req.body.operationName || 'anonymous';

      // Extract variables
      const variables = req.body.variables || {};

      // Create a structured representation of the GraphQL request
      params = {
        operation: `${operationType} ${operationName}`,
        variables: maskSensitiveData(variables),
      };
    } catch (error: any) {
      params = {
        error: 'Failed to parse GraphQL parameters',
        backtrace: error,
        body: maskSensitiveData(req.body),
      };
    }
  } else {
    // Regular POST request
    params = maskSensitiveData({ ...req.body, ...req.fields });
  }

  return JSON.stringify({
    timestamp: new Date().toISOString(),
    requestId: getCurrentRequestId(),
    userId: req.user?.id || 'anonymous',
    method: req.method,
    url: req.originalUrl,
    httpVersion: req.httpVersion,
    remoteAddr: req.ip,
    headers: maskedHeaders,
    params,
    responseStatus: res.statusCode,
    responseTime: res.locals?.responseTime || 0,
  });
});

export const morganLogger = () => {
  return [
    // Middleware to add request ID to log4js context
    (req: any, res: Response, next: () => void) => {
      // Get the request ID from the request object (set by RequestIdMiddleware)
      const requestId = req.requestId || getCurrentRequestId();

      // Add request ID to log4js context for all loggers
      inboundLogger.addContext('requestId', requestId);
      headerLogger.addContext('requestId', requestId);
      parameterLogger.addContext('requestId', requestId);
      operationNameLogger.addContext('requestId', requestId);
      jsonLogger.addContext('requestId', requestId);

      // Add response time tracking
      const startTime = Date.now();

      // Store response time in a variable that can be accessed by morgan tokens
      res.locals = res.locals || {};

      // Store the request ID in res.locals for easy access
      res.locals.requestId = requestId;

      res.on('finish', () => {
        const responseTime = Date.now() - startTime;
        // Store the response time in res.locals instead of trying to set a header
        res.locals.responseTime = responseTime;

        // Log the response time
        inboundLogger.info(
          `Response time: ${responseTime}ms [reqId:${getCurrentRequestId()}]`,
        );
      });

      // Set the X-Response-Time header before sending the response
      const originalEnd = res.end;
      // Override the end method with proper type handling
      res.end = function (
        this: Response,
        chunk?: any,
        encoding?: string | (() => void),
        callback?: () => void,
      ): Response {
        const responseTime = Date.now() - startTime;
        // Only set the header if headers haven't been sent yet
        if (!res.headersSent) {
          res.setHeader('X-Response-Time', responseTime.toString());
        }

        // Handle the case where encoding is actually the callback
        if (typeof encoding === 'function') {
          callback = encoding;
          encoding = undefined;
        }

        return originalEnd.call(
          this,
          chunk,
          encoding as BufferEncoding,
          callback,
        );
      };

      next();
    },

    // Request logging
    morgan(requestFormat, {
      immediate: true,
      stream: {
        write: (str: string) => {
          inboundLogger.info(str.substring(0, str.lastIndexOf('\n')));
        },
      },
    }),

    // Headers logging
    morgan(headerFormat, {
      immediate: true,
      stream: {
        write: (str: string) => {
          headerLogger.info(str.substring(0, str.lastIndexOf('\n')));
        },
      },
    }),

    // Parameters logging
    morgan(parameterFormat, {
      immediate: true,
      stream: {
        write: (str: string) => {
          if (!str || str.indexOf('-') === 0) {
            parameterLogger.info(
              str ? str.substring(0, str.lastIndexOf('\n')) : 'No parameters',
            );
          } else {
            try {
              const payload = JSON.parse(str);

              // Handle GraphQL operations
              if (payload?.operation) {
                // Log the operation name
                operationNameLogger.info(payload.operation);

                // Log the variables
                if (payload.variables) {
                  parameterLogger.info(JSON.stringify(payload.variables));
                }

                // Log the full query for complete context
                if (payload.query) {
                  parameterLogger.info(
                    `Query: ${JSON.stringify(payload.query)}`,
                  );
                }
              }
              // Handle legacy format
              else {
                const operationName = payload?.operationName;
                const variables = JSON.stringify(payload?.variables || {});

                if (operationName) {
                  operationNameLogger.info(operationName);
                }

                parameterLogger.info(variables);
              }
            } catch (error: any) {
              // Log the error and the full raw string for debugging
              parameterLogger.error(
                `Failed to parse parameters: ${error.message}`,
              );
              parameterLogger.info(`Failed to parse parameters: ${str}`);
            }
          }
        },
      },
    }),

    // Response logging
    morgan(responseFormat, {
      stream: {
        write: (str: string) => {
          inboundLogger.info(str.substring(0, str.lastIndexOf('\n')));
        },
      },
    }),

    // Structured JSON logging (for easier parsing)
    morgan(':structured-log', {
      skip: (_req: any, res: Response) => res.statusCode < 400, // Only log errors in JSON format to reduce noise
      stream: {
        write: (str: string) => {
          try {
            // The string is already JSON, so we can pass it directly
            jsonLogger.info(str.substring(0, str.lastIndexOf('\n')));
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';

            inboundLogger.error(
              `Failed to log in JSON format: ${errorMessage}`,
            );
          }
        },
      },
    }),
  ];
};
