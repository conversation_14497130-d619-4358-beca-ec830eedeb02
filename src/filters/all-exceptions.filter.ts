import {
  ExceptionFilter,
  Catch,
  ArgumentsHost,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { getCurrentRequestId } from '../middlewares/request-id.middleware';

/**
 * <PERSON><PERSON> lọc ngoại lệ toàn cục để xử lý tất cả các lỗi không được bắt
 * Đặc biệt quan trọng cho việc xử lý lỗi client disconnect
 */
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  private readonly logger = new Logger(AllExceptionsFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const requestId = getCurrentRequestId();

    // Kiểm tra xem đây có phải là HTTP context không
    if (host.getType() === 'http') {
      const ctx = host.switchToHttp();
      const response = ctx.getResponse<Response>();
      const request = ctx.getRequest<Request>();

      return this.handleHttpException(exception, request, response, requestId);
    }

    // Nếu không phải HTTP context (có thể là GraphQL), chỉ log lỗi
    this.logError(exception, requestId);
  }

  private logError(exception: unknown, requestId: string) {
    const message =
      exception instanceof Error ? exception.message : 'Unknown error';
    const stack = exception instanceof Error ? exception.stack : undefined;

    // Log lỗi với request ID
    this.logger.error(
      `Exception occurred during request processing [reqId:${requestId}]: ${message}`,
      stack,
      'AllExceptionsFilter',
    );
  }

  private handleHttpException(
    exception: unknown,
    request: Request,
    response: Response,
    requestId: string,
  ) {
    // Xử lý lỗi client disconnect
    if (
      exception instanceof Error &&
      (exception.message.includes('disconnect') ||
        exception.message.includes('aborted') ||
        exception.message.includes('closed') ||
        exception.message.includes('Request disconnected'))
    ) {
      // Log ở mức warning thay vì error
      this.logger.warn(
        `Client disconnected during request processing: ${request.method} ${request.url}`,
        {
          requestId,
          error: exception.message,
          stack: exception.stack,
        },
      );

      // Nếu response vẫn có thể ghi và là một đối tượng Response hợp lệ, trả về mã lỗi 499
      if (
        response &&
        typeof response.status === 'function' &&
        !response.writableEnded
      ) {
        response.status(499).json({
          statusCode: 499,
          message: 'Client disconnected during request processing',
          path: request.url,
          timestamp: new Date().toISOString(),
          requestId,
        });
      } else {
        // Log lỗi nếu không thể trả về response
        this.logger.warn(
          `Could not send client disconnect response: response object is invalid or response.status is not a function`,
          {
            requestId,
            error: exception.message,
          },
        );
      }
      return;
    }

    // Xử lý các lỗi HTTP
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    const message =
      exception instanceof HttpException
        ? exception.message
        : 'Internal server error';

    // Log lỗi với request ID
    this.logger.error(
      `Exception occurred during request processing: ${message}`,
      exception instanceof Error ? exception.stack : undefined,
      'AllExceptionsFilter',
    );

    // Nếu response vẫn có thể ghi và là một đối tượng Response hợp lệ, trả về lỗi
    if (
      response &&
      typeof response.status === 'function' &&
      !response.writableEnded
    ) {
      response.status(status).json({
        statusCode: status,
        message,
        path: request.url,
        timestamp: new Date().toISOString(),
        requestId,
      });
    } else {
      // Log lỗi nếu không thể trả về response
      this.logger.error(
        `Could not send error response: response object is invalid or response.status is not a function`,
        undefined,
        'AllExceptionsFilter',
      );
    }
  }
}
