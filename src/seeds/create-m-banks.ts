import { MBank } from 'src/modules/common/entity/mBank.entity';
import { DataSource } from 'typeorm';
import { Seeder } from 'typeorm-extension';

export default class MBankSeeder implements Seeder {
  public async run(dataSource: DataSource): Promise<void> {
    const repository = dataSource.getRepository(MBank);
    const data = [
      {
        code: 'VCB',
        name: 'Ngân hàng TMCP Ngoại Thương Việt Nam',
        shortName: 'Vietcombank',
        logo: 'https://vietqr.net/portal-service/resources/icons/VCB.png',
      },
      {
        code: 'MB',
        name: '<PERSON><PERSON> hàng TMCP Quân đội',
        shortName: 'MBBank',
        logo: 'https://vietqr.net/portal-service/resources/icons/MB.png',
      },
      {
        code: 'BIDV',
        name: 'Ngân hàng TMCP Đầu tư và Phát triển Việt Nam',
        shortName: 'BIDV',
        logo: 'https://vietqr.net/portal-service/resources/icons/BIDV.png',
      },
      {
        code: 'ICB',
        name: '<PERSON><PERSON> hàng TMCP Công thương Việt Nam',
        shortName: 'VietinBank',
        logo: 'https://vietqr.net/portal-service/resources/icons/ICB.png',
      },
      {
        code: 'TCB',
        name: 'Ngân hàng TMCP Kỹ thương Việt Nam',
        shortName: 'Techcombank',
        logo: 'https://vietqr.net/portal-service/resources/icons/TCB.png',
      },
      {
        code: 'TPB',
        name: 'Ngân hàng TMCP Tiên Phong',
        shortName: 'TPBank',
        logo: 'https://vietqr.net/portal-service/resources/icons/TPB.png',
      },
      {
        code: 'OCB',
        name: 'Ngân hàng TMCP Phương Đông',
        shortName: 'OCB',
        logo: 'https://vietqr.net/portal-service/resources/icons/OCB.png',
      },
      {
        code: 'VBP',
        name: 'Ngân hàng TMCP Việt Nam Thịnh Vượng',
        shortName: 'VPBank',
        logo: 'https://vietqr.net/portal-service/resources/icons/VPB.png',
      },
      {
        code: 'SHBVN',
        name: 'Ngân hàng TNHH MTV Shinhan Việt Nam',
        shortName: 'ShinhanBank',
        logo: 'https://vietqr.net/portal-service/resources/icons/SHBVN.png',
      },
      {
        code: 'ACB',
        name: 'Ngân hàng TMCP Á Châu',
        shortName: 'ACB',
        logo: 'https://vietqr.net/portal-service/resources/icons/ACB.png',
      },
      {
        code: 'VBA',
        name: 'Ngân hàng Nông nghiệp và Phát triển Nông thôn Việt Nam',
        shortName: 'Agribank',
        logo: 'https://vietqr.net/portal-service/resources/icons/VBA.png',
      },
    ];

    for (const element of data) {
      const bank = await repository.findOne({ where: { code: element.code } });
      if (bank) {
        continue;
      }

      await repository.insert({
        ...element,
      });
    }
  }
}
