import * as bcrypt from 'bcrypt';
import { ERoles } from 'src/enum/role';
import { User } from 'src/modules/admin/user/user.entity';
import { DataSource } from 'typeorm';
import { Seeder, SeederFactoryManager } from 'typeorm-extension';

export default class UserSeeder implements Seeder {
  public async run(
    dataSource: DataSource,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    factoryManager: SeederFactoryManager,
  ): Promise<void> {
    const repository = dataSource.getRepository(User);
    const admin = await repository.findOne({ where: { role: ERoles.ADMIN } });
    if (admin) {
      return;
    }

    await repository.insert({
      email: '<EMAIL>',
      password: await bcrypt.hash(process.env.ADMIN_PASSWORD, 10), // Băm mật khẩu
      role: ERoles.ADMIN,
    });
  }
}
