import * as bcrypt from 'bcrypt';
import { ERoles } from '../enum/role';
import { User } from '../modules/admin/user/user.entity';
import { DataSource } from 'typeorm';
import { Seeder, SeederFactoryManager } from 'typeorm-extension';
import * as moment from 'moment';

export default class ModUserSeeder implements Seeder {
  public async run(
    dataSource: DataSource,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    factoryManager: SeederFactoryManager,
  ): Promise<void> {
    const repository = dataSource.getRepository(User);
    const modUser = await repository.findOne({
      where: {
        email: '<EMAIL>',
        role: ERoles.MOD,
      },
    });

    if (modUser) {
      return;
    }

    await repository.insert({
      name: 'Moderator User',
      email: '<EMAIL>',
      password: await bcrypt.hash('Mod1234@', 10),
      role: ERoles.MOD,
      createdAt: moment.utc().valueOf().toString(),
    });
  }
}
