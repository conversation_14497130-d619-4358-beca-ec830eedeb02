import { runSeeders } from 'typeorm-extension';
import { DatabaseSource } from '../configs/typeorm.config';
import ModUserSeeder from './create-mod-user';

const runModSeed = async () => {
  try {
    await DatabaseSource.initialize();
    console.log('Database connection initialized');

    await runSeeders(DatabaseSource, {
      seeds: [ModUserSeeder],
    });

    console.log('ModUserSeeder executed successfully');
    await DatabaseSource.destroy();
    console.log('Database connection closed');
  } catch (error) {
    console.error('Error running seed:', error);
    if (DatabaseSource.isInitialized) {
      await DatabaseSource.destroy();
    }
  }
};

runModSeed();
