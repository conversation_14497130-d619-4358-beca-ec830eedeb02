import { DEFAULT_PRINT_SETTING } from 'src/constants';
import { EPrintSettingType } from 'src/enum';
import { Client } from 'src/modules/common/entity/client.entity';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { PrintSetting } from 'src/modules/common/entity/printSetting.entity';
import { DataSource } from 'typeorm';
import { Seeder } from 'typeorm-extension';

export default class PrintSettingSeeder implements Seeder {
  public async run(dataSource: DataSource): Promise<void> {
    const repository = dataSource.getRepository(PrintSetting);
    const printSetting = await repository.findOne({
      where: { type: EPrintSettingType.Default },
    });
    if (printSetting) {
      return;
    }

    const clients = await dataSource.getRepository(Client).find();

    for (const client of clients) {
      const machines = await dataSource
        .getRepository(Machine)
        .find({ where: { userId: client.id } });
      if (machines.length > 0) {
        await repository.insert({
          type: EPrintSettingType.Default,
          clientId: client.id,
          ...DEFAULT_PRINT_SETTING,
        });
      }
    }
  }
}
