declare module 'morgan' {
  import { <PERSON><PERSON> } from 'express';

  /**
   * Create a new morgan logger middleware function using the given format and options.
   * The format argument may be a string of predefined tokens, a format string of predefined tokens,
   * or a function that will produce a log entry.
   */
  function morgan(format: string | Function, options?: morgan.Options): Handler;

  namespace morgan {
    /**
     * Define a custom token for use in morgan format strings.
     */
    function token(name: string, callback: (req: any, res: any, arg?: any) => string): morgan;

    /**
     * Options for the morgan middleware.
     */
    interface Options {
      /**
       * Whether to skip logging the request.
       */
      skip?: (req: any, res: any) => boolean;
      /**
       * Output stream for writing log lines.
       */
      stream?: { write: (str: string) => void };
      /**
       * Whether to log before response (true) or after (false).
       */
      immediate?: boolean;
    }
  }

  export = morgan;
}
