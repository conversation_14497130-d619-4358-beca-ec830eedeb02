import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Machine } from '../common/entity/machine.entity';
import { Order } from '../clientApp/order/order.entity';
import { Promotion } from '../common/entity/promotion.entity';
import { PromotionCode } from '../common/entity/promotionCode.entity';
import { FirestoreService } from '../common/module/firestore/firestore.service';
import { EOrderAppStatus, EPaymentMethod } from '../../enum/index';
import { PromotionService } from '../common/module/promotion/promotion.service';
import { PaymentAccountSetting } from '../common/entity/paymentAccountSetting.entity';
import { privateDecrypt } from 'crypto';
import { decodeBase62 } from '../common/module/cryptoUtil/cryptoUtil.service';
@Injectable()
export class AutobankService {
  constructor(
    @InjectRepository(Machine)
    private readonly machineRepository: Repository<Machine>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(PromotionCode)
    private readonly promotionCodeRepository: Repository<PromotionCode>,
    @InjectRepository(Promotion)
    private readonly promotionRepository: Repository<Promotion>,
    @InjectRepository(PaymentAccountSetting)
    private readonly paymentAccountSettingRepository: Repository<PaymentAccountSetting>,
    private readonly firestoreService: FirestoreService,
    private readonly promotionService: PromotionService,
  ) {}

  async callback(body: Record<string, any>) {
    try {
      console.log('=== AUTOBANK CALLBACK DEBUG v2 ===');
      console.log('1. Received body:', body);
      console.log('1a. Body.data type:', typeof body.data);
      console.log('1b. Body.data value:', body.data);

      let jsonData;

      // Check if data is already JSON (for testing)
      if (typeof body.data === 'object') {
        console.log('2. Using object data (test mode)');
        jsonData = body.data;
      } else if (typeof body.data === 'string' && body.data.startsWith('{')) {
        console.log('2. Using plain JSON string (test mode)');
        jsonData = JSON.parse(body.data);
      } else if (typeof body.data === 'string') {
        // Normal encrypted flow
        console.log('2. Decrypting data...');
        const decryptedData = privateDecrypt(
          process.env.AUTOBANK_PRIVATE_KEY.replace(/\\n/g, '\n'),
          Buffer.from(body.data, 'base64'),
        );
        console.log('2a. Decrypted data:', decryptedData.toString('utf-8'));
        jsonData = JSON.parse(decryptedData.toString('utf-8'));
      } else {
        throw new Error('Invalid data format');
      }

      console.log('3. Parsed JSON:', jsonData);

      let machineId;

      // Test mode: if refCode contains "TEST" or "ACTUAL", use hardcoded values
      if (
        jsonData.refCode &&
        (jsonData.refCode.includes('TEST') ||
          jsonData.refCode.includes('ACTUAL'))
      ) {
        console.log('4. Using TEST mode with hardcoded values');
        machineId =
          jsonData.machineId ||
          'test-machine-id-********-90ab-cdef-1234-567890abcdef';
        console.log('4a. Test machineId:', machineId);
      } else {
        // Normal flow: decode from refCode
        console.log('4. Decoding machineId from refCode...');
        const encryptedMachineId = jsonData.refCode.split(' ')[1];
        const machineIdBuffer = decodeBase62(encryptedMachineId);
        machineId = [
          machineIdBuffer.toString('hex').substring(0, 8),
          machineIdBuffer.toString('hex').substring(8, 12),
          machineIdBuffer.toString('hex').substring(12, 16),
          machineIdBuffer.toString('hex').substring(16, 20),
          machineIdBuffer.toString('hex').substring(20),
        ].join('-');
        console.log('4a. Decoded machineId:', machineId);
      }
      if (!machineId) {
        return { message: 'Success' };
      }
      const machine = await this.machineRepository.findOne({
        where: { id: machineId },
      });
      if (!machine) {
        return { message: 'Success' };
      }

      console.log('=== FIRESTORE SEARCH DEBUG ===');
      console.log('Collection (userId):', machine.userId);
      console.log('Document (machineId):', machineId);
      console.log('Expected refCode:', jsonData.refCode);

      // Fix: Get order directly by machineId instead of searching by refCode
      const orderDocument = await this.firestoreService.getDocument(
        machine.userId,
        machineId,
      );

      console.log('Found order document:', orderDocument);

      let deliveredOrders = [];
      if (orderDocument && orderDocument.refCode === jsonData.refCode) {
        deliveredOrders = [orderDocument];
        console.log('✅ RefCode matches! Order found and will be processed.');
      } else if (orderDocument) {
        console.log('❌ Order found but refCode mismatch:');
        console.log('    Expected:', jsonData.refCode);
        console.log('    Found:', orderDocument.refCode);
      } else {
        console.log('❌ No order document found in Firestore');
        console.log(
          '    This means order was not created yet or machineId is wrong',
        );
      }
      console.log('===============================');

      if (deliveredOrders.length) {
        console.log('🔄 Processing delivered orders...');
        await this.processDeliveredOrders(
          deliveredOrders,
          jsonData.amount,
          machine.userId,
        );
        console.log('✅ Finished processing orders');
      } else {
        console.log('⚠️ No orders to process');
      }
    } catch (error) {
      console.log('error', error);
    } finally {
      return { message: 'Success' };
    }
  }

  private async processDeliveredOrders(
    deliveredOrders: any[],
    receivedAmount: number,
    userId: string,
  ) {
    const order = deliveredOrders[0];
    if (!order) {
      return { message: 'Success' };
    }

    const orderId = await this.createDatabaseOrder(order, receivedAmount);
    await this.updateFirestoreOrder(order, receivedAmount, userId, orderId);
  }

  private async updateFirestoreOrder(
    order: any,
    receivedAmount: number,
    userId: string,
    orderId: string,
  ) {
    const updatedOrder = {
      ...order,
      orderId,
      domain: `${process.env.MEMORY_DOMAIN}${orderId}`,
      status: EOrderAppStatus.DELIVERED,
      receivedAmount,
    };

    try {
      await this.firestoreService.updateDocument(
        userId,
        order.id,
        updatedOrder,
      );
    } catch (error) {
      console.error(`Failed to update order ${order.id}:`, error);
    }
  }

  private async createDatabaseOrder(order: any, receivedAmount: number) {
    const newOrder = this.orderRepository.create({
      orderCode: order.orderCode,
      amount: order.amount,
      receivedAmount,
      totalOrderNumber: order.totalPriceAfterDiscount,
      description: order.description,
      refCode: order.refCode,
      status: EOrderAppStatus.DELIVERED,
      paymentMethod: EPaymentMethod.ONLINE,
      machineId: order.machineId,
      clientId: order.clientId,
      settingSizeId: order.settingSizeId,
      frameId: order.frameId,
      topicId: order.topicId,
      promotionId: order.promotionId == 'not found' ? null : order.promotionId,
      promotionCodeId:
        order.promotionCodeId == 'not found' ? null : order.promotionCodeId,
      imageNumber: order.imageNumber,
    });

    try {
      const savedOrder = await this.orderRepository.save(newOrder);
      if (order.promotionCodeId && order.promotionCodeId !== 'not found') {
        const promotionCodeRecord = await this.promotionCodeRepository.findOne({
          where: { id: order.promotionCodeId },
        });

        const promotion = await this.promotionRepository.findOne({
          where: { id: promotionCodeRecord.promotionId },
        });

        await this.promotionService.updatePromotionCode(
          promotionCodeRecord.id,
          promotionCodeRecord,
          promotion,
        );
      }

      return savedOrder.id;
    } catch (error) {
      console.error('Failed to create new order in database:', error);
    }
  }
}
