import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Machine } from '../common/entity/machine.entity';
import { Order } from '../clientApp/order/order.entity';
import { Promotion } from '../common/entity/promotion.entity';
import { PromotionCode } from '../common/entity/promotionCode.entity';
import { FirestoreService } from '../common/module/firestore/firestore.service';
import { EOrderAppStatus, EPaymentMethod } from '../../enum/index';
import { PromotionService } from '../common/module/promotion/promotion.service';
import { PaymentAccountSetting } from '../common/entity/paymentAccountSetting.entity';
import { privateDecrypt } from 'crypto';
import { decodeBase62 } from '../common/module/cryptoUtil/cryptoUtil.service';
@Injectable()
export class AutobankService {
  constructor(
    @InjectRepository(Machine)
    private readonly machineRepository: Repository<Machine>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(PromotionCode)
    private readonly promotionCodeRepository: Repository<PromotionCode>,
    @InjectRepository(Promotion)
    private readonly promotionRepository: Repository<Promotion>,
    @InjectRepository(PaymentAccountSetting)
    private readonly paymentAccountSettingRepository: Repository<PaymentAccountSetting>,
    private readonly firestoreService: FirestoreService,
    private readonly promotionService: PromotionService,
  ) {}

  async callback(body: Record<string, any>) {
    try {
      const decryptedData = privateDecrypt(
        process.env.AUTOBANK_PRIVATE_KEY.replace(/\\n/g, '\n'),
        Buffer.from(body.data, 'base64'),
      );
      console.log('--------------------------------------');
      console.log('decryptedData', decryptedData);
      console.log('--------------------------------------');

      const jsonData = JSON.parse(decryptedData.toString('utf-8'));
      // const encryptedClientId = jsonData.refCode.split(' ')[0];
      // const clientIdBuffer = decodeBase62(encryptedClientId);
      // const clientId = [
      //   clientIdBuffer.toString('hex').substring(0, 8),
      //   clientIdBuffer.toString('hex').substring(8, 12),
      //   clientIdBuffer.toString('hex').substring(12, 16),
      //   clientIdBuffer.toString('hex').substring(16, 20),
      //   clientIdBuffer.toString('hex').substring(20),
      // ].join('-');
      const encryptedMachineId = jsonData.refCode.split(' ')[1];
      const machineIdBuffer = decodeBase62(encryptedMachineId);
      const machineId = [
        machineIdBuffer.toString('hex').substring(0, 8),
        machineIdBuffer.toString('hex').substring(8, 12),
        machineIdBuffer.toString('hex').substring(12, 16),
        machineIdBuffer.toString('hex').substring(16, 20),
        machineIdBuffer.toString('hex').substring(20),
      ].join('-');
      console.log('jsonData', jsonData);
      console.log('machineId', machineId);
      if (!machineId) {
        return { message: 'Success' };
      }
      const machine = await this.machineRepository.findOne({
        where: { id: machineId },
      });
      if (!machine) {
        return { message: 'Success' };
      }

      const deliveredOrders =
        await this.firestoreService.getDocumentsByCondition(machine.userId, [
          {
            field: 'refCode',
            operator: '==',
            value: jsonData.refCode,
          },
        ]);

      if (deliveredOrders.length) {
        await this.processDeliveredOrders(
          deliveredOrders,
          jsonData.amount,
          machine.userId,
        );
      }
    } catch (error) {
      console.log('error', error);
    } finally {
      return { message: 'Success' };
    }
  }

  private async processDeliveredOrders(
    deliveredOrders: any[],
    receivedAmount: number,
    userId: string,
  ) {
    const order = deliveredOrders[0];
    if (!order) {
      return { message: 'Success' };
    }

    const orderId = await this.createDatabaseOrder(order, receivedAmount);
    await this.updateFirestoreOrder(order, receivedAmount, userId, orderId);
  }

  private async updateFirestoreOrder(
    order: any,
    receivedAmount: number,
    userId: string,
    orderId: string,
  ) {
    const updatedOrder = {
      ...order,
      orderId,
      domain: `${process.env.MEMORY_DOMAIN}${orderId}`,
      status: EOrderAppStatus.DELIVERED,
      receivedAmount,
    };

    try {
      await this.firestoreService.updateDocument(
        userId,
        order.id,
        updatedOrder,
      );
    } catch (error) {
      console.error(`Failed to update order ${order.id}:`, error);
    }
  }

  private async createDatabaseOrder(order: any, receivedAmount: number) {
    const newOrder = this.orderRepository.create({
      orderCode: order.orderCode,
      amount: order.amount,
      receivedAmount,
      totalOrderNumber: order.totalPriceAfterDiscount,
      description: order.description,
      refCode: order.refCode,
      status: EOrderAppStatus.DELIVERED,
      paymentMethod: EPaymentMethod.ONLINE,
      machineId: order.machineId,
      clientId: order.clientId,
      settingSizeId: order.settingSizeId,
      frameId: order.frameId,
      topicId: order.topicId,
      promotionId: order.promotionId == 'not found' ? null : order.promotionId,
      promotionCodeId:
        order.promotionCodeId == 'not found' ? null : order.promotionCodeId,
      imageNumber: order.imageNumber,
    });

    try {
      const savedOrder = await this.orderRepository.save(newOrder);
      if (order.promotionCodeId && order.promotionCodeId !== 'not found') {
        const promotionCodeRecord = await this.promotionCodeRepository.findOne({
          where: { id: order.promotionCodeId },
        });

        const promotion = await this.promotionRepository.findOne({
          where: { id: promotionCodeRecord.promotionId },
        });

        await this.promotionService.updatePromotionCode(
          promotionCodeRecord.id,
          promotionCodeRecord,
          promotion,
        );
      }

      return savedOrder.id;
    } catch (error) {
      console.error('Failed to create new order in database:', error);
    }
  }
}
