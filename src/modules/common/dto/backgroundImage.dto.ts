import { Field, InputType, ObjectType, Int } from '@nestjs/graphql';

@InputType()
export class BackgroundImageListInput {
  @Field(() => Int, { nullable: true, defaultValue: 50 })
  limit?: number;

  @Field(() => Int, { nullable: true, defaultValue: 0 })
  offset?: number;

  @Field({ nullable: true, defaultValue: 'createdAt' })
  orderBy?: string;

  @Field({ nullable: true, defaultValue: 'DESC' })
  orderDirection?: string;
}

@ObjectType()
export class BackgroundImageDto {
  @Field()
  id: string;

  @Field()
  fileName: string;

  @Field()
  fileUrl: string;

  @Field({ nullable: true })
  description?: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

@ObjectType()
export class BackgroundImageListResponse {
  @Field(() => [BackgroundImageDto])
  images: BackgroundImageDto[];

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  offset: number;

  @Field()
  message: string;
}

@ObjectType()
export class BackgroundImageDetailResponse {
  @Field(() => BackgroundImageDto, { nullable: true })
  image?: BackgroundImageDto;

  @Field()
  message: string;
}
