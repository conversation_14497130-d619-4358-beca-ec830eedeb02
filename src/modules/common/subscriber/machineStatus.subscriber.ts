import {
  DataSource,
  EntityManager,
  EntitySubscriberInterface,
  EventSubscriber,
  InsertEvent,
  UpdateEvent,
} from 'typeorm';
import { MachineStatus } from '../entity/machineStatus.entity';
import { Machine } from '../entity/machine.entity';
import { ELayoutType, ESettingSizeType } from 'src/enum';

@EventSubscriber()
export class MachineStatusSubscriber implements EntitySubscriberInterface<any> {
  constructor(private readonly dataSource: DataSource) {
    this.dataSource.subscribers.push(this); // Đăng ký subscriber
  }

  listenTo(): Function {
    return Object; // Lắng nghe tất cả các entity
  }

  /**
   * Xử lý sự kiện cập nhật
   */
  async afterUpdate(event: UpdateEvent<any>) {
    const tableName = event.metadata.tableName;
    console.log(tableName);
    const relevantTables = ['layout', 'setting_size', 'waiting_screen', 'appearance_setting'];
    if (!relevantTables.includes(tableName)) return;

    // L<PERSON>y machineIds dựa trên logic chung
    const machineIds = await this.resolveMachineIds(tableName, event.entity, event.manager);
    if (machineIds) {
      await this.handleMachineUpdate(machineIds, event.manager, event.entity.clientId);
    }
  }

  /**
   * Xử lý sự kiện thêm mới
   */
  async afterInsert(event: InsertEvent<any>): Promise<void> {
    const tableName = event.metadata.tableName;
    const relevantTables = ['layout', 'setting_size', 'waiting_screen', 'appearance_setting'];

    if (!relevantTables.includes(tableName)) return;

    // Lấy machineIds dựa trên logic chung
    const machineIds = await this.resolveMachineIds(tableName, event.entity, event.manager);
    if (machineIds) {
      await this.handleMachineUpdate(machineIds, event.manager, event.entity.clientId);
    }
  }

  /**
   * Lấy danh sách machineIds từ entity
   */
  private async resolveMachineIds(
    tableName: string,
    entity: any,
    manager: EntityManager,
  ): Promise<string[] | undefined> {
    switch (tableName) {
      case 'layout':
        return await this.getMachineIdsForEntity(entity, 'layoutType', ELayoutType.Default, manager);

      // case 'promotion':
      //   return entity?.machineIds?.split(',');

      case 'setting_size':
        return await this.getMachineIdsForEntity(entity, 'settingSizeType', ESettingSizeType.Default, manager);

      case 'waiting_screen':
        return await this.getMachineIdsForEntity(entity, 'type', ESettingSizeType.Default, manager);

      case 'appearance_setting':
        return await this.getMachineIds(entity?.clientId, manager);
      default:
        return undefined;
    }
  }

  /**
   * Lấy danh sách machineIds từ entity dựa trên type
   */
  private async getMachineIdsForEntity(
    entity: any,
    typeField: string,
    defaultValue: any,
    manager: EntityManager,
  ): Promise<string[] | undefined> {
    if (entity?.[typeField] === defaultValue) {
      const clientId = entity?.clientId;
      if (clientId) {
        return await this.getMachineIds(clientId, manager);
      }
    } else {
      return entity?.machineIds?.split(',');
    }
    return undefined;
  }

  /**
   * Cập nhật trạng thái isUpdated cho danh sách máy
   */
  private async handleMachineUpdate(machineIds: string[], manager: EntityManager, clientId: string) {
    if (machineIds && machineIds.length > 0) {
      await Promise.all(
        machineIds.map(async (machineId) => {
          if (machineId) {
            if (machineId) {
              const existingMachineStatus = await manager.getRepository(MachineStatus).findOneBy({ machineId });
    
              if (existingMachineStatus) {
                existingMachineStatus.isUpdated = true;
                await manager.getRepository(MachineStatus).save(existingMachineStatus);
              } else {
                await manager.getRepository(MachineStatus).insert({
                  machineId,
                  clientId,
                  isUpdated: true,
                });
              }
            }
          }
        }),
      );
    }
  }

  /**
   * Lấy danh sách machineIds từ clientId
   */
  private async getMachineIds(clientId: string, manager: EntityManager): Promise<string[]> {
    const machines = await manager
      .getRepository(Machine)
      .createQueryBuilder('machine')
      .select('machine.id')
      .where('machine.userId = :clientId', { clientId })
      .getMany();

    return machines.map((machine) => machine.id);
  }
}
