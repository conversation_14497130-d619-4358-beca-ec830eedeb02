import {
  EventSubscriber,
  EntitySubscriberInterface,
  UpdateEvent,
  EntityManager,
} from 'typeorm';
import { DataSource } from 'typeorm';
import { Layout } from '../entity/layout.entity';
import { MachineStatus } from '../entity/machineStatus.entity';

@EventSubscriber()
export class LayoutSubscriber implements EntitySubscriberInterface<Layout> {
  constructor(private readonly dataSource: DataSource) {
    // Đăng ký subscriber với DataSource
    this.dataSource.subscribers.push(this);
  }

  /**
   * Lắng nghe sự kiện từ bảng Topic và Frame
   */
  listenTo() {
    return Layout; // C<PERSON><PERSON> bảng cần lắng nghe
  }

  /**
   * Xử lý sự kiện update
   */
  async afterUpdate(event: UpdateEvent<any>) {
    // Lấy machineId từ entity
    const machineIds = event.entity?.machineids?.split(',');
    await this.handleMachineUpdate(machineIds, event.manager);
  }

  async afterCreate(event: UpdateEvent<any>) {
    const machineIds = event.entity?.machineids?.split(',');
    await this.handleMachineUpdate(machineIds, event.manager);
  }

  private async handleMachineUpdate(machineIds: string[], manager: EntityManager) {
    if (machineIds) {
      await Promise.all(
        machineIds.map(async (machineId) => {
          if (machineId) {
            await manager.getRepository(MachineStatus).upsert(
              { machineId, isUpdated: true },
              ['machineId'],
            );
          }
        }),
      );
    }
  }
}
