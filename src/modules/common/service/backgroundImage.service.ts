import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BackgroundImage } from '../entity/backgroundImage.entity';

export interface BackgroundImageListOptions {
  clientId: string;
  limit?: number;
  offset?: number;
  orderBy?: 'createdAt' | 'updatedAt' | 'fileName';
  orderDirection?: 'ASC' | 'DESC';
}

export interface BackgroundImageListResult {
  images: BackgroundImage[];
  total: number;
}

@Injectable()
export class CommonBackgroundImageService {
  private readonly logger = new Logger(CommonBackgroundImageService.name);

  constructor(
    @InjectRepository(BackgroundImage)
    private backgroundImageRepository: Repository<BackgroundImage>,
  ) {}

  /**
   * Get background images with pagination and sorting
   */
  async getBackgroundImages(
    options: BackgroundImageListOptions,
  ): Promise<BackgroundImageListResult> {
    try {
      const {
        clientId,
        limit = 50,
        offset = 0,
        orderBy = 'createdAt',
        orderDirection = 'DESC',
      } = options;

      this.logger.log(
        `Getting background images for client: ${clientId}, limit: ${limit}, offset: ${offset}`,
      );

      const queryBuilder = this.backgroundImageRepository
        .createQueryBuilder('backgroundImage')
        .where('backgroundImage.clientId = :clientId', { clientId })
        .orderBy(`backgroundImage.${orderBy}`, orderDirection);

      // Get total count
      const total = await queryBuilder.getCount();

      // Get paginated results
      const images = await queryBuilder.skip(offset).take(limit).getMany();

      this.logger.log(
        `Found ${images.length} background images out of ${total} total for client: ${clientId}`,
      );

      return {
        images,
        total,
      };
    } catch (error) {
      this.logger.error(`Failed to get background images: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get single background image by ID
   */
  async getBackgroundImageById(
    id: string,
    clientId: string,
  ): Promise<BackgroundImage> {
    try {
      this.logger.log(
        `Getting background image: ${id} for client: ${clientId}`,
      );

      const image = await this.backgroundImageRepository.findOne({
        where: { id, clientId },
      });

      if (!image) {
        throw new NotFoundException('Background image not found');
      }

      return image;
    } catch (error) {
      this.logger.error(`Failed to get background image: ${error.message}`);
      throw error;
    }
  }

  /**
   * Delete background image
   */
  async deleteBackgroundImage(id: string, clientId: string): Promise<boolean> {
    try {
      this.logger.log(
        `Deleting background image: ${id} for client: ${clientId}`,
      );

      const image = await this.backgroundImageRepository.findOne({
        where: { id, clientId },
      });

      if (!image) {
        throw new NotFoundException('Background image not found');
      }

      await this.backgroundImageRepository.delete({ id, clientId });

      this.logger.log(`Background image deleted successfully: ${id}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete background image: ${error.message}`);
      throw error;
    }
  }

  /**
   * Check if background image exists
   */
  async backgroundImageExists(id: string, clientId: string): Promise<boolean> {
    try {
      const count = await this.backgroundImageRepository.count({
        where: { id, clientId },
      });
      return count > 0;
    } catch (error) {
      this.logger.error(
        `Failed to check background image existence: ${error.message}`,
      );
      return false;
    }
  }

  /**
   * Get background images count for client
   */
  async getBackgroundImagesCount(clientId: string): Promise<number> {
    try {
      return await this.backgroundImageRepository.count({
        where: { clientId },
      });
    } catch (error) {
      this.logger.error(
        `Failed to get background images count: ${error.message}`,
      );
      return 0;
    }
  }
}
