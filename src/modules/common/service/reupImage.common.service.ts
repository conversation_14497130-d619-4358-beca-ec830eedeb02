import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SBLogger } from '../../logger/logger.service';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { Order } from 'src/modules/clientApp/order/order.entity';
import { AppImage } from 'src/modules/clientApp/uploadImage/image.entity';
import { Frame } from 'src/modules/common/entity/frame.entity';
import { isTimeInRange } from 'src/utils/time.utils';
import { canReuploadImages } from 'src/utils/image.utils';

/**
 * Common service for reupload image functionality
 * Can be used by both client and admin modules
 */
@Injectable()
export class ReupImageCommonService {
  constructor(
    @InjectRepository(Machine)
    private readonly machineRepository: Repository<Machine>,

    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,

    @InjectRepository(AppImage)
    private readonly imageRepository: Repository<AppImage>,

    @InjectRepository(Frame)
    private readonly frameRepository: Repository<Frame>,

    private readonly loggerService: SBLogger,
    private readonly firestoreService: FirestoreService,
  ) {}

  /**
   * Process a request to reupload images for an order
   * @param machineId ID of the machine
   * @param orderId ID of the order
   * @param requesterId ID of the user making the request (client or admin)
   * @param isAdmin Whether the request is coming from an admin
   * @returns Message indicating the result of the operation
   */
  async processReuploadRequest(
    machineId: string,
    orderId: string,
    requesterId: string,
    isAdmin: boolean = false,
  ): Promise<{ message: string }> {
    try {
      // Fast fail if any required data is missing
      if (!machineId || !orderId) {
        throw new BadRequestException(
          'Missing required data: machineId or orderId',
        );
      }

      // For client requests, verify machine ownership
      // For admin requests, skip ownership check
      let machine: Machine | null = null;

      if (!isAdmin) {
        // Client request - check machine ownership
        machine = await this.machineRepository.findOne({
          where: { userId: requesterId, id: machineId },
        });

        if (!machine) {
          throw new NotFoundException(
            'Machine not found or not owned by this user.',
          );
        }
      } else {
        // Admin request - just check if machine exists
        machine = await this.machineRepository.findOne({
          where: { id: machineId },
        });

        if (!machine) {
          throw new NotFoundException('Machine not found.');
        }
      }

      // Get order with frame and images in a single query
      const order = await this.orderRepository.findOne({
        where: { id: orderId },
        relations: ['frame', 'images'],
      });

      // Validate order exists
      if (!order) {
        throw new NotFoundException('Order not found.');
      }

      // Validate frame exists
      const frame = order.frame;
      if (!frame) {
        throw new NotFoundException(
          'Frame information not found for this order.',
        );
      }

      // Check if the time is within the allowed range (after 15 minutes and before 72 hours)
      // Skip time validation for admin requests
      if (!isAdmin) {
        const timeValidation = isTimeInRange(order.createdAt, 15, 72);
        if (!timeValidation.isValid) {
          throw new BadRequestException(timeValidation.reason);
        }
      }

      // Use the images from the order relation
      const images = order.images || [];

      // Check if reupload is allowed based on image count and frame requirements
      const imageValidation = canReuploadImages(images, frame, orderId);
      if (!imageValidation.isValid) {
        throw new BadRequestException(imageValidation.reason);
      }

      // Log the reason why reupload is allowed
      if (imageValidation.message) {
        this.loggerService.log(imageValidation.message);
      }

      // Prepare data and push to Firestore
      const dataToSave = {
        machineId,
        orderId,
        status: true,
        createdAt: new Date().toISOString(),
        requestedBy: isAdmin ? 'admin' : 'client',
        requesterId,
      };

      try {
        // Push to Firestore with machineId as document ID, using merge=true to update existing document
        await this.firestoreService.pushData(
          'ImageReup',
          machineId,
          dataToSave,
          true,
        );

        this.loggerService.log(
          `Successfully created reupload job for machine ${machineId} and order ${orderId}`,
        );
      } catch (pushError) {
        this.loggerService.log(
          `Error pushing data to Firestore: ${pushError.message}`,
          { machineId, orderId, dataToSave, error: pushError },
        );
        throw new BadRequestException(
          'Failed to create reupload job. Please try again later.',
        );
      }

      return { message: 'Reupload job successfully created.' };
    } catch (error) {
      // Add context to the error for better debugging
      const errorContext = {
        requesterId,
        machineId,
        orderId,
        isAdmin,
        timestamp: new Date().toISOString(),
      };

      // Log the error with context
      this.loggerService.log(
        `Error in reupImage process: ${error.message}`,
        errorContext,
      );

      // Rethrow known exceptions directly
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }

      // For other errors, wrap in a BadRequestException
      throw new BadRequestException(
        error.message ||
          'Failed to create reupload job due to an internal error.',
      );
    }
  }
}
