import {
  <PERSON>umn,
  CreateDate<PERSON>olumn,
  DeleteDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { FrameItem } from './frameItem.entity';
import { Topic } from './topic.entity';
import { EFrameOrientation, EFrameSize } from '../../../enum';
import { Order } from '../../clientApp/order/order.entity';

@Entity()
@ObjectType()
export class Frame {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column()
  @Field({ nullable: true })
  clientId: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  description: string;

  @Column()
  @Field({ nullable: true })
  imageUrl: string;

  @Column()
  @Field({ nullable: true })
  frameSize: EFrameSize;

  @Column()
  @Field({ nullable: true })
  numberImage: number;

  @Column()
  @Field({ nullable: true })
  numberPicture: number;

  @Column()
  @Field({ nullable: true })
  topicId: string;

  @Column({ default: EFrameOrientation.Horizontal })
  @Field({ nullable: true })
  orientation: EFrameOrientation;

  @Column()
  @Field({ nullable: true })
  dateColor: string;

  @CreateDateColumn()
  @Field({ nullable: true })
  createdAt: string;

  @UpdateDateColumn()
  @Field({ nullable: true })
  updatedAt: string;

  @DeleteDateColumn()
  @Field({ nullable: true })
  deletedAt: string;

  @OneToMany(() => FrameItem, (frameItem) => frameItem.frame, { cascade: true })
  @Field(() => [FrameItem], { nullable: true })
  frameItems: FrameItem[];

  @ManyToOne(() => Topic, (topic) => topic.frames)
  topic: Topic;

  @OneToMany(() => Order, (order) => order.frame)
  orders: Order[];
}
