import { Field, ID, ObjectType } from '@nestjs/graphql';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { EPrintSettingType } from '../../../enum';

@Entity('print_setting')
@ObjectType()
export class PrintSetting {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => ID)
  id: string;

  @Column()
  @Field()
  clientId: string;

  @Column({ nullable: true, length: 36 })
  @Field({ nullable: true })
  machineId: string;

  @Column({ nullable: false })
  @Field()
  type: EPrintSettingType;

  @Column({ nullable: true })
  @Field({ nullable: true })
  adjustColor: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  printRetry: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  border: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  sharpness: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  overcoatFinish: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  printerQuality: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  yResolution: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  adjGammaR: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  adjGammaG: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  adjGammaB: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  adjBrightnessR: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  adjBrightnessG: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  adjBrightnessB: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  adjContrastR: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  adjContrastG: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  adjContrastB: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  adjChroma: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  icmMethod: number;

  @Column()
  @Field()
  createdAt: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  updatedAt: string;
}
