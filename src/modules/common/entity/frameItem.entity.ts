import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { Frame } from './frame.entity';
import { EFrameItemType } from '../../../enum';

@Entity()
@ObjectType()
export class FrameItem {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column()
  @Field()
  frameId: string;

  @Column()
  @Field()
  itemType: EFrameItemType;

  @Column({ nullable: true })
  @Field({ nullable: true })
  itemId?: string;

  @Column()
  @Field()
  x_coordinate: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  parentId?: string;

  @Column()
  @Field()
  y_coordinate: number;

  @Column()
  @Field()
  width: number;

  @Column()
  @Field()
  height: number;

  @Column({ type: 'int', default: 0 })
  @Field()
  position: number;

  @Column({ type: 'float', nullable: true })
  @Field({ nullable: true })
  angle?: number;

  @CreateDateColumn()
  @Field()
  createdAt: string;

  @UpdateDateColumn()
  @Field({ nullable: true })
  updatedAt: string;

  @ManyToOne(() => Frame, (frame) => frame.frameItems, { onDelete: 'CASCADE' })
  @Field(() => Frame)
  frame: Frame;
}
