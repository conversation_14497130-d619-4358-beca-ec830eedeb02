import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from 'typeorm';
import { Field, ObjectType, ID } from '@nestjs/graphql';
import { Client } from './client.entity';

@ObjectType()
@Entity('background_image')
export class BackgroundImage {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field()
  @Column({ type: 'varchar', length: 255 })
  fileName: string;

  @Field()
  @Column({ type: 'varchar', length: 500 })
  fileUrl: string;

  @Field({ nullable: true })
  @Column({ type: 'varchar', length: 100, nullable: true })
  description?: string;

  @Field()
  @Column({ type: 'uuid' })
  clientId: string;

  @Field()
  @CreateDateColumn()
  createdAt: Date;

  @Field()
  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Client, { onDelete: 'CASCADE' })
  client: Client;
}
