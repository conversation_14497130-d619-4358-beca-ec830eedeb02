import { Field, ObjectType } from '@nestjs/graphql';
import { EMachineStatus, EProductionStatus } from '../../../enum';
import { MachineBrand } from '../../admin/machineBrand/machineBrand.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { Client } from './client.entity';
import { Order } from '../../clientApp/order/order.entity';
import { MachineStatus } from './machineStatus.entity';

@Entity()
@ObjectType()
export class Machine {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column()
  @Field()
  price: number;

  @Column()
  @Field()
  machineId: string;

  @Column()
  @Field()
  machineCode: string;

  @Column()
  @Field()
  machinePin: string;

  @Column()
  @Field({ nullable: true })
  remotePin?: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  userId?: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  orderId?: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  rentalDate: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  renewalDate: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  havePayScreen: boolean;

  @Column({ nullable: true, default: EProductionStatus.IN_PRODUCTION })
  @Field({ nullable: true })
  productionStatus: EProductionStatus;

  @Column({ nullable: true, default: EMachineStatus.INACTIVE })
  @Field({ nullable: true })
  status: EMachineStatus;

  @Column({ nullable: false })
  @Field({ nullable: false })
  expiryDate: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  location: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  lastPingAt: string;

  @Column({ nullable: true })
  @Field({ nullable: true, defaultValue: 0 })
  pendingPrints: number;

  @Column({ nullable: true })
  @Field({ nullable: true, defaultValue: 0 })
  remainingMedia: number;

  @Column({ nullable: true })
  @Field({ nullable: true, defaultValue: 0 })
  remainingInk: number;

  @Column({ nullable: true })
  @Field({ nullable: true })
  state: string;

  @Column({ nullable: true })
  @Field({ nullable: true, defaultValue: true })
  hasPayment: boolean;

  @Column({ nullable: true })
  @Field({ nullable: true })
  description: string;

  @Column()
  @Field()
  createdAt: string;

  @Column()
  @Field()
  createdBy: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  updatedAt: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  updatedBy: string;

  @ManyToOne(() => MachineBrand, (machineBrand) => machineBrand.machines)
  @Field(() => MachineBrand)
  machineBrand: MachineBrand;

  @OneToMany(() => Order, (order) => order.machine)
  @Field(() => [Order], { nullable: true })
  orders: Order[];

  @OneToOne(() => MachineStatus, (machineStatus) => machineStatus.machine)
  machineStatus: MachineStatus;
}

@ObjectType()
export class MachineResponse {
  @Field()
  id: string;

  @Field()
  price: number;

  @Field({ nullable: true })
  machineBrand: MachineBrand;

  @Field()
  machineCode: string;

  @Field()
  machinePin: string;

  @Column()
  @Field({ nullable: true })
  remotePin?: string;

  @Field({ nullable: true })
  user?: Client;

  @Field({ nullable: true })
  orderId?: string;

  @Field({ nullable: true })
  rentalDate: string;

  @Field({ nullable: true })
  renewalDate: string;

  @Field({ nullable: true })
  havePayScreen: boolean;

  @Field({ nullable: true })
  productionStatus: EProductionStatus;

  @Field({ nullable: true })
  status: EMachineStatus;

  @Field({ nullable: false })
  expiryDate: string;

  @Field({ nullable: true })
  lastPingAt: string;

  @Field({ nullable: true })
  pendingPrints: number;

  @Field({ nullable: true })
  remainingMedia: number;

  @Field({ nullable: true })
  remainingInk: number;

  @Field({ nullable: true })
  state: string;

  @Field({ nullable: true })
  hasPayment: boolean;

  @Field({ nullable: true })
  description: string;

  @Field({ nullable: true })
  location: string;

  @Field()
  createdAt: string;

  @Field()
  createdBy: string;

  @Field({ nullable: true })
  updatedAt: string;

  @Field({ nullable: true })
  updatedBy: string;
}
