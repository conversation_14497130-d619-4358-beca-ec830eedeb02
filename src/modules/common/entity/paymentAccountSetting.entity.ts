import { Field, ID, ObjectType } from '@nestjs/graphql';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { MBank } from './mBank.entity';
import { EPaymentAccountSettingType } from '../../../modules/admin/paymentAccountSetting/paymentAccountSetting.input';
@ObjectType()
@Entity('payment_account_setting')
export class PaymentAccountSetting {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field()
  @Column({ type: 'varchar', length: 255 })
  bankName: string;

  @Field({ nullable: true })
  @Column({ type: 'varchar', length: 255, nullable: true })
  apiId: string;

  @Field({ nullable: true })
  @Column({ type: 'varchar', length: 255, nullable: true })
  apiKey: string;

  @Field({ nullable: true })
  @Column({ type: 'varchar', length: 255, nullable: true })
  checkSum: string;

  @Field({ nullable: true })
  @Column({ type: 'varchar', length: 255, nullable: true })
  mBankId: string;

  @Field({ nullable: true })
  @Column({ type: 'varchar', length: 255, nullable: true })
  bankOwnerName: string;

  @Field({ nullable: true })
  @Column({ type: 'varchar', length: 255, nullable: true })
  bankAccountNumber: string;

  @Field()
  @Column({ type: 'boolean', default: false })
  isActive: boolean;

  @Field()
  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Field({ nullable: true })
  @Column({ type: 'varchar', length: 255, nullable: true })
  type: EPaymentAccountSettingType;

  @Field()
  @Column({
    type: 'uuid',
    nullable: false,
    default: '',
  })
  clientId: string;

  @Field()
  @CreateDateColumn()
  createdAt: Date;

  @Field()
  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => MBank, (mbank) => mbank.paymentAccountSetting)
  @JoinColumn({ name: 'mBankId' })
  @Field(() => MBank, { nullable: true })
  mBank: MBank;
}
