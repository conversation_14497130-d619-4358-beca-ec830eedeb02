import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { ObjectType, Field, ID } from '@nestjs/graphql';
import { LayoutItem } from './layoutItem.entity';
import { Frame } from './frame.entity';
import { Order } from '../../clientApp/order/order.entity';

@Entity()
@ObjectType()
export class Topic {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => ID)
  id: string;

  @Column()
  @Field()
  name: string;

  @Column('float', { nullable: true })
  @Field()
  extraFee: number;

  @Column({ default: true })
  @Field()
  isActive: boolean;

  @Column()
  @Field()
  clientId: string;

  @CreateDateColumn()
  @Field()
  createdAt: Date;

  @UpdateDateColumn({ nullable: true })
  @Field({ nullable: true })
  updatedAt?: Date;

  @OneToMany(() => LayoutItem, (layoutItem) => layoutItem.topic, {
    cascade: true,
  })
  layoutItems: LayoutItem[];

  @OneToMany(() => Frame, (frame) => frame.topic, { cascade: true })
  frames: Frame[];

  @OneToMany(() => Order, (order) => order.topic)
  orders: Order[];

  @Field({ nullable: true })
  get totalFrame(): number {
    return this.frames ? this.frames.length : 0;
  }
}
