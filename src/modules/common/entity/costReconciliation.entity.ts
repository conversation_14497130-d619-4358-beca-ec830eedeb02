import { Field, ObjectType, ID } from '@nestjs/graphql';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { ECostReconciliationStatus } from '../../../enum';
import { Client } from './client.entity';

@Entity('cost_reconciliation')
@ObjectType()
export class CostReconciliation {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field()
  @Column({ type: 'varchar', length: 255 })
  clientId: string;

  @Field()
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  revenue: number;

  @Field()
  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  discountAmount: number;

  @Field()
  @Column({
    type: 'enum',
    enum: ECostReconciliationStatus,
    default: ECostReconciliationStatus.NOT_RECONCILED,
  })
  status: ECostReconciliationStatus;

  @Field()
  @Column({ type: 'int', default: 0 })
  totalOrders: number;

  @Field()
  @Column({ type: 'datetime' })
  reconciliationDate: Date;

  @Field()
  @CreateDateColumn()
  createdAt: Date;

  @Field()
  @UpdateDateColumn()
  updatedAt: Date;

  @Field({ nullable: true })
  @Column({ type: 'varchar', length: 255, nullable: true })
  createdBy: string;

  @Field({ nullable: true })
  @Column({ type: 'varchar', length: 255, nullable: true })
  updatedBy: string;

  @Field({ nullable: true })
  @Column({ type: 'text', nullable: true })
  notes: string;

  @Field({ nullable: true })
  @Column({
    type: 'longtext',
    nullable: true,
    comment:
      'JSON array of all orderApp IDs that have been processed and added to this reconciliation',
  })
  processedOrderIds?: string;

  @Field()
  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    comment: 'Unique code generated for this cost reconciliation record',
  })
  reconciliationCode: string;

  // Relation to Client
  @ManyToOne(() => Client, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'clientId' })
  @Field(() => Client, { nullable: true })
  client: Client;
}
