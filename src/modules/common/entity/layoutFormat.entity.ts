import {
  CreateDateColumn,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Entity, ManyToOne } from 'typeorm';

import { Column } from 'typeorm';
import { Layout } from './layout.entity';
import { Field } from '@nestjs/graphql';
import { ObjectType } from '@nestjs/graphql';
import { LayoutItem } from './layoutItem.entity';
import { ELayoutFormatStatus } from '../../../enum';

@Entity()
@ObjectType()
export class LayoutFormat {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  layoutId: string;

  @Column({ type: 'varchar', length: 50 })
  @Field()
  name: string;

  @Column({ type: 'int' })
  @Field()
  imageCount: number;

  @Column({ default: ELayoutFormatStatus.INACTIVE })
  @Field({ nullable: true })
  status: ELayoutFormatStatus;

  @CreateDateColumn()
  @Field()
  createdAt: string;

  @UpdateDateColumn()
  @Field({ nullable: true })
  updatedAt: string;

  @ManyToOne(() => Layout, (layout) => layout.formats, { onDelete: 'CASCADE' })
  @Field(() => Layout, { nullable: true })
  layout: Layout;

  @OneToMany(() => LayoutItem, (layoutItem) => layoutItem.layoutFormat, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  @Field(() => [LayoutItem])
  layoutItems: LayoutItem[];
}
