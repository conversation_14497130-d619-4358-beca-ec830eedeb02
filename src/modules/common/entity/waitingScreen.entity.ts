import { Field, ObjectType } from '@nestjs/graphql';
import { EWaitingScreenType } from '../../../enum';
import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { WaitingScreenImage } from './waitingScreenImage.entity';

@Entity()
@ObjectType()
export class WaitingScreen {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  name: string;

  @Column()
  @Field()
  clientId: string;

  @Field(() => [WaitingScreenImage])
  @OneToMany(() => WaitingScreenImage, (image) => image.waitingScreen, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  images: WaitingScreenImage[];

  @Column({ nullable: true, length: 10000 })
  @Field({ nullable: true })
  machineIds: string;

  @Column({ nullable: false })
  @Field()
  type: EWaitingScreenType;

  @Column()
  @Field()
  createdAt: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  updatedAt: string;
}
