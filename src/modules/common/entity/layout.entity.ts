import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { ELayoutStatus, ELayoutType } from '../../../enum';
import { LayoutFormat } from './layoutFormat.entity';

@Entity()
@ObjectType()
export class Layout {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  name: string;

  @Column()
  @Field()
  clientId: string;

  @Column({ nullable: true, length: 10000 })
  @Field({ nullable: true })
  machineIds: string;

  @Column({ default: ELayoutStatus.ACTIVE })
  @Field()
  status: ELayoutStatus;

  @Column({ default: ELayoutType.Default })
  @Field()
  layoutType: ELayoutType;

  @CreateDateColumn()
  @Field()
  createdAt: string;

  @UpdateDateColumn()
  @Field({ nullable: true })
  updatedAt: string;

  @OneToMany(() => LayoutFormat, (format) => format.layout, {
    cascade: true,
    onDelete: 'CASCADE',
  })
  @Field(() => [LayoutFormat], { nullable: true })
  formats: LayoutFormat[];
}
