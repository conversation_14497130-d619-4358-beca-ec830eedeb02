import {
  Column,
  CreateDateColumn,
  Entity,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { Promotion } from './promotion.entity';
import { Order } from '../../clientApp/order/order.entity';

@Entity()
@ObjectType()
export class PromotionCode {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column()
  @Field({ nullable: true })
  promotionId: string;

  @ManyToOne(() => Promotion, (promotion) => promotion.codes, {
    onDelete: 'CASCADE',
  })
  @Field(() => Promotion, { nullable: true })
  promotion: Promotion;

  @Column({ type: 'varchar', length: 8 })
  @Field({ nullable: true })
  code: string;

  @Column({ default: false })
  @Field({ nullable: true })
  isUsed: boolean;

  @Column({ default: 0 })
  @Field({ nullable: true })
  numberUsed: number;

  @CreateDateColumn()
  @Field({ nullable: true })
  createdAt: string;

  @UpdateDateColumn()
  @Field({ nullable: true })
  updatedAt: string;

  @OneToMany(() => Order, (order) => order.frame)
  orders: Order[];
}
