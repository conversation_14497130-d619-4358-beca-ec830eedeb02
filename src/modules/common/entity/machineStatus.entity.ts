import { Field, ObjectType } from '@nestjs/graphql';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { Machine } from './machine.entity';
import { EMachineStatus } from '../../../enum';

@Entity()
@ObjectType()
export class MachineStatus {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column()
  @Field({ nullable: true })
  machineId: string;

  @Column({ default: false })
  @Field({ nullable: true })
  isUpdated: boolean;

  @Column({ nullable: true })
  @Field({ nullable: true })
  syncDate: string;

  @Column()
  @Field({ nullable: true })
  clientId: string;

  @Column({ default: EMachineStatus.WAITING })
  @Field({ nullable: true })
  status: EMachineStatus;

  @CreateDateColumn()
  @Field({ nullable: true })
  createdAt: Date;

  @UpdateDateColumn()
  @Field({ nullable: true })
  updatedAt: Date;

  @OneToOne(() => Machine, (machine) => machine.machineStatus, { eager: false })
  @JoinColumn({ name: 'machineId' })
  @Field(() => Machine, { nullable: true })
  machine: Machine;
}
