import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { WaitingScreen } from './waitingScreen.entity';

@ObjectType()
@Entity()
export class WaitingScreenImage {
  @Field()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field()
  @Column()
  url: string;

  @Field(() => WaitingScreen)
  @ManyToOne(() => WaitingScreen, (waitingScreen) => waitingScreen.images, {
    onDelete: 'CASCADE',
    })
  waitingScreen: WaitingScreen;

  @CreateDateColumn()
  @Field()
  createdAt: Date;

  @UpdateDateColumn({ nullable: true })
  @Field({ nullable: true })
  updatedAt?: Date;
}
