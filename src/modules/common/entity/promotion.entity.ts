import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { PromotionCode } from './promotionCode.entity';

@ObjectType()
export class PromotionStatistics {
  @Field()
  totalOrders: number;

  @Field()
  totalRevenue: number;
}

@Entity()
@ObjectType()
export class Promotion {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column()
  @Field()
  clientId: string;

  @Column()
  @Field()
  name: string;

  @Column({ nullable: true, length: 10000 })
  @Field({ nullable: true })
  machineIds?: string;

  @Column()
  @Field()
  startDate: string;

  @Column()
  @Field()
  endDate: string;

  @Column({ default: 1 })
  @Field()
  usageLimitPerCode: number;

  @Column({ default: 1 })
  @Field()
  maxPromotionCodes: number;

  @Column({ default: 0 })
  @Field()
  discountValue: number;

  @Column()
  @Field()
  promotionCode: string;

  @CreateDateColumn()
  @Field()
  createdAt: string;

  @UpdateDateColumn()
  @Field()
  updatedAt: string;

  @OneToMany(() => PromotionCode, (code) => code.promotion)
  codes: PromotionCode[];
}

@ObjectType()
export class PromotionResponse {
  @Field()
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  machineIds?: string;

  @Field({ nullable: true })
  startDate?: string;

  @Field({ nullable: true })
  endDate?: string;

  @Field({ nullable: true })
  usageLimitPerCode?: number;

  @Field({ nullable: true })
  maxPromotionCodes?: number;

  @Field({ nullable: true })
  discountValue?: number;

  @Field({ nullable: true })
  promotionCode?: string;

  @Field({ nullable: true })
  createdAt?: string;

  @Field({ nullable: true })
  updatedAt?: string;

  @Field(() => [PromotionCode])
  codes?: PromotionCode[];

  @Field(() => PromotionStatistics, { nullable: true })
  statistics?: PromotionStatistics;
}

@ObjectType()
export class ExportPromotionResponse {
  @Field()
  url: string;
}
