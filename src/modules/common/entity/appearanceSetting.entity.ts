import { Field, ObjectType } from '@nestjs/graphql';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity()
@ObjectType()
export class AppearanceSetting {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  logo: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  background: string;

  @Column({ type: 'varchar', length: 7 })
  @Field()
  primary_color: string;

  @Column({ type: 'varchar', length: 7 })
  @Field()
  secondary_color: string;

  @Column({ type: 'varchar', length: 7 })
  @Field()
  background_color: string;

  @Column({ type: 'varchar', length: 7 })
  @Field()
  primary_text_color: string;

  @Column({ type: 'varchar', length: 7 })
  @Field()
  secondary_text_color: string;

  @Column({ type: 'varchar', length: 7 })
  @Field()
  secondary_text_color_2: string;

  @Column()
  @Field()
  clientId: string;

  @CreateDateColumn()
  @Field()
  createdAt: Date;

  @UpdateDateColumn()
  @Field()
  updatedAt: Date;
}
