import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
  OneToMany,
} from 'typeorm';
import { ObjectType, Field, Int } from '@nestjs/graphql';
import { ESettingSizeStatus, ESettingSizeType } from '../../../enum';
import { Order } from '../../clientApp/order/order.entity';

@Entity()
@ObjectType()
@Unique(['name', 'clientId'])
export class SettingSize {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  name: string;

  @Column({ default: ESettingSizeType.Default })
  @Field()
  settingSizeType: string;

  @Column({ nullable: true, length: 10000 })
  @Field(() => String, { nullable: true })
  machineIds?: string;

  @Column()
  @Field(() => String)
  clientId: string;

  // Small size prices for fixed quantities
  @Column({ nullable: true, default: 0 })
  @Field(() => Int, { nullable: true })
  smallSizePrice2?: number;

  @Column({ nullable: true, default: 0 })
  @Field(() => Int, { nullable: true })
  smallSizePrice4?: number;

  @Column({ nullable: true, default: 0 })
  @Field(() => Int, { nullable: true })
  smallSizePrice6?: number;

  @Column({ nullable: true, default: 0 })
  @Field(() => Int, { nullable: true })
  smallSizePrice8?: number;

  @Column({ nullable: true, default: 0 })
  @Field(() => Int, { nullable: true })
  smallSizePrice10?: number;

  // Large size prices for fixed quantities
  @Column({ nullable: true, default: 0 })
  @Field(() => Int, { nullable: true })
  largeSizePrice2?: number;

  @Column({ nullable: true, default: 0 })
  @Field(() => Int, { nullable: true })
  largeSizePrice3?: number;

  @Column({ nullable: true, default: 0 })
  @Field(() => Int, { nullable: true })
  largeSizePrice4?: number;

  @Column({ nullable: true, default: 0 })
  @Field(() => Int, { nullable: true })
  largeSizePrice5?: number;

  @Column({ nullable: true, default: 0 })
  @Field(() => Int, { nullable: true })
  largeSizePrice6?: number;

  @Column({ nullable: true, default: ESettingSizeStatus.ACTIVE })
  @Field({ nullable: true })
  status?: ESettingSizeStatus;

  @CreateDateColumn()
  @Field()
  createdAt: Date;

  @UpdateDateColumn({ nullable: true })
  @Field({ nullable: true })
  updatedAt?: Date;

  @OneToMany(() => Order, (order) => order.settingSize)
  orders: Order[];
}
