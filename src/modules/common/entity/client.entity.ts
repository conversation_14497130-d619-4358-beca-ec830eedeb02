import { Field, ObjectType } from '@nestjs/graphql';
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity()
@ObjectType()
export class Client {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column()
  @Field()
  name: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  ownerName?: string;

  @Column()
  @Field()
  email: string;

  @Column()
  @Field()
  password?: string;

  @Column()
  @Field()
  phone: string;

  @Column()
  @Field()
  province: string;

  @Column()
  @Field()
  address: string;

  @Column()
  @Field()
  createdAt: string;

  @Column()
  @Field()
  createdBy: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  updatedAt: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  updatedBy: string;

  @Column({ default: 0 })
  @Field()
  totalOrders: number;

  @Column({ default: 0 })
  @Field()
  totalMachines: number;
}
