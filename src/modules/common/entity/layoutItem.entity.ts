import {
  Column,
  Create<PERSON>ate<PERSON><PERSON>umn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  ManyToOne,
} from 'typeorm';
import { Entity } from 'typeorm';
import { Field, ObjectType } from '@nestjs/graphql';
import { LayoutFormat } from './layoutFormat.entity';
import { Topic } from './topic.entity';
@Entity()
@ObjectType()
export class LayoutItem {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column()
  @Field()
  layoutFormatId: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  imageUrl: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  topicId: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  position: number;

  @CreateDateColumn()
  @Field()
  createdAt: string;

  @UpdateDateColumn()
  @Field({ nullable: true })
  updatedAt: string;

  @ManyToOne(() => LayoutFormat, (format) => format.layoutItems, { onDelete: 'CASCADE' })
  @Field(() => LayoutFormat, { nullable: true })
  layoutFormat: LayoutFormat;

  @ManyToOne(() => Topic, (topic) => topic.layoutItems)
  @Field(() => Topic, { nullable: true })
  topic: Topic;
}
