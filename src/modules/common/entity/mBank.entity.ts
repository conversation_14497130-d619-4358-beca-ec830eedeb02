import { Field, ID, ObjectType } from '@nestjs/graphql';
import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { PaymentAccountSetting } from './paymentAccountSetting.entity';

@Entity('m_bank')
@ObjectType()
export class MBank {
  @PrimaryGeneratedColumn('uuid')
  @Field(() => ID)
  id: string;

  @Column()
  @Field()
  code: string;

  @Column({ nullable: true, length: 36 })
  @Field({ nullable: true })
  shortName: string;

  @Column({ nullable: false })
  @Field()
  name: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  logo: string;

  @OneToMany(
    () => PaymentAccountSetting,
    (paymentAccountSetting) => paymentAccountSetting.mBank,
  )
  @Field(() => [PaymentAccountSetting], { nullable: true })
  paymentAccountSetting: PaymentAccountSetting[];
}
