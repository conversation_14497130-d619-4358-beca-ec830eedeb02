import { Field, ObjectType } from '@nestjs/graphql';
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity()
@ObjectType()
export class Auth {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column()
  @Field()
  email: string;

  @Column({ type: 'longtext' })
  @Field()
  token: string;

  @Column()
  @Field()
  createdAt: string;

  @Column({ default: true })
  @Field({ nullable: true })
  role: string;
}
