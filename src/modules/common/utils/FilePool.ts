import * as fs from 'fs';
import { Logger } from '@nestjs/common';

export class FilePool {
  private readonly logger = new Logger(FilePool.name);
  private pool = new Map<
    string,
    {
      path: string;
      refCount: number;
      lastAccessed: Date;
    }
  >();

  private cleanupInterval: NodeJS.Timeout;
  private readonly maxIdleTime = 5 * 60 * 1000; // 5 minutes

  constructor() {
    // Start cleanup interval
    this.cleanupInterval = setInterval(() => {
      this.cleanupIdleFiles();
    }, 60 * 1000); // Check every minute
  }

  async getFile(key: string): Promise<string | null> {
    const file = this.pool.get(key);
    if (file) {
      file.refCount++;
      file.lastAccessed = new Date();
      this.logger.debug(
        `Increased refCount for file ${key} to ${file.refCount}`,
      );
      return file.path;
    }
    return null;
  }

  async addFile(key: string, path: string): Promise<void> {
    if (!this.pool.has(key)) {
      this.pool.set(key, {
        path,
        refCount: 1,
        lastAccessed: new Date(),
      });
      this.logger.debug(`Added new file ${key} to pool`);
    }
  }

  async releaseFile(key: string): Promise<void> {
    const file = this.pool.get(key);
    if (file) {
      file.refCount--;
      file.lastAccessed = new Date();
      this.logger.debug(
        `Decreased refCount for file ${key} to ${file.refCount}`,
      );

      if (file.refCount === 0) {
        await this.removeFile(key);
      }
    }
  }

  private async removeFile(key: string): Promise<void> {
    const file = this.pool.get(key);
    if (file) {
      try {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
          this.logger.debug(`Deleted file ${file.path}`);
        }
      } catch (error) {
        this.logger.error(`Error deleting file ${file.path}:`, error);
      }
      this.pool.delete(key);
      this.logger.debug(`Removed file ${key} from pool`);
    }
  }

  private async cleanupIdleFiles(): Promise<void> {
    const now = new Date();
    for (const [key, file] of this.pool.entries()) {
      if (
        file.refCount === 0 &&
        now.getTime() - file.lastAccessed.getTime() > this.maxIdleTime
      ) {
        await this.removeFile(key);
      }
    }
  }

  public destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    // Cleanup all files
    for (const [key] of this.pool.entries()) {
      this.removeFile(key);
    }
  }
}
