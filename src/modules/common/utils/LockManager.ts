import { Logger } from '@nestjs/common';

export class LockManager {
  private readonly logger = new Logger(LockManager.name);
  private locks = new Map<
    string,
    {
      locked: boolean;
      queue: Array<{
        resolve: (value: boolean) => void;
        reject: (reason?: any) => void;
      }>;
    }
  >();

  private readonly lockTimeout = 30000; // 30 seconds

  async acquire(key: string): Promise<boolean> {
    if (!this.locks.has(key)) {
      this.locks.set(key, {
        locked: false,
        queue: [],
      });
    }

    const lock = this.locks.get(key)!;

    if (!lock.locked) {
      lock.locked = true;
      this.logger.debug(`Lock acquired for ${key}`);
      return true;
    }

    this.logger.debug(`Waiting for lock on ${key}`);
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        const index = lock.queue.findIndex((q) => q.resolve === resolve);
        if (index !== -1) {
          lock.queue.splice(index, 1);
        }
        reject(new Error(`Lock acquisition timeout for ${key}`));
      }, this.lockTimeout);

      lock.queue.push({
        resolve: (value: boolean) => {
          clearTimeout(timeout);
          resolve(value);
        },
        reject: (reason?: any) => {
          clearTimeout(timeout);
          reject(reason);
        },
      });
    });
  }

  async release(key: string): Promise<void> {
    const lock = this.locks.get(key);
    if (!lock) return;

    if (lock.queue.length > 0) {
      const next = lock.queue.shift()!;
      lock.locked = true;
      this.logger.debug(`Lock transferred for ${key}`);
      next.resolve(true);
    } else {
      lock.locked = false;
      this.logger.debug(`Lock released for ${key}`);
      if (lock.queue.length === 0) {
        this.locks.delete(key);
      }
    }
  }

  isLocked(key: string): boolean {
    return this.locks.get(key)?.locked ?? false;
  }

  async withLock<T>(key: string, fn: () => Promise<T>): Promise<T> {
    try {
      await this.acquire(key);
      return await fn();
    } finally {
      await this.release(key);
    }
  }
}
