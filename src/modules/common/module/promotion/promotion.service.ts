import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PromotionCode } from '../../entity/promotionCode.entity';
import { Promotion } from '../../entity/promotion.entity';

@Injectable()
export class PromotionService {
  constructor(
    @InjectRepository(PromotionCode)
    private readonly promotionCodeRepository: Repository<PromotionCode>,
    @InjectRepository(Promotion)
    private readonly promotionRepository: Repository<Promotion>,
  ) {}

  async updatePromotionCode(
    promotionCodeId: string,
    promotionCode?: PromotionCode,
    promotion?: Promotion,
  ): Promise<void> {
    const promotionCodeRecord =
      promotionCode ??
      (await this.promotionCodeRepository.findOne({
        where: { id: promotionCodeId },
      }));

    if (!promotionCodeRecord) {
      console.warn(`Promotion code with ID ${promotionCodeId} not found.`);
      return;
    }

    const promotionRecord =
      promotion ??
      (await this.promotionRepository.findOne({
        where: { id: promotionCodeRecord.promotionId },
      }));

    if (!promotionRecord) {
      console.warn(
        `Promotion with ID ${promotionCodeRecord.promotionId} not found.`,
      );
      return;
    }

    if (
      promotionRecord.usageLimitPerCode <=
      promotionCodeRecord.numberUsed + 1
    ) {
      promotionCodeRecord.isUsed = true;
    }

    promotionCodeRecord.numberUsed += 1;

    try {
      await this.promotionCodeRepository.save(promotionCodeRecord);
    } catch (error) {
      console.error(
        `Failed to update promotion code ${promotionCodeRecord.code}:`,
        error,
      );
    }
  }
}
