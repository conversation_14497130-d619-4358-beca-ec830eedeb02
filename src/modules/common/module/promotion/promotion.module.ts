import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PromotionService } from './promotion.service';
import { PromotionCode } from '../../entity/promotionCode.entity';
import { Promotion } from '../../entity/promotion.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Promotion, PromotionCode])],
  providers: [PromotionService],
  exports: [PromotionService],
})
export class PromotionModule {}
