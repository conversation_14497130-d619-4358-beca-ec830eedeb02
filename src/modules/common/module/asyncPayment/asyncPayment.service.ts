import { Injectable, Logger } from '@nestjs/common';

export interface PaymentJobData {
  orderId: string;
  orderCode: string;
  clientId: string;
  machineId: string;
  amount: number;
  paymentProvider: 'PAYOS' | 'AUTOBANK';
  paymentData: any;
}

export interface RetryConfig {
  maxRetries: number;
  retryDelay: number;
  backoffMultiplier: number;
}

@Injectable()
export class AsyncPaymentService {
  private readonly logger = new Logger(AsyncPaymentService.name);
  private readonly runningJobs = new Map<string, Promise<void>>();
  
  private readonly defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    retryDelay: 1000, // 1 second
    backoffMultiplier: 2,
  };

  /**
   * Process payment asynchronously without blocking response
   */
  async processPaymentAsync(
    paymentData: PaymentJobData,
    retryConfig?: Partial<RetryConfig>,
  ): Promise<void> {
    const config = { ...this.defaultRetryConfig, ...retryConfig };

    // Fire and forget - don't await this promise
    const jobPromise = this.executePaymentWithRetry(paymentData, config)
      .catch((error) => {
        this.logger.error(
          `Final payment failure for order ${paymentData.orderCode}:`,
          error.message,
        );
      })
      .finally(() => {
        // Clean up tracking
        this.runningJobs.delete(paymentData.orderId);
      });

    // Track the job (optional - for monitoring)
    this.runningJobs.set(paymentData.orderId, jobPromise);

    this.logger.log(`Payment job queued for order ${paymentData.orderCode}`);
  }

  /**
   * Execute payment processing with retry logic
   */
  private async executePaymentWithRetry(
    paymentData: PaymentJobData,
    config: RetryConfig,
  ): Promise<void> {
    let lastError: Error;
    let delay = config.retryDelay;

    for (let attempt = 1; attempt <= config.maxRetries; attempt++) {
      try {
        this.logger.log(
          `Processing payment attempt ${attempt}/${config.maxRetries} for order ${paymentData.orderCode}`,
        );

        await this.processPayment(paymentData);
        
        this.logger.log(
          `Payment processed successfully for order ${paymentData.orderCode}`,
        );
        return;
      } catch (error) {
        lastError = error;
        this.logger.warn(
          `Payment attempt ${attempt} failed for order ${paymentData.orderCode}: ${error.message}`,
        );

        if (attempt < config.maxRetries) {
          this.logger.log(
            `Retrying payment for order ${paymentData.orderCode} in ${delay}ms`,
          );
          await this.sleep(delay);
          delay *= config.backoffMultiplier;
        }
      }
    }

    throw new Error(
      `Payment failed after ${config.maxRetries} attempts: ${lastError.message}`,
    );
  }

  /**
   * Process individual payment
   */
  private async processPayment(paymentData: PaymentJobData): Promise<void> {
    // Simulate payment processing
    // In real implementation, this would call external payment APIs
    // or update database records
    
    this.logger.log(
      `Processing ${paymentData.paymentProvider} payment for order ${paymentData.orderCode}`,
    );

    // Simulate some processing time
    await this.sleep(100);

    // Simulate random failures for testing retry logic
    if (Math.random() < 0.1) { // 10% failure rate
      throw new Error('Simulated payment processing failure');
    }

    this.logger.log(
      `Payment processed successfully for order ${paymentData.orderCode}`,
    );
  }

  /**
   * Get status of running payment jobs
   */
  getRunningJobsCount(): number {
    return this.runningJobs.size;
  }

  /**
   * Check if a specific order is being processed
   */
  isOrderBeingProcessed(orderId: string): boolean {
    return this.runningJobs.has(orderId);
  }

  /**
   * Wait for all running jobs to complete (useful for testing/shutdown)
   */
  async waitForAllJobs(): Promise<void> {
    const jobs = Array.from(this.runningJobs.values());
    await Promise.allSettled(jobs);
  }

  /**
   * Utility method to sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
