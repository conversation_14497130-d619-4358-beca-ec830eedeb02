import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CostReconciliation } from '../../entity/costReconciliation.entity';
import { GenerateReconciliationCodeResponse } from '../../../client/costReconciliation/costReconciliationCode.input';
import {
  generateReconciliationCode,
  getFirstDayOfMonth,
} from '../../../../utils/reconciliation.util';

@Injectable()
export class CostReconciliationCodeService {
  constructor(
    @InjectRepository(CostReconciliation)
    private costReconciliationRepository: Repository<CostReconciliation>,
  ) {}

  /**
   * Generate reconciliation code for a specific month and amount
   * This is used for payment reference codes
   *
   * @param clientId Client ID
   * @param month Month in format YYYY-MM
   * @param amount Amount for the reconciliation
   * @returns GenerateReconciliationCodeResponse
   */
  async generateReconciliationCode(
    clientId: string,
    month: string,
    amount: number,
  ): Promise<GenerateReconciliationCodeResponse> {
    // Generate a unique reference code
    let refCode: string;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 10;

    while (!isUnique && attempts < maxAttempts) {
      refCode = generateReconciliationCode();

      // Check if this code already exists
      const existingRecord = await this.costReconciliationRepository.findOne({
        where: { reconciliationCode: refCode },
      });

      if (!existingRecord) {
        isUnique = true;
      }

      attempts++;
    }

    if (!isUnique) {
      throw new Error(
        'Failed to generate unique reconciliation code after maximum attempts',
      );
    }

    return {
      refCode,
      month,
      amount,
      generatedAt: new Date().toISOString(),
    };
  }

  /**
   * Validate if a reconciliation code exists and belongs to the client
   *
   * @param refCode Reference code to validate
   * @param clientId Client ID
   * @returns True if valid, false otherwise
   */
  async validateReconciliationCode(
    refCode: string,
    clientId: string,
  ): Promise<boolean> {
    const reconciliation = await this.costReconciliationRepository.findOne({
      where: {
        reconciliationCode: refCode,
        clientId,
      },
    });

    return !!reconciliation;
  }

  /**
   * Get reconciliation details by reference code
   *
   * @param refCode Reference code
   * @param clientId Client ID
   * @returns Reconciliation details or null if not found
   */
  async getReconciliationByCode(
    refCode: string,
    clientId: string,
  ): Promise<CostReconciliation | null> {
    return this.costReconciliationRepository.findOne({
      where: {
        reconciliationCode: refCode,
        clientId,
      },
      relations: ['client'],
    });
  }
}
