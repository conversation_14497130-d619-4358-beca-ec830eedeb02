/* eslint-disable @typescript-eslint/no-unsafe-function-type */
import { Injectable } from '@nestjs/common';
import { Storage } from '@google-cloud/storage';
import * as path from 'path';
import * as fs from 'fs';
import * as mime from 'mime-types';
import { Readable } from 'stream';
import 'dotenv/config';

@Injectable()
export class FileService {
  private readonly storageProvider: string;
  private readonly uploadFolderPrefix: string = 'uploads';
  private readonly baseUrl = process.env.BASE_URL || 'http://localhost:3000';

  constructor() {
    this.storageProvider = process.env.STORAGE_PROVIDER || 'LOCAL'; // 'GCP' hoặc 'LOCAL'
  }

  /**
   * Upload một file (single upload)
   * @param buffer - Buffer của file
   * @param fileName - Tên file
   * @param folder - Thư mục chứa file
   * @param isPublic - File có public không
   * @returns URL hoặc đường dẫn của file đã upload
   */
  async singleUpload(
    buffer: Buffer,
    fileName: string,
    folder?: string,
    isPublic = true,
  ): Promise<string> {
    let filePath: string;
    fileName = fileName.replace(/(à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ)/g, 'a');
    fileName = fileName.replace(/(À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ)/g, 'A');
    fileName = fileName.replace(/(è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ)/g, 'e');
    fileName = fileName.replace(/(È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ)/g, 'E');
    fileName = fileName.replace(/(ì|í|ị|ỉ|ĩ)/g, 'i');
    fileName = fileName.replace(/(Ì|Í|Ị|Ỉ|Ĩ)/g, 'I');
    fileName = fileName.replace(/(ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ)/g, 'o');
    fileName = fileName.replace(/(Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ)/g, 'O');
    fileName = fileName.replace(/(ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ)/g, 'u');
    fileName = fileName.replace(/(Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ)/g, 'U');
    fileName = fileName.replace(/(ỳ|ý|ỵ|ỷ|ỹ)/g, 'y');
    fileName = fileName.replace(/(Ỳ|Ý|Ỵ|Ỷ|Ỹ)/g, 'Y');
    fileName = fileName.replace(/(đ)/g, 'd');
    fileName = fileName.replace(/(Đ)/g, 'D');
    // Xóa ký tự đặc biệt
    fileName = fileName.replace(/([^0-9a-zA-Z-.\s])/g, '');
    // Xóa khoảng trắng thay bằng ký tự -
    fileName = fileName.replace(/(\s+)/g, '-');

    switch (this.storageProvider) {
      case 'GCP': {
        const storage = new Storage({
          keyFilename: '@/../gcp-service.json',
        });
        const bucket = storage.bucket(process.env.GOOGLE_CLOUD_STORAGE_BUCKET);
        filePath = path.join(folder || '', fileName);
        const fileCloud = bucket.file(`${this.uploadFolderPrefix}/${filePath}`);

        if (isPublic) {
          await fileCloud.save(buffer);
          await fileCloud.makePublic();
          return `https://storage.googleapis.com/${bucket.name}/${fileCloud.name}`;
        } else {
          await fileCloud.save(buffer);
          return fileCloud.name;
        }
      }

      default: {
        const uploadFolder = path.join(
          path.resolve('./'),
          'public',
          this.uploadFolderPrefix,
        );
        if (folder) {
          const folderPath = path.join(uploadFolder, folder);
          if (!fs.existsSync(folderPath)) {
            fs.mkdirSync(folderPath, { recursive: true });
          }
        }
        filePath = path.join(uploadFolder, folder || '', fileName);
        fs.writeFileSync(filePath, buffer);
        return `${this.baseUrl}/${this.uploadFolderPrefix}/${folder}/${fileName}`;
      }
    }
  }

  /**
   * Lấy metadata của file
   */
  async headObject(key: string): Promise<any> {
    const objectHeader: any = {
      ContentType: undefined,
      ContentLength: undefined,
      AcceptRanges: 'bytes',
    };

    switch (this.storageProvider) {
      default: {
        const uploadFolder = path.join(
          path.resolve('./'),
          'public',
          this.uploadFolderPrefix,
        );
        const filePath = path.join(uploadFolder, key);
        const stats = fs.statSync(filePath);
        objectHeader.ContentType =
          mime.lookup(filePath) || 'application/octet-stream';
        objectHeader.ContentLength = stats.size;
      }
    }

    return objectHeader;
  }

  /**
   * Lấy stream của file
   */
  async getObject(
    key: string,
    onData: Function,
    onDone: Function,
  ): Promise<void> {
    switch (this.storageProvider) {
      default: {
        const uploadFolder = path.join(
          path.resolve('./'),
          'public',
          this.uploadFolderPrefix,
        );
        const filePath = path.join(uploadFolder, key);
        const readStream = fs.createReadStream(filePath);
        readStream.on('data', (chunk) => {
          onData(chunk);
        });
        readStream.on('close', () => {
          onDone();
        });
      }
    }
  }

  /**
   * Lấy URL có chữ ký (signed URL)
   */
  // async getSignedUrl(key: string, isPublic = true): Promise<string> {
  //   let signedUrl: string = key;

  //   switch (this.storageProvider) {
  //     case 'GCP': {
  //       if (isPublic) {
  //         signedUrl = `${process.env.FILE_STORAGE_HOST}/${process.env.GOOGLE_CLOUD_STORAGE_BUCKET}/${this.uploadFolderPrefix}/${key}`;
  //       } else {
  //         const cacheUrl = await this.redisClient.get(`File_${key}`);
  //         if (cacheUrl) {
  //           signedUrl = cacheUrl;
  //         } else {
  //           const storage = new Storage({
  //             keyFilename: process.env.GCP_SERVICE_ACCOUNT_KEY,
  //           });
  //           const bucket = storage.bucket(process.env.GOOGLE_CLOUD_STORAGE_BUCKET);
  //           const file = bucket.file(`${this.uploadFolderPrefix}/${key}`);
  //           const [url] = await file.getSignedUrl({
  //             version: 'v4',
  //             action: 'read',
  //             expires: Date.now() + this.signedUrlTTL,
  //           });
  //           await this.redisClient.set(`File_${key}`, url, 'PX', this.cacheSignedUrlTTL);
  //           signedUrl = url;
  //         }
  //       }
  //       break;
  //     }
  //     default: {
  //       signedUrl = `${process.env.FILE_STORAGE_HOST}/${this.uploadFolderPrefix}/${key}`;
  //     }
  //   }

  //   return signedUrl;
  // }

  /**
   * Convert stream thành buffer
   */
  async getBufferFromStream(stream: Readable): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const chunks: Buffer[] = [];
      stream.on('data', (chunk: any) => chunks.push(Buffer.from(chunk)));
      stream.on('error', (err: any) => reject(err));
      stream.on('end', () => resolve(Buffer.concat(chunks)));
    });
  }
}
