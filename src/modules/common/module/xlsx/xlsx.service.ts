/* eslint-disable prettier/prettier */
import { Injectable } from '@nestjs/common';
import { SBLogger } from '../../../logger/logger.service';
import { Promotion } from '../../entity/promotion.entity';
import * as XLSX from 'xlsx-js-style';
import * as dayjs from 'dayjs';

@Injectable()
export class XlsxService {
  static readonly DEFAULT_BORDER_OPTIONS = { top: { style: 'thin' }, bottom: { style: 'thin' }, left: { style: 'thin' }, right: { style: 'thin' } }

  constructor(private loggerService?: SBLogger) {}

  public async exportPromotion(
    promotion: Promotion,
  ) {
    const overview = [
      [
        { v: 'Tên chương trình', s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS, font: { bold: true } } },
        { v: promotion.name, s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS } },
      ],
      [
        { v: 'Thời gian bắt đầu', s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS, font: { bold: true } } },
        { v: dayjs(parseInt(promotion.startDate)).format('DD/MM/YYYY'), s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS } },
      ],
      [
        { v: 'Thời gian kết thúc', s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS, font: { bold: true } } },
        { v: dayjs(parseInt(promotion.endDate)).format('DD/MM/YYYY'), s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS } },
      ],
      [
        { v: 'Giá trị khuyến mãi', s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS, font: { bold: true } } },
        { v: promotion.discountValue, s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS } },
      ],
      [
        { v: 'Số lượng khuyến mãi', s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS, font: { bold: true } } },
        { v: promotion.maxPromotionCodes, s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS } },
      ],
      [
        { v: 'Giới hạn số lần sử dụng một mã', s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS, font: { bold: true } } },
        { v: promotion.usageLimitPerCode, s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS } },
      ],
    ]
    const overviewColsOpts = [{ wch: 30 }, { wch: 20 }];
    const overviewWorkSheet = XlsxService.appendSingleSheet(overview, overviewColsOpts);
    const vouchers = promotion.codes.map((code) => [
      { v: code.code, s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS } },
      { v: promotion.usageLimitPerCode, s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS } },
      { v: code.numberUsed, s: { alignment: { horizontal: 'left' }, border: XlsxService.DEFAULT_BORDER_OPTIONS } },
    ]);
    const voucherHeaders = [
      { v: 'Mã voucher', s: { alignment: { horizontal: 'center' }, font: { bold: true }, border: XlsxService.DEFAULT_BORDER_OPTIONS } },
      { v: 'Lượt dùng tối đa', s: { alignment: { horizontal: 'center' }, font: { bold: true }, border: XlsxService.DEFAULT_BORDER_OPTIONS } },
      { v: 'Đã dùng', s: { alignment: { horizontal: 'center' }, font: { bold: true }, border: XlsxService.DEFAULT_BORDER_OPTIONS } },
    ];
    vouchers.unshift(voucherHeaders);
    const vouchersColsOpts = [{ wch: 20 }, { wch: 20 }, { wch: 20 }];
    const vouchersWorkSheet = XlsxService.appendSingleSheet(vouchers, vouchersColsOpts);
    return await this.exportToExcel([
      { sheetName: 'Overview', sheetData: overviewWorkSheet },
      { sheetName: 'Vouchers', sheetData: vouchersWorkSheet },
    ]);
  }

  private static appendSingleSheet (data: any, wsColsOpts: XLSX.ColInfo[] = undefined) {
    const workSheetData = [
      ...data,
    ];
    const workSheet = XLSX.utils.aoa_to_sheet(workSheetData);
    workSheet['!cols'] = wsColsOpts;
    return workSheet;
  }

  private async exportToExcel(
    workSheets: { sheetName: string; sheetData: XLSX.WorkSheet }[],
  ) {
    const workBook = XLSX.utils.book_new();
    const writeOpts: XLSX.WritingOptions = {
      type: 'buffer',
      cellDates: true,
      bookSST: false,
      bookType: 'xlsx',
      compression: false,
    };
    for (const sheet of workSheets) {
      XLSX.utils.book_append_sheet(workBook, sheet.sheetData, sheet.sheetName);
    }
    const buffer = XLSX.write(workBook, writeOpts);
    return buffer;
  }
}
