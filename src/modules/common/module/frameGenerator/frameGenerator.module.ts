import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { SBLogger } from 'src/modules/logger/logger.service';
import { FrameGeneratorService } from './frameGenerator.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Client } from '../../entity/client.entity';
import { Topic } from '../../entity/topic.entity';
import { Layout } from '../../entity/layout.entity';
import { FileService } from '../file/file.service';
import { LayoutItem } from '../../entity/layoutItem.entity';
import { LayoutFormat } from '../../entity/layoutFormat.entity';
import { Frame } from '../../entity/frame.entity';
import { FrameItem } from '../../entity/frameItem.entity';
import { Sticker } from '../../entity/sticker.entity';
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([
      Client,
      Topic,
      Layout,
      LayoutFormat,
      LayoutItem,
      Frame,
      FrameItem,
      Sticker,
    ]),
  ],
  providers: [FrameGeneratorService, SBLogger, FileService],
  exports: [FrameGeneratorService],
})
export class FrameGeneratorModule {}
