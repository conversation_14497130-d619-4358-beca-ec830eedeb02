import { Injectable } from '@nestjs/common';
import 'dotenv/config';
import { SBLogger } from '../../../logger/logger.service';
import { Client } from '../../../common/entity/client.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Topic } from '../../../common/entity/topic.entity';
import { Layout } from '../../../common/entity/layout.entity';
import { ELayoutType, ELayoutFormatStatus } from '../../../../enum';
import { LayoutItem } from '../../../common/entity/layoutItem.entity';
import { LayoutFormat } from '../../../common/entity/layoutFormat.entity';
import { FileService } from '../../../common/module/file/file.service';
import * as fs from 'fs';
import * as path from 'path';
import { Frame } from '../../entity/frame.entity';
import { FrameItem } from '../../entity/frameItem.entity';
import { Sticker } from '../../../common/entity/sticker.entity';
@Injectable()
export class FrameGeneratorService {
  constructor(
    @InjectRepository(Client)
    private readonly clientRepository: Repository<Client>,
    @InjectRepository(Topic)
    private readonly topicRepository: Repository<Topic>,
    @InjectRepository(Layout)
    private readonly layoutRepository: Repository<Layout>,
    @InjectRepository(LayoutFormat)
    private readonly layoutFormatRepository: Repository<LayoutFormat>,
    @InjectRepository(LayoutItem)
    private readonly layoutItemRepository: Repository<LayoutItem>,
    @InjectRepository(Frame)
    private readonly frameRepository: Repository<Frame>,
    @InjectRepository(FrameItem)
    private readonly frameItemRepository: Repository<FrameItem>,
    @InjectRepository(Sticker)
    private readonly stickerRepository: Repository<Sticker>,
    private readonly fileService: FileService,
    private readonly loggerService?: SBLogger,
  ) {}

  public async execute(clientId: string) {
    try {
      const client = await this.clientRepository.findOneBy({ id: clientId });
      if (!client) return;
      const topics = await this.generateTopics(client);
      await this.generateLayoutItems(client, topics);
      await this.generateFrames(client, topics);
      await this.generateSticker(client);
    } catch (error) {
      this.loggerService.log('ERROR - frameGenerator - execute', error);
    }
  }

  private async generateTopics(client: Client) {
    const topicNames = ['Basic', 'Limited', 'Kawaii'];
    const topics = [];
    for (const topicName of topicNames) {
      const topic = this.topicRepository.create({
        name: topicName,
        clientId: client.id,
        extraFee: 0,
      });
      const record = await this.topicRepository.save(topic);
      topics.push(record);
    }
    return topics;
  }

  private async generateLayoutItems(client: Client, topics: Topic[]) {
    const layout = await this.layoutRepository.findOne({
      where: { clientId: client.id, layoutType: ELayoutType.Default },
    });
    const layoutFormat = await this.layoutFormatRepository.findOne({
      where: { layoutId: layout.id, imageCount: 3 },
    });
    layoutFormat.status = ELayoutFormatStatus.ACTIVE;
    await this.layoutFormatRepository.save(layoutFormat);
    const layoutItems = await this.layoutItemRepository.find({
      where: { layoutFormatId: layoutFormat.id },
      order: { position: 'ASC' },
    });
    for (const [index, layoutItem] of layoutItems.entries()) {
      let url;
      const topic = topics[index];
      if (!topic) continue;
      const imagePath = path.resolve(
        __dirname,
        `../../../../../public/default-frame-assets/layoutItems/${topic.name}.png`,
      );
      try {
        const imageBuffer = await fs.readFileSync(imagePath);
        url = await this.fileService.singleUpload(
          imageBuffer,
          `${topic.name.toLowerCase()}.png`,
          `layoutItems/${layoutItem.id}`,
        );
        layoutItem.imageUrl = url;
        layoutItem.topicId = topic.id;
        await this.layoutItemRepository.save(layoutItem);
      } catch (error) {
        console.log(error);
        this.loggerService.log(
          `Failed to process image for topic ${topic.name}:`,
          error,
        );
      }
    }
  }

  private async generateFrames(client: Client, topics: Topic[]) {
    const frames = [];
    for (const topic of topics) {
      try {
        const configs = JSON.parse(
          await fs.readFileSync(
            path.resolve(
              __dirname,
              `../../../../../public/default-frame-assets/frames/${topic.name}/configs.json`,
            ),
            'utf8', // specify encoding
          ),
        );
        for (const config of configs) {
          const imagePath = path.resolve(
            __dirname,
            `../../../../../public/default-frame-assets/frames/${topic.name}/${config.fileName}`,
          );
          const imageBuffer = await fs.readFileSync(imagePath);
          const url = await this.fileService.singleUpload(
            imageBuffer,
            `${topic.name.toLowerCase()}.png`,
            `frames/${client.id}/${Date.now().toString()}`,
          );
          const frame = this.frameRepository.create({
            clientId: client.id,
            topicId: topic.id,
            imageUrl: url,
            frameSize: config.frameSize,
            numberImage: config.numberImage,
            numberPicture: config.numberPicture,
            orientation: config.orientation,
          });
          const record = await this.frameRepository.save(frame);
          frames.push(record);
          for (const item of config.items) {
            const frameItem = this.frameItemRepository.create({
              frameId: record.id,
              ...item,
            });
            await this.frameItemRepository.save(frameItem);
          }
        }
      } catch (error) {
        this.loggerService.log(
          `Failed to process configs for topic ${topic.name}:`,
          error,
        );
      }
    }
  }

  private async generateSticker(client: Client) {
    const stickersPath = path.resolve(
      __dirname,
      `../../../../../public/default-frame-assets/stickers`,
    );

    try {
      const files = fs.readdirSync(stickersPath);
      for (const file of files) {
        const filePath = path.join(stickersPath, file);
        const imageBuffer = await fs.readFileSync(filePath);
        const url = await this.fileService.singleUpload(
          imageBuffer,
          file,
          `frames/${client.id}/${Date.now().toString()}`,
        );
        const sticker = this.stickerRepository.create({
          clientId: client.id,
          image: url,
        });
        await this.stickerRepository.save(sticker);
      }
    } catch (error) {
      this.loggerService.log('Failed to process stickers:', error);
    }
  }
}
