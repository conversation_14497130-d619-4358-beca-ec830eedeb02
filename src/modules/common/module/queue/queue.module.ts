import { <PERSON><PERSON><PERSON>, OnModuleInit, Logger } from '@nestjs/common';
import { BullModule, InjectQueue } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { Queue } from 'bull';
import { execSync } from 'child_process';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FileJob } from '../../../clientApp/uploadImage/file-job.entity';
import { QueueService } from './queue.service';

export class QueueHealthCheck implements OnModuleInit {
  private readonly logger = new Logger(QueueHealthCheck.name);

  constructor(
    @InjectQueue('file-processing') private readonly fileProcessingQueue: Queue,
  ) {}

  async onModuleInit() {
    try {
      // Kiểm tra kết nối đến Redis
      this.logger.log('Attempting to connect to Redis...');

      // Get Redis connection details for logging
      const redisClient = this.fileProcessingQueue.client;
      const redisOptions = redisClient.options as any;

      // Log Redis connection details
      if (redisOptions.url) {
        const maskedUrl = redisOptions.url.replace(/\/\/.*:.*@/, '//***:***@');
        this.logger.log(`Redis connection URL: ${maskedUrl}`);
      } else if (redisOptions.path) {
        this.logger.log(`Redis connection Socket: ${redisOptions.path}`);
      } else {
        this.logger.log(
          `Redis connection Host: ${redisOptions.host || 'localhost'}, Port: ${redisOptions.port || 6379}`,
        );
      }

      await redisClient.ping();
      this.logger.log('Connected to Redis successfully');

      // Kiểm tra số lượng job trong queue
      const jobCounts = await this.fileProcessingQueue.getJobCounts();
      this.logger.log(`Current job counts: ${JSON.stringify(jobCounts)}`);

      // Kiểm tra danh sách job đang chờ
      const waitingJobs = await this.fileProcessingQueue.getJobs(['waiting']);
      this.logger.log(`Waiting jobs: ${waitingJobs.length}`);

      // Kiểm tra danh sách job đang xử lý
      const activeJobs = await this.fileProcessingQueue.getJobs(['active']);
      this.logger.log(`Active jobs: ${activeJobs.length}`);

      // Kiểm tra danh sách job đã hoàn thành
      const completedJobs = await this.fileProcessingQueue.getJobs([
        'completed',
      ]);
      this.logger.log(`Completed jobs: ${completedJobs.length}`);

      // Kiểm tra danh sách job bị lỗi
      const failedJobs = await this.fileProcessingQueue.getJobs(['failed']);
      this.logger.log(`Failed jobs: ${failedJobs.length}`);

      // Kiểm tra xem có worker nào đang lắng nghe queue không
      const workers = await this.fileProcessingQueue.getWorkers();
      this.logger.log(`Workers: ${workers.length}`);

      if (workers.length === 0) {
        this.logger.warn(
          'No workers are listening to the queue. Jobs will not be processed.',
        );
      }
    } catch (error) {
      this.logger.error(`Failed to connect to Redis: ${error.message}`);
      this.logger.error(`Error stack: ${error.stack}`);

      // Try to get more information about the Redis connection
      try {
        const redisClient = this.fileProcessingQueue.client;
        const redisOptions = redisClient.options as any;

        // Log Redis connection details
        if (redisOptions.url) {
          const maskedUrl = redisOptions.url.replace(
            /\/\/.*:.*@/,
            '//***:***@',
          );
          this.logger.error(`Redis connection URL: ${maskedUrl}`);
        } else if (redisOptions.path) {
          this.logger.error(`Redis connection Socket: ${redisOptions.path}`);
        } else {
          this.logger.error(
            `Redis connection Host: ${redisOptions.host || 'localhost'}, Port: ${redisOptions.port || 6379}`,
          );
        }

        // Thử ping Redis server từ bên ngoài để kiểm tra kết nối
        this.logger.log('Attempting to check Redis connectivity...');

        // Hiển thị thông tin về network trong Docker
        this.logger.log('Network information:');
        try {
          const networkInfo = execSync('ip route').toString();
          this.logger.log(`IP routes: ${networkInfo}`);
        } catch (netError) {
          this.logger.error(`Could not get network info: ${netError.message}`);
        }
      } catch (innerError) {
        this.logger.error(
          `Could not get Redis connection details: ${innerError.message}`,
        );
      }
    }
  }
}

@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forFeature([FileJob]),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        // Get Redis configuration from ConfigService
        const redisHost = configService.get('REDIS_HOST');
        const redisPort = configService.get<number>('REDIS_PORT');
        const redisUsername = configService.get('REDIS_USERNAME');
        const redisPassword = configService.get('REDIS_PASSWORD');

        console.log('Redis config', {
          host: redisHost,
          port: redisPort,
          password: redisPassword ? '******' : undefined,
        });

        // Cấu hình Redis - Sử dụng thông tin kết nối trực tiếp
        // Trong Docker, biến môi trường có thể được định dạng không đúng
        // Ưu tiên sử dụng thông tin kết nối cố định để tránh vấn đề với các biến riêng lẻ

        // Sử dụng thông tin kết nối cố định cho Redis
        const redisConfig = {
          host: redisHost,
          port: redisPort,
          username: redisUsername,
          password: redisPassword,
          connectTimeout: 30000, // Tăng timeout kết nối
          maxRetriesPerRequest: 10, // Tăng số lần thử lại
          enableReadyCheck: false,
          enableOfflineQueue: true, // Cho phép hàng đợi ngoại tuyến
        };

        return {
          redis: {
            ...redisConfig,
            retryStrategy: (times) => {
              console.log(`Redis retry attempt: ${times}`);
              return Math.min(times * 2000, 30000); // Tăng thời gian giữa các lần thử lại
            },
            reconnectOnError: (err) => {
              console.log(`Redis connection error: ${err.message}`);
              // Thử kết nối lại cho tất cả các lỗi
              return true;
            },
          },
          defaultJobOptions: {
            attempts: 3,
            removeOnComplete: true,
            removeOnFail: false,
            backoff: {
              type: 'exponential',
              delay: 1000,
            },
          },
          limiter: {
            max: 1000,
            duration: 5000,
          },
          settings: {
            maxStalledCount: 2,
            lockDuration: 30000,
            stalledInterval: 30000,
            maxListeners: 20, // Tăng giới hạn số lượng listeners
          },
        };
      },
      inject: [ConfigService],
    }),
    BullModule.registerQueue({
      name: 'file-processing',
    }),
  ],
  exports: [BullModule],
  providers: [QueueHealthCheck, QueueService],
})
export class QueueModule {}
