import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FileJob } from '../../../clientApp/uploadImage/file-job.entity';

@Injectable()
export class QueueService implements OnModuleInit {
  private readonly logger = new Logger(QueueService.name);

  constructor(
    @InjectQueue('file-processing') private readonly fileProcessingQueue: Queue,
    @InjectRepository(FileJob)
    private fileJobRepository: Repository<FileJob>,
  ) {}

  async onModuleInit() {
    this.logger.log('QueueService initialized');
    await this.logQueueStats();
  }

  private async logQueueStats() {
    try {
      const jobCounts = await this.fileProcessingQueue.getJobCounts();
      this.logger.log(`Current queue stats:
        Waiting: ${jobCounts.waiting}
        Active: ${jobCounts.active}
        Completed: ${jobCounts.completed}
        Failed: ${jobCounts.failed}
        Delayed: ${jobCounts.delayed}
      `);
    } catch (error) {
      this.logger.error(`Failed to get queue stats: ${error.message}`);
    }
  }
}
