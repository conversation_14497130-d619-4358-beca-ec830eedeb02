import { Repository } from 'typeorm';
import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PromotionCode } from '../../entity/promotionCode.entity';
import { Promotion } from '../../entity/promotion.entity';
import { SettingSize } from '../../entity/settingSize.entity';
import { Topic } from '../../entity/topic.entity';
import { Frame } from '../../entity/frame.entity';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { EOrderAppStatus } from '../../../../enum/index';
import { Order } from '../../../clientApp/order/order.entity';
import {
  AppOrderOnlineInput,
  AppOrderOnlineCashInput,
} from '../../../clientApp/order/order.input';
import * as moment from 'moment';

@Injectable()
export class AppOrderService {
  constructor(
    private firestoreService: FirestoreService,
    @InjectRepository(Promotion)
    private readonly promotionRepository: Repository<Promotion>,
    @InjectRepository(PromotionCode)
    private readonly promotionCodeRepository: Repository<PromotionCode>,
    @InjectRepository(SettingSize)
    private readonly settingSizeRepository: Repository<SettingSize>,
    @InjectRepository(Topic)
    private readonly topicRepository: Repository<Topic>,
    @InjectRepository(Frame)
    private readonly frameRepository: Repository<Frame>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
  ) {}

  async prepareOrderDependencies(
    input: AppOrderOnlineInput | AppOrderOnlineCashInput,
    userId: string,
    machineId: string,
    validateReceivedAmount: boolean = false,
  ) {
    const { promotionCode, settingSizeId, settingSizeKey, frameId } = input;

    let promotionCodeRecord = null;
    let promotion = null;

    if (promotionCode) {
      promotionCodeRecord =
        await this.fetchPromotionCodeIfExists(promotionCode);

      if (promotionCodeRecord?.isUsed) {
        throw new BadRequestException('Promotion code has expired');
      }

      promotion = await this.fetchPromotionIfExists(
        promotionCodeRecord?.promotionId,
        machineId,
      );

      if (promotion?.isUsed) {
        throw new BadRequestException('Promotion code has expired');
      }

      const totalDeliveredOrders = await this.getDeliveredOrdersCount(
        userId,
        promotionCodeRecord.id,
      );
      const isOngoing = this.isPromotionOngoing(promotion);
      const totalUsed = totalDeliveredOrders + promotionCodeRecord.numberUsed;
      const canUse = isOngoing && totalUsed <= promotion.usageLimitPerCode;

      if (!canUse) {
        throw new BadRequestException('Promotion code has expired');
      }
    }

    const settingSize = await this.validateSettingSize(
      settingSizeId,
      userId,
      machineId,
    );
    const frame = await this.validateFrame(frameId, userId);
    const topic = frame?.topic;

    const { totalPrice, totalPriceAfterDiscount } = this.calculateAmount(
      settingSize,
      settingSizeKey,
      topic?.extraFee || 0,
      promotion?.discountValue || 0,
    );

    // Validate received amount if needed
    if (validateReceivedAmount) {
      // Check if receivedAmount field exists
      if (!('receivedAmount' in input)) {
        throw new BadRequestException(
          'Received amount is required for this payment method',
        );
      }

      // Skip validation if totalPriceAfterDiscount is 0
      if (
        totalPriceAfterDiscount > 0 &&
        input.receivedAmount < totalPriceAfterDiscount
      ) {
        throw new BadRequestException(
          `Received amount (${input.receivedAmount}) must be greater than or equal to the total price after discount (${totalPriceAfterDiscount})`,
        );
      }
    }

    return {
      totalPrice,
      totalPriceAfterDiscount,
      promotionCodeRecord,
      promotion,
      settingSize,
      topic,
      frame,
    };
  }

  private async fetchPromotionIfExists(
    promotionId: string | undefined,
    machineId: string,
  ): Promise<Promotion | null> {
    if (!promotionId) return null;

    const promotion = await this.promotionRepository.findOne({
      where: { id: promotionId },
    });

    if (!promotion) {
      throw new BadRequestException('Promotion not found.');
    }

    if (
      promotion.machineIds &&
      !promotion.machineIds.split(',').includes(machineId)
    ) {
      throw new BadRequestException('Promotion is not valid for this machine.');
    }

    return promotion;
  }

  private async fetchPromotionCodeIfExists(
    promotionCode: string,
  ): Promise<PromotionCode | null> {
    if (!promotionCode) return null;

    const promotionCodeRecord = await this.promotionCodeRepository.findOne({
      where: { code: promotionCode },
    });

    if (!promotionCodeRecord) {
      throw new BadRequestException('PromotionCode not found.');
    }

    return promotionCodeRecord;
  }

  private async validateSettingSize(
    settingSizeId: string,
    userId: string,
    machineId: string,
  ): Promise<SettingSize> {
    const settingSize = await this.settingSizeRepository.findOne({
      where: { id: settingSizeId, clientId: userId },
    });

    if (!settingSize) {
      throw new BadRequestException(
        'Setting size not found or invalid for this user.',
      );
    }

    if (
      settingSize.machineIds &&
      !settingSize.machineIds.split(',').includes(machineId)
    ) {
      throw new BadRequestException(
        'Setting size is not valid for this machine.',
      );
    }

    return settingSize;
  }

  private async validateTopic(topicId: string, userId: string): Promise<Topic> {
    const topic = await this.topicRepository.findOne({
      where: { id: topicId, clientId: userId },
    });

    if (!topic) {
      throw new BadRequestException(
        'Topic not found or invalid for this user.',
      );
    }

    return topic;
  }

  private async validateFrame(frameId: string, userId: string): Promise<Frame> {
    const frame = await this.frameRepository.findOne({
      where: { id: frameId, clientId: userId },
      withDeleted: true,
      relations: ['topic'],
    });

    if (!frame) {
      throw new BadRequestException(
        'Frame not found or invalid for this user.',
      );
    }

    return frame;
  }

  private calculateAmount(
    settingSize: SettingSize,
    settingSizeKey: string,
    extraFeeValue: number,
    discountValue: number,
  ): { totalPrice: number; totalPriceAfterDiscount: number } {
    const basePrice = settingSize[settingSizeKey];

    if (typeof basePrice !== 'number' || isNaN(basePrice)) {
      throw new Error(`Invalid base price for key: ${settingSizeKey}`);
    }

    const totalPrice = basePrice + extraFeeValue;
    const totalPriceAfterDiscount = Math.max(0, totalPrice - discountValue);

    return { totalPrice, totalPriceAfterDiscount };
  }

  private isPromotionOngoing(promotion: Promotion): boolean {
    const now = moment.utc();
    const startDate = moment.utc(Number(promotion.startDate));
    const endDate = moment.utc(Number(promotion.endDate));

    return startDate.isSameOrBefore(now) && endDate.isSameOrAfter(now);
  }

  private async getDeliveredOrdersCount(
    userId: string,
    promotionCodeId: string,
  ): Promise<number> {
    const validStatuses = [
      EOrderAppStatus.PENDING,
      EOrderAppStatus.ACCEPTED,
      EOrderAppStatus.REJECTED,
    ];

    const deliveredOrders = await this.firestoreService.getDocumentsByCondition(
      userId,
      [
        { field: 'status', operator: 'in', value: validStatuses },
        { field: 'promotionCodeId', operator: '==', value: promotionCodeId },
      ],
    );

    return deliveredOrders.length;
  }

  async generateQRCode(machineId: string, clientId: string): Promise<string> {
    let uniqueCode: string;
    let isUnique = false;

    while (!isUnique) {
      uniqueCode = `${machineId}-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 10)
        .toUpperCase()}`;

      const existingOrder = await this.orderRepository.findOne({
        where: { orderCode: uniqueCode, machineId, clientId },
      });

      if (!existingOrder) {
        isUnique = true;
      }
    }

    return uniqueCode;
  }

  extractNumbersFromString(input) {
    return input.match(/\d+/g)?.map(Number)[0] || [];
  }
}
