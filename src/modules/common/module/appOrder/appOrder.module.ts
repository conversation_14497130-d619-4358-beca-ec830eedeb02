import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
// entity
import { Frame } from '../../entity/frame.entity';
import { Promotion } from '../../entity/promotion.entity';
import { PromotionCode } from '../../entity/promotionCode.entity';
import { SettingSize } from '../../entity/settingSize.entity';
import { Topic } from '../../entity/topic.entity';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { Order } from '../../../clientApp/order/order.entity';
// service
import { AppOrderService } from './appOrder.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Promotion,
      PromotionCode,
      Topic,
      SettingSize,
      Frame,
      Order,
    ]),
  ],
  providers: [FirestoreService, AppOrderService],
  exports: [AppOrderService],
})
export class AppOrderModule {}
