import { BadRequestException, Injectable } from '@nestjs/common';
import 'dotenv/config';
import axios from 'axios';
import { SBLogger } from '../../../logger/logger.service';
import * as hmacSHA256 from 'crypto-js/hmac-sha256';
import * as QueryString from 'qs';
import { createHmac } from 'crypto';

@Injectable()
export class PayosService {
  constructor(private loggerService?: SBLogger) {}

  public async makePayment(
    machineId: string,
    machineCode: string,
    amount: number,
    clientId: string,
    apiKey: string,
    checkSum: string,
  ): Promise<{ refCode: string; qrCode: string }> {
    const orderCode = parseInt(`${Math.floor(Date.now())}`);
    const body = {
      orderCode,
      amount: amount,
      description: `${machineCode}`,
      items: [],
      cancelUrl: `${process.env.API_ENDPOINT}/payos/cancel-callback`,
      returnUrl: `${process.env.API_ENDPOINT}/payos/return-callback`,
      expiredAt: Math.floor(Date.now() / 1000) + 30 * 60,
      signature: '',
    };
    body.signature = this.generateMakePaymentSecureHash(
      checkSum,
      body.amount,
      body.cancelUrl,
      body.description,
      body.orderCode,
      body.returnUrl,
    );
    try {
      const response = await axios.post(
        `${process.env.PAYOS_ENDPOINT}/v2/payment-requests`,
        body,
        {
          headers: {
            'x-client-id': clientId,
            'x-api-key': apiKey,
            'Content-Type': 'application/json',
          },
        },
      );

      if (!response?.data?.data) {
        const desc = `PayOS: ${response.data.desc || 'Không thể tạo thanh toán. Vui lòng kiểm tra thông tin thanh toán và thử lại.'}`;
        throw new BadRequestException(desc);
      }

      const { paymentLinkId, qrCode } = response?.data?.data;

      return {
        refCode: paymentLinkId,
        qrCode: qrCode,
      };
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }

      const errorMessage = `PayOS: ${
        error.response?.data?.desc ||
        error.response?.data?.message ||
        error.message ||
        'Có lỗi xảy ra khi xử lý thanh toán. Vui lòng thử lại sau.'
      }`;

      throw new BadRequestException(errorMessage);
    }
  }

  async confirmCallbackUrl(clientId: string, apiKey: string) {
    try {
      const res = await axios.post(
        `${process.env.PAYOS_ENDPOINT}/confirm-webhook`,
        {
          webhookUrl: `${process.env.API_ENDPOINT}/payos/callback`,
        },
        {
          headers: {
            'x-client-id': clientId,
            'x-api-key': apiKey,
            'Content-Type': 'application/json',
          },
        },
      );

      return res.data;
    } catch (error) {
      console.log('ERROR - payos - confirmCallbackUrl', error);
    }
  }

  private generateMakePaymentSecureHash(
    checkSum: string,
    amount: number,
    cancelUrl: string,
    description: string,
    orderCode: number,
    returnUrl: string,
  ) {
    const result = hmacSHA256(
      QueryString.stringify(
        {
          amount: amount,
          cancelUrl: cancelUrl,
          description: description,
          orderCode: orderCode,
          returnUrl: returnUrl,
        },
        { encode: false },
      ),
      checkSum,
    ).toString();
    return result;
  }

  public static isValidSignature(
    webhookData: Record<string, any>,
    webhookSignature: string,
    checkSum: string,
  ) {
    const sortedDataByKey = this.sortObjDataByKey(webhookData);
    const dataQueryStr = this.convertObjToQueryStr(sortedDataByKey);
    const dataToSignature = createHmac('sha256', checkSum)
      .update(dataQueryStr)
      .digest('hex');
    return dataToSignature === webhookSignature;
  }

  private static sortObjDataByKey(object) {
    const orderedObject = Object.keys(object)
      .sort()
      .reduce((obj, key) => {
        obj[key] = object[key];
        return obj;
      }, {});
    return orderedObject;
  }

  private static convertObjToQueryStr(object) {
    return Object.keys(object)
      .filter((key) => object[key] !== undefined)
      .map((key) => {
        let value = object[key];
        if (value && Array.isArray(value)) {
          value = JSON.stringify(
            value.map((val) => this.sortObjDataByKey(val)),
          );
        }
        if ([null, undefined, 'undefined', 'null'].includes(value)) {
          value = '';
        }

        return `${key}=${value}`;
      })
      .join('&');
  }
}
