import * as crypto from 'crypto';
import { BASE62 } from 'src/constants';

const SECRET_KEY = process.env.SECRET_KEY || 'snapbox-secret-key';
const ALGORITHM = 'aes-256-cbc';
const IV_LENGTH = 16;

export function encrypt(text: string): string {
  const key = crypto.createHash('sha256').update(SECRET_KEY).digest();
  const iv = crypto.randomBytes(IV_LENGTH);

  const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

  let encrypted = cipher.update(text, 'utf-8', 'hex');
  encrypted += cipher.final('hex');

  return iv.toString('hex') + ':' + encrypted;
}

export function decrypt(encrypted: string): string {
  const [ivHex, encryptedData] = encrypted.split(':');
  if (!ivHex || !encryptedData) {
    throw new Error('Invalid encrypted data format');
  }

  const iv = Buffer.from(ivHex, 'hex');
  const key = crypto.createHash('sha256').update(SECRET_KEY).digest();

  const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);

  let decrypted = decipher.update(encryptedData, 'hex', 'utf-8');
  decrypted += decipher.final('utf-8');

  return decrypted;
}

export function encodeBase62(buffer: Buffer): string {
  let num = BigInt('0x' + buffer.toString('hex'));
  let result = '';
  while (num > 0) {
    const rem = Number(num % BigInt(BASE62.length));
    result = BASE62[rem] + result;
    num = num / BigInt(BASE62.length);
  }
  return result;
}

export function decodeBase62(str: string): Buffer {
  let num = BigInt(0);
  for (let i = 0; i < str.length; i++) {
    num = num * BigInt(BASE62.length) + BigInt(BASE62.indexOf(str[i]));
  }
  let hex = num.toString(16);
  while (hex.length < 32) hex = '0' + hex; // pad to 16 bytes (32 hex chars)
  return Buffer.from(hex, 'hex');
}
