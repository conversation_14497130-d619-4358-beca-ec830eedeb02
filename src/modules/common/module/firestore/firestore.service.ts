import { Firestore } from '@google-cloud/firestore';
import * as fs from 'fs';
import * as dotenv from 'dotenv';
dotenv.config();

// Đường dẫn đến tệp JSON credentials
const credentialsPath = process.env.GOOGLE_APPLICATION_CREDENTIALS;

if (!credentialsPath) {
  console.error(
    'GOOGLE_APPLICATION_CREDENTIALS environment variable is not set.',
  );
  process.exit(1);
}

let firestore: Firestore;

try {
  // Đọc và parse credentials từ tệp JSON
  const credentialsContent = fs.readFileSync(credentialsPath, 'utf-8');
  const credentials = JSON.parse(credentialsContent);

  // Log connection attempt (remove in production)
  console.log(
    'Attempting to initialize Firestore with project:',
    credentials.project_id,
  );

  // Khởi tạo Firestore
  firestore = new Firestore({
    credentials,
    projectId: credentials.project_id,
  });

  console.log('Firestore initialized successfully.');
} catch (error) {
  console.error('Error initializing Firestore:', {
    message: error.message,
    stack: error.stack,
  });
  process.exit(1);
}

// Firestore Service Class
export class FirestoreService {
  private firestore: Firestore;

  constructor() {
    this.firestore = firestore;
  }

  /**
   * Push data lên Firestore với key chỉ định
   * @param collectionName Tên collection
   * @param key Document ID (key trong Firestore)
   * @param data Dữ liệu để lưu
   * @returns Promise<void>
   */
  async pushData(
    collectionName: string,
    key: string,
    data: Record<string, any>,
    isMergeData,
  ): Promise<void> {
    try {
      const docRef = this.firestore.collection(collectionName).doc(key);
      await docRef.set(data, { merge: isMergeData || false });
      console.log(
        `Data pushed successfully: Collection=${collectionName}, Key=${key}`,
      );
    } catch (error) {
      console.error('Failed to push data:', error.message);
      throw new Error('Unable to save data to Firestore.');
    }
  }

  /**
   * Cập nhật dữ liệu cho một document trong Firestore
   * @param collectionName Tên collection
   * @param key Document ID (key trong Firestore)
   * @param data Dữ liệu để cập nhật
   * @returns Promise<void>
   */
  async updateDocument(
    collectionName: string,
    key: string,
    data: Record<string, any>,
  ): Promise<void> {
    try {
      const docRef = this.firestore.collection(collectionName).doc(key);
      await docRef.set(data, { merge: true });
      console.log(
        `Document updated/created successfully: Collection=${collectionName}, Key=${key}`,
      );
    } catch (error) {
      console.error('Failed to update/create document:', {
        collection: collectionName,
        key: key,
        error: error.message,
        code: error.code,
        details: error.details,
      });
      throw new Error(`Unable to update/create document: ${error.message}`);
    }
  }

  /**
   * Lấy dữ liệu từ Firestore với key chỉ định
   * @param collectionName Tên collection
   * @param key Document ID (key trong Firestore)
   * @returns Promise<Record<string, any>> Dữ liệu tài liệu hoặc null nếu không tìm thấy
   */
  async getData(
    collectionName: string,
    key: string,
  ): Promise<Record<string, any> | null> {
    try {
      const docRef = this.firestore.collection(collectionName).doc(key);
      const docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        console.log(
          `Data fetched successfully: Collection=${collectionName}, Key=${key}`,
        );
        return docSnapshot.data() as Record<string, any>;
      } else {
        console.warn(
          `No document found: Collection=${collectionName}, Key=${key}`,
        );
        return null;
      }
    } catch (error) {
      console.error('Failed to fetch data:', error.message); // Không log thông tin nhạy cảm
      throw new Error('Unable to fetch data from Firestore.');
    }
  }

  /**
   * Lấy danh sách tất cả các document từ một collection
   * @param collectionName Tên collection
   * @returns Promise<Record<string, any>[]> Danh sách document
   */
  async getDataList(collectionName: string): Promise<Record<string, any>[]> {
    try {
      const collectionRef = this.firestore.collection(collectionName);
      const snapshot = await collectionRef.get();

      if (snapshot.empty) {
        console.warn(`No documents found in collection: ${collectionName}`);
        return [];
      }

      const documents = snapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      console.log(
        `Data list fetched successfully from collection: ${collectionName}`,
      );
      return documents;
    } catch (error) {
      console.error('Failed to fetch data list:', error.message); // Không log thông tin nhạy cảm
      throw new Error('Unable to fetch data list from Firestore.');
    }
  }

  /**
   * Lấy một document từ Firestore
   * @param collectionName Tên collection
   * @param documentId Document ID
   * @returns Promise<Record<string, any> | null> Document data hoặc null nếu không tìm thấy
   */
  async getDocument(
    collectionName: string,
    documentId: string,
  ): Promise<Record<string, any> | null> {
    try {
      const docRef = this.firestore.collection(collectionName).doc(documentId);
      const doc = await docRef.get();

      if (doc.exists) {
        return {
          id: doc.id,
          ...doc.data(),
        };
      } else {
        return null;
      }
    } catch (error) {
      console.error('Failed to get document:', error.message);
      throw new Error('Unable to get document from Firestore.');
    }
  }

  /**
   * Lấy danh sách các document từ Firestore với điều kiện
   * @param collectionName Tên collection
   * @param conditions Điều kiện để lọc
   * @returns Promise<Record<string, any>[]> Danh sách tài liệu thỏa mãn điều kiện
   */
  async getDocumentsByCondition(
    collectionName: string,
    conditions: {
      field: string;
      operator: FirebaseFirestore.WhereFilterOp;
      value: any;
    }[],
  ): Promise<Record<string, any>[]> {
    try {
      let query: FirebaseFirestore.Query<FirebaseFirestore.DocumentData> =
        this.firestore.collection(collectionName);

      // Áp dụng các điều kiện lọc
      conditions.forEach((condition) => {
        query = query.where(
          condition.field,
          condition.operator,
          condition.value,
        );
      });

      const querySnapshot = await query.get();
      const documents = querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      }));

      console.log(
        `Fetched ${documents.length} documents from ${collectionName}`,
      );
      return documents;
    } catch (error) {
      console.error('Failed to fetch documents by condition:', error.message);
      throw new Error('Unable to fetch documents by condition.');
    }
  }

  /**
   * Xóa một document trong Firestore theo ID
   * @param collectionName Tên collection
   * @param key Document ID (key trong Firestore)
   * @returns Promise<void>
   */
  async deleteDocument(collectionName: string, key: string): Promise<void> {
    try {
      const docRef = this.firestore.collection(collectionName).doc(key);
      const docSnapshot = await docRef.get();

      if (!docSnapshot.exists) {
        console.warn(
          `Document not found: Collection=${collectionName}, Key=${key}`,
        );
        return;
      }

      await docRef.delete();
      console.log(
        `Document deleted successfully: Collection=${collectionName}, Key=${key}`,
      );
    } catch (error) {
      console.error('Failed to delete document:', {
        collection: collectionName,
        key: key,
        error: error.message,
        code: error.code,
        details: error.details,
      });
      throw new Error(`Unable to delete document: ${error.message}`);
    }
  }
}
