type FrameItem {
  id: String
  frameId: String
  imageUrl: String
  createdAt: String
  updatedAt: String
}

input FrameItemInput {
  frameId: String
  itemType: String
  itemId: Int
  x_coordinate: Int
  y_coordinate: Int
  width: Int
  height: Int
  parentId: Int
  angle: Float
  frameId: String
}

type Query {
  clientGetFrameItems(frameId: String!): [FrameItem]
}

type Mutation {
  clientCreateFrameItem(input: FrameItemInput): FrameItem
  clientUpdateFrameItem(input: FrameItemInput): FrameItem
  clientDeleteFrameItem(id: String!): FrameItem
}
