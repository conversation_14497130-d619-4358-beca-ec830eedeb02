import { InputType, Field } from '@nestjs/graphql';
import { EFrameItemType } from 'src/enum';

@InputType()
export class FrameItemInput {
  @Field()
  itemType: EFrameItemType;

  @Field()
  frameId: string;

  @Field()
  x_coordinate: number;

  @Field()
  y_coordinate: number;

  @Field()
  width: number;

  @Field()
  height: number;

  @Field({ nullable: true })
  parentId?: string;

  @Field({ nullable: true })
  angle?: number;

  @Field({ nullable: true })
  itemId?: string;
}

@InputType()
export class UpdateFrameItemInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  x_coordinate?: number;

  @Field({ nullable: true })
  y_coordinate?: number;

  @Field({ nullable: true })
  width?: number;

  @Field({ nullable: true })
  height?: number;

  @Field({ nullable: true })
  isDeleted?: boolean;

  @Field({ nullable: true })
  itemId?: string;

  @Field({ nullable: true })
  angle?: number;
}
