import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { FrameItem } from '../../common/entity/frameItem.entity';
import { FrameItemResolver } from './frameItem.resolver';
import { FrameItemService } from './frameItem.service';

@Module({
  imports: [TypeOrmModule.forFeature([FrameItem])],
  providers: [FrameItemService, JwtService, SBLogger, FrameItemResolver],
  exports: [FrameItemService],
})
export class FrameItemModule {}
