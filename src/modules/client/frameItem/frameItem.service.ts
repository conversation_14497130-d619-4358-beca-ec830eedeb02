import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FrameItem } from '../../common/entity/frameItem.entity';
import { FrameItemInput, UpdateFrameItemInput } from './frameItem.input';

@Injectable()
export class FrameItemService {
  constructor(
    @InjectRepository(FrameItem)
    private readonly frameItemRepository: Repository<FrameItem>,
  ) {}

  async findAll(frameId: string): Promise<FrameItem[]> {
    return this.frameItemRepository.find({ where: { frameId } });
  }

  async create(input: FrameItemInput): Promise<FrameItem> {
    try {
      return this.frameItemRepository.save(input);
    } catch (error) {
      throw error;
    }
  }

  async update(input: UpdateFrameItemInput): Promise<FrameItem> {
    try {
      const frameItem = await this.frameItemRepository.findOne({
        where: { id: input.id },
      });
      if (!frameItem) {
        throw new Error('Frame item not found');
      }
      return this.frameItemRepository.save({ ...frameItem, ...input });
    } catch (error) {
      throw error;
    }
  }

  async delete(id: string): Promise<FrameItem> {
    try {
      const frameItem = await this.frameItemRepository.findOne({
        where: { id },
      });
      if (!frameItem) {
        throw new Error('Frame item not found');
      }
      await this.frameItemRepository.delete(id);
      return frameItem;
    } catch (error) {
      throw error;
    }
  }
}
