import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { FrameItem } from '../../common/entity/frameItem.entity';
import { FrameItemInput, UpdateFrameItemInput } from './frameItem.input';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { FrameItemService } from './frameItem.service';
@Resolver(() => FrameItem)
@UseGuards(ClientAuthGuard)
export class FrameItemResolver {
  constructor(private readonly frameItemService: FrameItemService) {}

  @Query(() => [FrameItem])
  async clientGetFrameItems(
    @Args('frameId') frameId: string,
  ): Promise<FrameItem[]> {
    return this.frameItemService.findAll(frameId);
  }

  @Mutation(() => FrameItem)
  async clientCreateFrameItem(
    @Args('input') input: FrameItemInput,
  ): Promise<FrameItem> {
    return this.frameItemService.create(input);
  }

  @Mutation(() => FrameItem)
  async clientUpdateFrameItem(
    @Args('input') input: UpdateFrameItemInput,
  ): Promise<FrameItem> {
    return this.frameItemService.update(input);
  }

  @Mutation(() => Boolean)
  async clientDeleteFrameItem(@Args('id') id: string): Promise<FrameItem> {
    return this.frameItemService.delete(id);
  }
}
