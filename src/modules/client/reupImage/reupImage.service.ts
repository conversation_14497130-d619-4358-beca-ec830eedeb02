import { Injectable } from '@nestjs/common';
import { ReupImageCommonService } from 'src/modules/common/service/reupImage.common.service';

@Injectable()
export class ReupImageService {
  constructor(
    private readonly reupImageCommonService: ReupImageCommonService,
  ) {}

  /**
   * Process a request to reupload images for an order
   * @param reupData Data for the reupload request
   * @param userId ID of the user making the request
   * @returns Message indicating the result of the operation
   */
  async pushToFirestore(
    reupData: Record<string, any>,
    userId: string,
  ): Promise<{ message: string }> {
    // Delegate to common service
    return this.reupImageCommonService.processReuploadRequest(
      reupData.machineId,
      reupData.orderId,
      userId,
      false, // Not an admin request
    );
  }
}
