import { Resolver, Mutation, Context, Args } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { ReupImageService } from './reupImage.service';
import { ReupImageInput, ReupImageResponse } from './reupImage.input';

@Resolver()
@UseGuards(ClientAuthGuard)
export class ReupImageResolver {
  constructor(private readonly reupImageService: ReupImageService) {}

  @Mutation(() => ReupImageResponse)
  async clientReupImage(
    @Args('input') input: ReupImageInput,
    @Context() context: any,
  ): Promise<ReupImageResponse> {
    return await this.reupImageService.pushToFirestore(
      input,
      context.req.user.id,
    );
  }
}
