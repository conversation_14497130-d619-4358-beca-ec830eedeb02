type Layout {
  id: ID!
  name: String
  machineIds: String
  clientId: String
  status: String
  layoutType: String
  createdAt: String
  updatedAt: String
  formats: [LayoutFormat]
}

type LayoutFormat {
  id: ID!
  name: String
  imageCount: Int
  layoutItems: [LayoutItem]
}

type LayoutItem {
  id: ID!
  layoutFormatId: ID
  imageUrl: String
  topicId: ID
  position: Int
}

input CreateLayoutInput {
  name: String
  machineIds: String
  layoutType: String
  status: String
}

input UpdateLayoutInput {
  id: ID!
  name: String
  machineIds: String
  layoutType: String
  status: String
}

type Query {
  clientGetLayouts: [Layout!]!
  clientGetLayout(id: ID!): Layout!
}

type Mutation {
  clientCreateLayout(createLayoutInput: CreateLayoutInput!): Layout
  clientUpdateLayout(updateLayoutInput: UpdateLayoutInput!): Layout
  clientDeleteLayout(id: ID!): Layout
  clientUpdateLayoutFormat(layoutFormatId: string!, layoutId: string): Layout
}
