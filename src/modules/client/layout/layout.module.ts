import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Layout } from '../../common/entity/layout.entity';
import { LayoutService } from './layout.service';
import { LayoutResolver } from './layout.resolver';
import { LayoutFormat } from 'src/modules/common/entity/layoutFormat.entity';
@Module({
  imports: [TypeOrmModule.forFeature([Layout, LayoutFormat])],
  providers: [LayoutService, LayoutResolver, JwtService, SBLogger],
  exports: [LayoutService],
})
export class LayoutModule {}
