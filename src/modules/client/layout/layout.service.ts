import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateLayoutInput, UpdateLayoutInput } from './layout.input';
import { Layout } from 'src/modules/common/entity/layout.entity';
import { TLayoutResponse } from './layout.type';
import { LayoutFormat } from 'src/modules/common/entity/layoutFormat.entity';
import { LayoutItem } from 'src/modules/common/entity/layoutItem.entity';
import { ELayoutFormatStatus } from 'src/enum';

@Injectable()
export class LayoutService {
  constructor(
    @InjectRepository(Layout)
    private readonly layoutRepository: Repository<Layout>,

    @InjectRepository(LayoutFormat)
    private readonly layoutFormatRepository: Repository<LayoutFormat>,
  ) {}

  async findAll(clientId: string): Promise<Layout[]> {
    return this.layoutRepository.find({
      where: { clientId },
      relations: ['formats', 'formats.layoutItems'],
    });
  }

  async findOne(id: string, clientId: string): Promise<TLayoutResponse> {
    return this.layoutRepository.findOne({ where: { id, clientId } });
  }

  async create(
    createLayoutInput: CreateLayoutInput,
    clientId: string,
  ): Promise<TLayoutResponse> {
    try {
      const layout = this.layoutRepository.create(createLayoutInput);
      layout.clientId = clientId;
      const formats: LayoutFormat[] = [3, 4, 5, 6].map((count) => {
        const format = new LayoutFormat();
        format.name = `${count} Images`;
        format.imageCount = count;
        format.layoutItems = Array.from({ length: count }).map((_, index) => {
          const item = new LayoutItem();
          item.position = index + 1;
          return item;
        });
        return format;
      });
      layout.formats = formats;

      return this.layoutRepository.save(layout);
    } catch (error) {
      throw new Error(`Failed to update layout: ${error}`);
    }
  }

  async update(
    updateLayoutInput: UpdateLayoutInput,
    clientId: string,
  ): Promise<TLayoutResponse> {
    try {
      const { id, ...updates } = updateLayoutInput;
      const layout = await this.layoutRepository.findOne({
        where: { id, clientId },
      });
      if (!layout) {
        throw new Error('Layout not found');
      }

      Object.assign(layout, updates);
      return this.layoutRepository.save(layout);
    } catch (error) {
      throw new Error(`Failed to update layout: ${error}`);
    }
  }

  async delete(id: string): Promise<Layout> {
    const layout = await this.layoutRepository.findOne({
      where: { id },
      relations: ['formats'],
    });
    if (!layout) {
      throw new Error('Layout not found');
    }
    const data = { ...layout };
    await this.layoutRepository.remove(layout);
    return data;
  }

  async setDefaultFormat(
    layoutId: string,
    defaultFormatId: string,
    clientId: string,
  ): Promise<Layout> {
    try {
      const layout = await this.layoutRepository.findOne({
        where: { id: layoutId, clientId },
        relations: ['formats', 'formats.layoutItems'],
      });
      if (!layout) {
        throw new Error('Layout not found');
      }
      await this.layoutFormatRepository
        .createQueryBuilder()
        .update(LayoutFormat)
        .set({ status: ELayoutFormatStatus.INACTIVE })
        .where('layoutId = :layoutId', { layoutId })
        .execute();

      await this.layoutFormatRepository
        .createQueryBuilder()
        .update(LayoutFormat)
        .set({ status: ELayoutFormatStatus.ACTIVE })
        .where('id = :layoutFormatId', { layoutFormatId: defaultFormatId })
        .execute();

      const updatedLayout = await this.layoutRepository.findOne({
        where: { id: layoutId, clientId },
        relations: ['formats', 'formats.layoutItems'],
      });

      return updatedLayout!;
    } catch (error) {
      throw new Error(`Failed to update layout: ${error}`);
    }
  }
}
