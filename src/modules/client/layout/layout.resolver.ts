import { Resolver, Query, Mutation, Args, Context, Parent, ResolveField } from '@nestjs/graphql';
import { Layout } from '../../common/entity/layout.entity';
import { CreateLayoutInput, UpdateLayoutInput } from './layout.input';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { TLayoutResponse } from './layout.type';
import { LayoutService } from './layout.service';
import { LayoutFormat } from 'src/modules/common/entity/layoutFormat.entity';
@Resolver(() => Layout)
@UseGuards(ClientAuthGuard)
export class LayoutResolver {
  constructor(private readonly layoutService: LayoutService) {}

  @Query(() => [Layout])
  async clientGetLayouts(@Context() context: any): Promise<Layout[]> {
    return this.layoutService.findAll(context.req.user.id);
  }

  @Query(() => Layout)
  async clientGetLayout(@Args('id') id: string, @Context() context: any): Promise<TLayoutResponse> {
    return this.layoutService.findOne(id, context.req.user.id);
  }

  // @ResolveField(() => [LayoutFormat])
  // async formats(@Parent() layout: Layout): Promise<LayoutFormat[]> {
  //   return layout.formats;
  // }
    
  @Mutation(() => Layout)
  async clientCreateLayout(@Args('input') input: CreateLayoutInput, @Context() context: any): Promise<TLayoutResponse> {
    return this.layoutService.create(input, context.req.user.id);
  }

  @Mutation(() => Layout)
  async clientUpdateLayout(@Args('input') input: UpdateLayoutInput, @Context() context: any): Promise<TLayoutResponse> {
    return this.layoutService.update(input, context.req.user.id);
  }

  @Mutation(() => Layout)
  async clientDeleteLayout(@Args('id') id: string): Promise<Layout> {
    return this.layoutService.delete(id);
  }

  @Mutation(() => Layout)
  async clientSetDefaultFormat(
    @Args('layoutId') layoutId: string, 
    @Args('defaultFormatId') defaultFormatId: string,
    @Context() context: any
  ): Promise<Layout> {
    return this.layoutService.setDefaultFormat(layoutId, defaultFormatId, context.req.user.id);
  }
} 
