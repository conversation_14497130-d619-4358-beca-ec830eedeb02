import { Field, InputType } from '@nestjs/graphql';
import { ELayoutStatus, ELayoutType } from 'src/enum';

@InputType()
export class CreateLayoutInput {
  @Field()
  name: string;

  @Field(() => String, { nullable: true })
  machineIds?: string;

  @Field()
  layoutType: ELayoutType;

  @Field()
  status?: ELayoutStatus;
}

@InputType()
export class UpdateLayoutInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  machineIds?: string;

  @Field({ nullable: true })
  status?: ELayoutStatus;

  @Field({ nullable: true })
  topicId?: string;
}
