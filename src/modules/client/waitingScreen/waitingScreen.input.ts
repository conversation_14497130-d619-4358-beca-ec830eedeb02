import { Field, InputType, ObjectType } from '@nestjs/graphql';
import { EWaitingScreenType } from 'src/enum';

@InputType()
export class CreateWaitingScreenInput {
  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  machineIds?: string;
}

@InputType()
export class UpdateWaitingScreenInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  machineIds?: string;

  @Field(() => [ImageInput], { nullable: true })
  oldImages?: ImageInput[];
}

@InputType()
export class GetWaitingScreenInput {
  @Field({ nullable: true })
  machineId: string;
}

@ObjectType()
export class WaitingScreenResponse {
  @Field()
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field(() => String, { nullable: true })
  machineIds?: string;

  @Field({ nullable: true })
  type?: EWaitingScreenType;

  @Field(() => [ImageResponse], { nullable: 'itemsAndList' })
  images?: ImageResponse[];
}

@ObjectType()
class ImageResponse {
  @Field()
  id: string;

  @Field({ nullable: true })
  url?: string;
}

@InputType()
class ImageInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  url?: string;

  @Field({ nullable: true })
  isDeleted?: boolean;
}
