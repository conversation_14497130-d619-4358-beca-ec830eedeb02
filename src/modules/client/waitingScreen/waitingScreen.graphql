type WaitingScreenResponse {
  id: ID!
  name: String
  machineIds: String
  screenType: String
  images: [String]
}

type Query {
  clientWaitingScreenById(id: ID!): WaitingScreenResponse
  clientWaitingScreens: [WaitingScreenResponse]
}

type Mutation {
  clientCreateWaitingScreen(input: CreateWaitingScreenInput): WaitingScreenResponse
  clientUpdateWaitingScreen(input: UpdateWaitingScreenInput): WaitingScreenResponse
  clientDeleteWaitingScreen(id: ID!): WaitingScreenResponse
}
