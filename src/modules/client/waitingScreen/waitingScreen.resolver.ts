import { Resolver, Mutation, Query, Args, Context, ID } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { WaitingScreen } from 'src/modules/common/entity/waitingScreen.entity';
import { WaitingScreenService } from './waitingScreen.service';
import { TWaitingScreenResponse } from './waitingScreen.type';
import { GraphQLUpload } from 'graphql-upload';
import { FileUpload } from 'graphql-upload';

import {
  CreateWaitingScreenInput,
  GetWaitingScreenInput,
  WaitingScreenResponse,
  UpdateWaitingScreenInput,
} from './waitingScreen.input';

@Resolver(() => WaitingScreen)
@UseGuards(ClientAuthGuard)
export class WaitingScreenResolver {
  constructor(private waitingScreenService: WaitingScreenService) {}

  @Query(() => [WaitingScreenResponse])
  async clientWaitingScreens(@Args('input') input: GetWaitingScreenInput, @Context() context: any): Promise<WaitingScreenResponse[]> {
    return this.waitingScreenService.findAll(input, context.req.user.id);
  }

  @Query(() => WaitingScreenResponse) 
  async clientWaitingScreenById(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<TWaitingScreenResponse> {
    return this.waitingScreenService.findOneById(id);
  }

  @Mutation(() => WaitingScreenResponse)
  async clientCreateWaitingScreen(
    @Args('input') input: CreateWaitingScreenInput,
    @Args({ name: 'files', type: () => [GraphQLUpload] }) files: FileUpload[],
    @Context() context: any,
  ): Promise<TWaitingScreenResponse> {
    return this.waitingScreenService.create(input, files, context.req.user.id);
  }

  @Mutation(() => WaitingScreenResponse)
  async clientUpdateWaitingScreen(
    @Context() context: any,
    @Args('input') input: UpdateWaitingScreenInput,
    @Args({ name: 'files', type: () => [GraphQLUpload], nullable: 'itemsAndList' }) files?: FileUpload[],    
  ): Promise<TWaitingScreenResponse> {
    return this.waitingScreenService.update(input, files, context.req.user.id);
  }

  @Mutation(() => WaitingScreen)
  async clientDeleteWaitingScreen(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any,
  ): Promise<WaitingScreenResponse> {
    return this.waitingScreenService.delete(id, context.req.user.id);
  }
}
