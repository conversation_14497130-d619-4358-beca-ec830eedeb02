import { Client } from '../../common/entity/client.entity';
import { TMachineResponse } from 'src/modules/admin/machine/machine.type';
import { WaitingScreen } from 'src/modules/common/entity/waitingScreen.entity';
import { WaitingScreenImage } from 'src/modules/common/entity/waitingScreenImage.entity';

export type TWaitingScreenResponse = WaitingScreen & {
  machines?: TMachineResponse[];
  user?: Client;
  images?: { id: string; url: string }[];
};
