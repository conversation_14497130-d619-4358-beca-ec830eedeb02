import { Module } from '@nestjs/common';
import { WaitingScreenService } from './waitingScreen.service';
import { WaitingScreenResolver } from './waitingScreen.resolver';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WaitingScreen } from 'src/modules/common/entity/waitingScreen.entity';
import { Client } from 'src/modules/common/entity/client.entity';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { SBLogger } from 'src/modules/logger/logger.service';
import { WaitingScreenImage } from 'src/modules/common/entity/waitingScreenImage.entity';
import { JwtService } from '@nestjs/jwt';
import { FileService } from 'src/modules/common/module/file/file.service';
@Module({
  imports: [TypeOrmModule.forFeature([WaitingScreen, Client, Machine, WaitingScreenImage])],
  providers: [JwtService, WaitingScreenService, WaitingScreenResolver, SBLogger, FileService],
  exports: [WaitingScreenService],
})
export class WaitingScreenModule {}

