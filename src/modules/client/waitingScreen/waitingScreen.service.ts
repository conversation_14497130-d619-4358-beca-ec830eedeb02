import {
  Injectable,
  NotFoundException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import * as moment from 'moment';
import { EWaitingScreenType } from 'src/enum';
import { WaitingScreen } from 'src/modules/common/entity/waitingScreen.entity';
import { WaitingScreenImage } from 'src/modules/common/entity/waitingScreenImage.entity';
import { FileService } from 'src/modules/common/module/file/file.service';
import { Repository } from 'typeorm';
import { Client } from '../../common/entity/client.entity';
import { SBLogger } from '../../logger/logger.service';
import { CreateWaitingScreenInput, GetWaitingScreenInput, UpdateWaitingScreenInput, WaitingScreenResponse } from './waitingScreen.input';
import { TWaitingScreenResponse } from './waitingScreen.type';

@Injectable()
export class WaitingScreenService {
  constructor(
    @InjectRepository(WaitingScreen)
    private waitingScreenRepository: Repository<WaitingScreen>,

    @InjectRepository(WaitingScreenImage)
    private waitingScreenImageRepository: Repository<Client>,
    
    private loggerService: SBLogger,

    private fileService: FileService, 
  ) {}


  async findAll(input: GetWaitingScreenInput, clientId: string): Promise<WaitingScreenResponse[]> {
    const res = this.waitingScreenRepository.find({
      where: {
        clientId,
      },
      relations: ['images']
    });
    return res;
  }

  async findOneById(id: string): Promise<TWaitingScreenResponse> {
    const result = await this.waitingScreenRepository.findOneBy({ id });
    
    const convertRes = {
      ...result,
    };
    return convertRes;
  }

  async create(
    createWaitingScreenInput: CreateWaitingScreenInput,
    files: any,
    clientId: string,
  ): Promise<TWaitingScreenResponse> {
    const createdAt = moment.utc().valueOf().toString();

    const fileUrls = await Promise.all(
      files.map((file) => {
        if (file && typeof file === 'object') {
          return file.then(async (data) => {
            const stream = data.createReadStream();
            const buffer = await new Promise<Buffer>((resolve, reject) => {
              const chunks: Buffer[] = [];
              stream.on('data', (chunk) => chunks.push(chunk));
              stream.on('end', () => resolve(Buffer.concat(chunks)));
              stream.on('error', reject);
            });
    
            const url = await this.fileService.singleUpload(buffer, data.filename, `waitingScreens/${clientId}`);
            return { url };
          });
        }
        return Promise.resolve(null);
      })
    );
    
    const validFileUrls = fileUrls.filter((url) => url !== null);
    
    const images = validFileUrls.map((file) => {
      const image = new WaitingScreenImage();
      image.url = file.url;
      return image;
    });
    
    const tmp = this.waitingScreenRepository.create({
      ...createWaitingScreenInput,
      type: EWaitingScreenType.Custom,
      clientId,
      createdAt,
      images,
    });
    const result = await this.waitingScreenRepository.save(tmp);
    const convertResult = {
      ...result,
      images: result.images,
    };

    return convertResult;
  }

  async update(
    updateWaitingScreenInput: UpdateWaitingScreenInput,
    files: any,
    clientId: string,
  ): Promise<TWaitingScreenResponse> {
    const { id, oldImages, name, machineIds } = updateWaitingScreenInput;
  
    const waitingScreen = await this.waitingScreenRepository.findOne({
      where: { id, clientId },
      relations: ['images'], // Bao gồm mối quan hệ với bảng images
    });
  
    if (!waitingScreen) {
      throw new NotFoundException('WaitingScreen not found');
    }
  
    const imagesToDelete = oldImages?.filter((img) => img.isDeleted) || [];
    const imagesToKeep = oldImages?.filter((img) => !img.isDeleted) || [];
  
    for (const image of imagesToDelete) {
      await this.waitingScreenImageRepository.delete(image.id);
  
      // await this.fileService.deleteFile(image.url);
    }

    if (!files || !Array.isArray(files)) {
      this.loggerService.log('No files uploaded');
      files = [];
    } 
    const fileUrls = await Promise.all(
      files.map((file) => {
        if (file && typeof file === 'object') {
          return file.then(async (data) => {
            const stream = data.createReadStream();
            const buffer = await new Promise<Buffer>((resolve, reject) => {
              const chunks: Buffer[] = [];
              stream.on('data', (chunk) => chunks.push(chunk));
              stream.on('end', () => resolve(Buffer.concat(chunks)));
              stream.on('error', reject);
            });
  
            const url = await this.fileService.singleUpload(buffer, data.filename, `waitingScreens/${clientId}`);
            return { url };
          });
        }
        return Promise.resolve(null);
      }),
    );
  
    // Chuyển URL thành danh sách ảnh mới
    const newImages = fileUrls.map((url) => {
      const image = new WaitingScreenImage();
      image.url = url.url;
      return image;
    });
  
    waitingScreen.images = [
      ...waitingScreen.images.filter((img) =>
        imagesToKeep.some((keepImg) => keepImg.id === img.id),
      ),
      ...newImages,
    ];
  
    if (name) {
      waitingScreen.name = name;
    }
    if (machineIds) {
      waitingScreen.machineIds = machineIds;
    }
  
    const updatedWaitingScreen = await this.waitingScreenRepository.save(waitingScreen);

    return {
      ...updatedWaitingScreen,
      images: updatedWaitingScreen.images,
    };
  }
  

  async delete(id: string, clientId: string): Promise<WaitingScreenResponse> {
    const tmp = await this.waitingScreenRepository.findOneBy({ id, clientId });
    if (!tmp) {
      this.loggerService.log('ERROR - order - delete', 'Order not found');
      throw new NotFoundException('Order not found');
    }
    const deleted = { ...tmp };
    await this.waitingScreenRepository.remove(tmp);
    return {
      ...deleted,
      images: [],
    };
  }
}
