import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import {
  GetClientAccountInput,
  ClientClientAccountResponse,
  CreateClientAccountInput,
  UpdateClientAccountInput,
} from './clientAccount.input';
import * as bcrypt from 'bcrypt';
import * as moment from 'moment';
import { SBLogger } from '../../logger/logger.service';
import { JwtService } from '@nestjs/jwt';
import { ClientAccount } from 'src/modules/common/entity/clientAccount.entity';
import { Client } from 'src/modules/common/entity/client.entity';
@Injectable()
export class ClientAccountService {
  constructor(
    @InjectRepository(ClientAccount)
    private clientAccountRepository: Repository<ClientAccount>,
    @InjectRepository(Client)
    private clientRepository: Repository<Client>,
    private loggerService: SBLogger,
    private jwtService: JwtService,
  ) {}

  async findOneById(id: string, clientId: string): Promise<ClientAccount> {
    const res = this.clientAccountRepository.findOneBy({ id, clientId });
    return res;
  }

  async create(
    createClientInput: CreateClientAccountInput,
    clientId: string,
  ): Promise<ClientAccount> {
    if (createClientInput.email) {
      const duplicateClients = await this.clientRepository.find({
        where: [
          {
            email: createClientInput.email,
          },
        ],
      });
      const duplicateClientAccounts = await this.clientAccountRepository.find({
        where: [
          {
            email: createClientInput.email,
          },
        ],
      });

      [...duplicateClientAccounts, ...duplicateClients]
        .filter((client) => !!client)
        .forEach((client) => {
          if (client.email === createClientInput.email) {
            this.loggerService.log(
              'ERROR - client account - create',
              'Email already exists',
            );
            throw new BadRequestException('Email đã tồn tại trong hệ thống');
          }
        });
    }

    const hashPassword = await bcrypt.hash(createClientInput.password, 10);
    const tmp = await this.clientAccountRepository.create({
      ...createClientInput,
      clientId,
      password: hashPassword,
      role: 'STAFF',
      createdAt: moment.utc().valueOf().toString(),
    });

    const res = await this.clientAccountRepository.save(tmp);
    return res;
  }

  async update(
    updateClientInput: UpdateClientAccountInput,
    clientId: string,
  ): Promise<ClientAccount> {
    const tmp = await this.clientAccountRepository.findOneBy({
      id: updateClientInput.id,
      clientId,
    });
    if (!tmp) {
      this.loggerService.log(
        'ERROR - client account - update',
        'Client account not found',
      );
      throw new NotFoundException('Client account not found');
    }

    if (updateClientInput.email) {
      const duplicateClients = await this.clientRepository.find({
        where: [
          {
            email: updateClientInput.email,
          },
        ],
      });
      const duplicateClientAccounts = await this.clientAccountRepository.find({
        where: [
          {
            email: updateClientInput.email,
            id: Not(updateClientInput.id),
          },
        ],
      });

      [...duplicateClientAccounts, ...duplicateClients]
        .filter((client) => !!client)
        .forEach((client) => {
          if (client.email === updateClientInput.email) {
            this.loggerService.log(
              'ERROR - client account - update',
              'Email already exists',
            );
            throw new BadRequestException('Email đã tồn tại trong hệ thống');
          }
        });
    }

    const hashPassword = updateClientInput.password
      ? await bcrypt.hash(updateClientInput.password, 10)
      : tmp.password;

    const res = this.clientAccountRepository.save({
      ...tmp,
      ...updateClientInput,
      password: hashPassword,
      updatedAt: moment.utc().valueOf().toString(),
    });

    return res;
  }

  async delete(id: string, clientId: string): Promise<ClientAccount> {
    const tmp = await this.clientAccountRepository.findOneBy({ id, clientId });
    if (!tmp) {
      this.loggerService.log(
        'ERROR - client account - delete',
        'Client account not found',
      );
      throw new NotFoundException('Client account not found');
    }
    const res = await this.clientAccountRepository.remove(tmp);
    return res;
  }

  async getClientAccounts(
    input: GetClientAccountInput,
    clientId: string,
  ): Promise<Omit<ClientClientAccountResponse, 'password'>[]> {
    const clientAccounts = await this.clientAccountRepository.find({
      where: {
        ...input,
        clientId,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    const res = await Promise.all(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      clientAccounts.map(async ({ password, ...clientAccount }) => {
        return {
          ...clientAccount,
        };
      }),
    );

    return res;
  }
}
