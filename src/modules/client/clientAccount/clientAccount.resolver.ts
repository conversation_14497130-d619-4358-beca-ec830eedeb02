import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import {
  CreateClientAccountInput,
  GetClientAccountInput,
  UpdateClientAccountInput,
  ClientClientAccountResponse,
} from './clientAccount.input';
import { ClientAccountService } from './clientAccount.service';
import { Client } from '../../common/entity/client.entity';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { ClientAccount } from 'src/modules/common/entity/clientAccount.entity';

@Resolver(() => Client)
@UseGuards(ClientAuthGuard)
export class ClientAccountResolver {
  constructor(private clientAccountService: ClientAccountService) {}

  @Mutation(() => [ClientClientAccountResponse])
  async clientClientAccounts(
    @Args('input') input: GetClientAccountInput,
    @Context() context: any,
  ): Promise<Omit<ClientClientAccountResponse, 'password'>[]> {
    return this.clientAccountService.getClientAccounts(
      input,
      context.req.user.id,
    );
  }

  @Query(() => ClientAccount)
  async clientClientAccountById(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any,
  ): Promise<ClientAccount> {
    return this.clientAccountService.findOneById(id, context.req.user.id);
  }

  @Mutation(() => ClientAccount)
  async clientCreateClientAccount(
    @Args('input') input: CreateClientAccountInput,
    @Context() context: any,
  ): Promise<ClientAccount> {
    return this.clientAccountService.create(input, context.req.user.id);
  }

  @Mutation(() => ClientAccount)
  async clientUpdateClientAccount(
    @Args('input') input: UpdateClientAccountInput,
    @Context() context: any,
  ): Promise<ClientAccount> {
    return this.clientAccountService.update(input, context.req.user.id);
  }

  @Mutation(() => ClientAccount)
  async clientDeleteClientAccount(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any,
  ): Promise<ClientAccount> {
    return this.clientAccountService.delete(id, context.req.user.id);
  }
}
