import { Field, InputType, ObjectType } from '@nestjs/graphql';

@InputType()
export class CreateClientAccountInput {
  @Field()
  name: string;

  @Field()
  email: string;

  @Field()
  password: string;
}

@InputType()
export class UpdateClientAccountInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  password?: string;

  @Field({ nullable: true })
  email?: string;
}

@InputType()
export class GetClientAccountInput {
  @Field({ nullable: true })
  name: string;

  @Field({ nullable: true })
  email?: string;
}

@ObjectType()
export class ClientClientAccountResponse {
  @Field()
  id: string;

  @Field()
  role: string;

  @Field()
  clientId: string;

  @Field()
  name: string;

  @Field()
  email: string;

  @Field()
  createdAt: string;
}
