type ClientClientAccountResponse {
  id: ID!
  clientId: ID!
  name: String!
  email: String!
  createdAt: String!
}

input CreateClientAccountInput {
  name: String!
  email: String!
  password: String!
}

input UpdateClientAccountInput {
  id: ID
  name: String!
  password: String!
}

input GetClientAccountInput {
  name: String!
  email: String!
}

type AdminClientGenerateFramesRes {
  isSuccess: Boolean!
}

type ClientLoginRes {
  id: ID!
  email: String!
  token: String!
}

type Query {
  clientClientAccountById(id: ID!): ClientClientAccountResponse
}

type Mutation {
  clientClientAccounts(input: GetClientAccountInput): [ClientClientAccountResponse]
  clientCreateClientAccount(input: CreateClientAccountInput): ClientClientAccountResponse
  clientUpdateClientAccount(input: UpdateClientAccountInput): ClientClientAccountResponse
  clientDeleteClientAccount(id: ID!): ClientClientAccountResponse
}
