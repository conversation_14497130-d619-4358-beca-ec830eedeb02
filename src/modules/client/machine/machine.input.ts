import { Field, InputType } from '@nestjs/graphql';
import { EMachineStatus } from 'src/enum';

@InputType()
export class ClientCreateMachineInput {
  @Field()
  machineId: string;

  @Field()
  price: number;

  @Field({ nullable: true })
  status?: EMachineStatus;

  @Field({ nullable: true })
  havePayScreen: boolean;
}

@InputType()
export class ClientUpdateMachineInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  location?: string;
}

@InputType()
export class ClientGetMachineInput {
  @Field({ nullable: true })
  machineId: string;

  @Field({ nullable: true })
  machineCode: string;

  @Field({ nullable: true })
  status?: EMachineStatus;

  @Field({ nullable: true })
  havePayScreen: boolean;

  @Field({ nullable: true })
  haveOrder: boolean;

  @Field({ nullable: true })
  userId: string;

  @Field({ nullable: true })
  location?: string;
}
