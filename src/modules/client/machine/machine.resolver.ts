import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { MachineService } from './machine.service';
import { TMachineResponse } from './machine.type';
import {
  ClientGetMachineInput,
  ClientUpdateMachineInput,
} from './machine.input';
import {
  Machine,
  MachineResponse,
} from 'src/modules/common/entity/machine.entity';

@Resolver(() => Machine)
@UseGuards(ClientAuthGuard)
export class MachineResolver {
  constructor(private machineService: MachineService) {}

  @Mutation(() => [MachineResponse])
  async clientMachines(
    @Args('input') input: ClientGetMachineInput,
    @Context() context: any,
  ): Promise<TMachineResponse[]> {
    return this.machineService.getMachines(input, context.req.user);
  }

  @Query(() => MachineResponse)
  async clientMachineById(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any,
  ): Promise<TMachineResponse> {
    return this.machineService.findOneById(id, context.req.user);
  }

  @Mutation(() => MachineResponse)
  async clientUpdateMachine(
    @Args('input') input: ClientUpdateMachineInput,
    @Context() context: any,
  ): Promise<TMachineResponse> {
    return this.machineService.update(input, context.req.user);
  }
}
