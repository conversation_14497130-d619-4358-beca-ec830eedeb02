import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MachineService } from './machine.service';
import { MachineResolver } from './machine.resolver';
import { JwtService } from '@nestjs/jwt';
import { Client } from '../../common/entity/client.entity';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { MachineBrand } from 'src/modules/admin/machineBrand/machineBrand.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Client, Machine, MachineBrand])],
  providers: [JwtService, MachineService, MachineResolver, SBLogger],
  exports: [MachineService],
})
export class MachineModule {}
