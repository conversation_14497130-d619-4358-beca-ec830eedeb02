import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SBLogger } from '../../logger/logger.service';
import { get } from 'lodash';
import { Client } from '../../common/entity/client.entity';
import { MachineBrand } from '../../admin/machineBrand/machineBrand.entity';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { TMachineResponse } from './machine.type';
import {
  ClientGetMachineInput,
  ClientUpdateMachineInput,
} from './machine.input';
@Injectable()
export class MachineService {
  constructor(
    @InjectRepository(Machine)
    private machineRepository: Repository<Machine>,

    @InjectRepository(MachineBrand)
    private machineBrandRepository: Repository<MachineBrand>,

    @InjectRepository(Client)
    private clientRepository: Repository<Client>,

    private loggerService: SBLogger,
  ) {}

  async findAll(user: Client): Promise<Machine[]> {
    const res = await this.machineRepository.findBy({ userId: user.id });
    return res;
  }

  async findByIds(ids: string[]): Promise<TMachineResponse[]> {
    const result = await this.machineRepository.findByIds(ids);
    const convertRes = await this.convertRef(result);
    return convertRes;
  }

  async findOneById(id: string, user: Client): Promise<TMachineResponse> {
    const result = await this.machineRepository.findOneBy({
      id,
      userId: user.id,
    });
    const convertRes = await this.convertRef([result]);
    return convertRes[0];
  }

  async convertRef(result: Machine[]): Promise<TMachineResponse[]> {
    let tmp = await this.mappingProp(
      result,
      'machineId',
      'machineBrand',
      this.machineBrandRepository,
    );
    tmp = await this.mappingProp(tmp, 'userId', 'user', this.clientRepository);
    return tmp;
  }

  async mappingProp(
    clients,
    prop,
    targetProp,
    repository,
  ): Promise<TMachineResponse[]> {
    const ids = clients.map((el) => el[prop]);
    if (ids.length === 0) return clients;

    const mapping = {};
    (await repository.findByIds(ids)).map((el) => {
      mapping[el.id] = el;
    });
    return clients.map((el) => ({
      ...el,
      [targetProp]: mapping[el[prop]],
    }));
  }

  async getMachines(
    input: ClientGetMachineInput,
    user: Client,
  ): Promise<TMachineResponse[]> {
    const { machineCode, havePayScreen, status } = input;
    let clients = await this.machineRepository.findBy({
      machineCode,
      havePayScreen,
      status,
      userId: user.id,
    });

    if (get(input, 'haveOrder') !== undefined) {
      clients = clients.filter(
        (el) => Boolean(el.orderId) === !!input.haveOrder,
      );
    }

    clients = await this.convertRef(clients);
    return clients;
  }

  async update(
    input: ClientUpdateMachineInput,
    user: Client,
  ): Promise<TMachineResponse> {
    const machine = await this.machineRepository.findOne({
      where: {
        id: input.id,
        userId: user.id,
      },
      relations: ['machineBrand'],
    });
    if (!machine) {
      throw new Error('Machine not found');
    }
    return this.machineRepository.save({ ...machine, ...input });
  }
}
