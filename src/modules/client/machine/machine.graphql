type MachineBrand {
  id: ID!
  brandCode: String!
  name: String!
  description: String!
}

type User {
  id: ID!
  name: String!
  email: String!
}

type MachineResponse {
  id: ID!
  machineBrand: MachineBrand!
  machineCode: String!
  user: User!
  orderId: String!
  rentalDate: String!
  renewalDate: String!
  havePayScreen: Boolean!
  status: String!
  price: Int!
  location: String!
}

type Machine {
  id: ID!
  machineCode: String!
  rentalDate: String!
  renewalDate: String!
  havePayScreen: Boolean!
  status: String!
  price: Int!
  location: String!
}

input ClientGetMachineInput {
  machineCode: String!
  machineName: String!
  havePayScreen: Boolean!
  status: String!
  haveOrder: Boolean!
}

input ClientUpdateMachineInput {
  id: ID!
  location: String
}

type Query {
  clientMachineById(id: ID!): MachineResponse
}

type Mutation {
  clientMachines(input: ClientGetMachineInput): [MachineResponse]
  clientMachineById(id: ID!): MachineResponse
  clientUpdateMachine(input: UpdateMachineInput): MachineResponse
}
