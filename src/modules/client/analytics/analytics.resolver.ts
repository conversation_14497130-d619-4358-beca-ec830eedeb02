import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { AnalyticsService } from './analytics.service';
import {
  AnalyticsOrdersDataResponse,
  AnalyticsResponse,
  ExportOrdersResponse,
  ParamsInput,
  RevenueChartResponse,
  TopFramesResponse,
  TopMachinesResponse,
  TopTopicsResponse,
} from './analytics.input';

@Resolver('Analytics')
@UseGuards(ClientAuthGuard)
export class AnalyticsResolver {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Query(() => AnalyticsResponse)
  async clientRevenue(
    @Args('startDate') startDate: string,
    @Args('endDate') endDate: string,
    @Context() context: any,
    @Args('machineIds', { type: () => [String], nullable: true })
    machineIds?: string[],
  ): Promise<AnalyticsResponse> {
    return this.analyticsService.revenue(
      startDate,
      endDate,
      machineIds,
      context.req.user.id,
    );
  }

  @Query(() => AnalyticsOrdersDataResponse)
  async clientAnalyticsOrders(
    @Args('startDate') startDate: string,
    @Args('endDate') endDate: string,
    @Context() context: any,
    @Args('paramsInput', { type: () => ParamsInput }) paramsInput: ParamsInput,
  ): Promise<AnalyticsOrdersDataResponse> {
    return this.analyticsService.orders(
      startDate,
      endDate,
      paramsInput,
      context.req.user.id,
    );
  }

  @Query(() => [TopTopicsResponse])
  async clientTopTopics(
    @Args('startDate') startDate: string,
    @Args('endDate') endDate: string,
    @Context() context: any,
    @Args('machineIds', { type: () => [String], nullable: true })
    machineIds?: string[],
  ): Promise<TopTopicsResponse[]> {
    return this.analyticsService.topTopics(
      startDate,
      endDate,
      context.req.user.id,
      machineIds,
    );
  }

  @Query(() => [TopFramesResponse])
  async clientTopFrames(
    @Args('startDate') startDate: string,
    @Args('endDate') endDate: string,
    @Context() context: any,
    @Args('machineIds', { type: () => [String], nullable: true })
    machineIds?: string[],
  ): Promise<TopFramesResponse[]> {
    return this.analyticsService.topFrames(
      startDate,
      endDate,
      context.req.user.id,
      machineIds,
    );
  }

  @Query(() => [TopMachinesResponse])
  async topMachines(
    @Args('startDate') startDate: string,
    @Args('endDate') endDate: string,
    @Context() context: any,
    @Args('machineIds', { type: () => [String], nullable: true })
    machineIds?: string[],
  ): Promise<TopMachinesResponse[]> {
    return this.analyticsService.topMachines(
      startDate,
      endDate,
      context.req.user.id,
      machineIds,
    );
  }

  @Query(() => RevenueChartResponse)
  async clienGetRevenueChart(
    @Args('startDate') startDate: string,
    @Args('endDate') endDate: string,
    @Context() context: any,
  ): Promise<RevenueChartResponse> {
    return this.analyticsService.revenueChart(
      startDate,
      endDate,
      context.req.user.id,
    );
  }

  @Query(() => ExportOrdersResponse)
  async clientExportOrders(
    @Args('startDate') startDate: string,
    @Args('endDate') endDate: string,
    @Context() context: any,
    @Args('machineIds', { type: () => [String], nullable: true })
    machineIds?: string[],
    @Args('fileType', { type: () => String, nullable: true, defaultValue: 'csv' })
    fileType?: 'csv' | 'xlsx',
  ): Promise<ExportOrdersResponse> {
    return this.analyticsService.exportOrders(
      startDate,
      endDate,
      context.req.user.id,
      machineIds,
      fileType as 'csv' | 'xlsx',
    );
  }
}
