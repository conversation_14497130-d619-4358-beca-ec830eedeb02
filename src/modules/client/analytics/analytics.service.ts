import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, In, Repository } from 'typeorm';
import { SBLogger } from '../../logger/logger.service';
import { Order } from 'src/modules/clientApp/order/order.entity';
import {
  AnalyticsOrdersDataResponse,
  AnalyticsResponse,
  RevenueChartResponse,
  TopFramesResponse,
  TopMachinesResponse,
  TopTopicsResponse,
} from './analytics.input';
import { EPaymentMethod } from 'src/enum';
import * as moment from 'moment-timezone';
import * as XLSX from 'xlsx';
import {
  translateEnum,
  paymentMethodTranslations,
  orderStatusTranslations,
  orderAppStatusTranslations,
} from '../../../utils/enumTranslations';

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    private readonly logger: SBLogger,
  ) {}

  async revenue(
    startDate: string,
    endDate: string,
    machineIds: string[],
    clientId: string,
  ): Promise<AnalyticsResponse> {
    try {
      const start = moment
        .unix(parseInt(startDate))
        .tz('Asia/Ho_Chi_Minh')
        .startOf('day');
      const end = moment
        .unix(parseInt(endDate))
        .tz('Asia/Ho_Chi_Minh')
        .endOf('day');
      // Tính toán khoảng thời gian
      const duration = this.calculateDuration(start, end);
      const previousPeriod = this.calculatePreviousPeriod(start, end, duration);
      // Lấy đơn hàng hiện tại và kỳ trước
      const [orders, previousOrders] = await this.fetchOrdersForPeriods(
        clientId,
        start,
        end,
        previousPeriod.start,
        previousPeriod.end,
        machineIds,
      );

      // Tính toán các thông số
      const totalReceivedAmount = this.calculateTotalAmount(orders);
      const totalReceivedAmountPrevious =
        this.calculateTotalAmount(previousOrders);

      const growthPercentage = this.calculateGrowth(
        totalReceivedAmount,
        totalReceivedAmountPrevious,
      );
      const growthOrders = this.calculateGrowth(
        orders.length,
        previousOrders.length,
      );

      // Tính toán theo phương thức thanh toán
      const totalBankTranfer = this.calculateTotalByPaymentMethod(
        orders,
        EPaymentMethod.ONLINE,
      );
      const totalCash = this.calculateTotalByPaymentMethod(
        orders,
        EPaymentMethod.OFFLINE,
      );

      // Trả về kết quả
      return this.buildAnalyticsResponse({
        totalReceivedAmount,
        totalReceivedAmountPrevious,
        growthPercentage,
        totalBankTranfer,
        totalCash,
        totalOrders: orders.length,
        totalPreviousOrders: previousOrders.length,
        growthOrders,
      });
    } catch (error) {
      this.handleError(error);
    }
  }

  async revenueChart(
    startDate: string,
    endDate: string,
    clientId: string,
  ): Promise<RevenueChartResponse> {
    try {
      const start = moment
        .unix(parseInt(startDate))
        .tz('Asia/Ho_Chi_Minh')
        .startOf('day');
      const end = moment
        .unix(parseInt(endDate))
        .tz('Asia/Ho_Chi_Minh')
        .endOf('day');
      const isSameDay = moment(start.format('YYYY-MM-DD')).isSame(
        end.format('YYYY-MM-DD'),
        'day',
      );

      // Tính toán khoảng thời gian
      const duration = this.calculateDuration(start, end);
      const previousPeriod = this.calculatePreviousPeriod(start, end, duration);

      const currentResults = await this.getRevenueData(
        start,
        end,
        clientId,
        isSameDay,
      );
      const previousResults = await this.getRevenueData(
        previousPeriod.start,
        previousPeriod.end,
        clientId,
        isSameDay,
      );
      return {
        current: currentResults.map((result) => ({
          time: result.group,
          value: parseInt(result.totalOrders),
          revenue: parseInt(result.totalAmount),
        })),
        previous: previousResults.map((result) => ({
          time: result.group,
          value: parseInt(result.totalOrders),
          revenue: parseInt(result.totalAmount),
        })),
      };
    } catch (error) {
      this.handleError(error);
    }
  }

  private async getRevenueData(
    startDate: moment.Moment,
    endDate: moment.Moment,
    clientId: string,
    isSameDay: boolean,
  ): Promise<any[]> {
    const queryBuilder = this.orderRepository.createQueryBuilder('order');

    queryBuilder.select(
      isSameDay
        ? "DATE_FORMAT(CONVERT_TZ(order.createdAt, '+00:00', '+07:00'), '%H:00') as `group`"
        : "DATE_FORMAT(CONVERT_TZ(order.createdAt, '+00:00', '+07:00'), '%Y-%m-%d') as `group`",
    );
    queryBuilder.addSelect('SUM(order.totalOrderNumber)', 'totalAmount');
    queryBuilder.addSelect('COUNT(order.id)', 'totalOrders');
    queryBuilder.where('order.clientId = :clientId', { clientId });
    queryBuilder.andWhere(
      'CONVERT_TZ(order.createdAt, "+00:00", "+07:00") BETWEEN :start AND :end',
      {
        start: startDate.format('YYYY-MM-DD HH:mm:ss'),
        end: endDate.format('YYYY-MM-DD HH:mm:ss'),
      },
    );
    queryBuilder.groupBy('`group`');
    queryBuilder.orderBy('`group`', 'ASC');

    return queryBuilder.getRawMany();
  }

  async orders(
    startDate: string,
    endDate: string,
    params: any,
    clientId: string,
  ): Promise<AnalyticsOrdersDataResponse> {
    const {
      paymentType,
      page = 1,
      limit = 10,
      frameIds,
      topicIds,
      machineIds,
    } = params;

    const start = new Date(Number(startDate) * 1000); // Chuyển từ UNIX timestamp sang Date
    const end = new Date(Number(endDate) * 1000);

    const whereConditions: any = {
      createdAt: Between(start, end),
      clientId,
    };

    if (paymentType) {
      whereConditions.paymentMethod = paymentType;
    }

    if (frameIds && frameIds.length > 0) {
      whereConditions.frameId = In(frameIds);
    }

    if (topicIds && topicIds.length > 0) {
      whereConditions.topicId = In(topicIds);
    }

    if (machineIds && machineIds.length > 0) {
      whereConditions.machineId = In(machineIds);
    }

    // Sử dụng repository để thực hiện truy vấn
    const [orders, total] = await this.orderRepository.findAndCount({
      where: whereConditions,
      relations: [
        'promotionCode',
        'promotionCode.promotion',
        'frame',
        'topic',
        'settingSize',
        'machine',
        'images',
      ], // Liên kết các bảng liên quan
      skip: (page - 1) * limit,
      withDeleted: true,
      relationLoadStrategy: 'query',
      take: limit,
      order: { createdAt: 'DESC' },
    });

    const totalSum = await this.orderRepository
      .createQueryBuilder('order')
      .select('SUM(order.totalOrderNumber)', 'sum')
      .addSelect('SUM(order.receivedAmount)', 'totalRecievedAmount')
      .addSelect('SUM(order.amount)', 'totalAmount')
      .where(whereConditions)
      .getRawOne();

    const totalOrderSum = totalSum?.sum || 0;
    const totalRecievedAmount = totalSum?.totalRecievedAmount || 0;
    const totalAmount = totalSum?.totalAmount || 0;
    const ordersWithDomain = orders.map((order) => ({
      ...order,
      domain: `${process.env.MEMORY_DOMAIN}${order.id}`,
    }));

    return {
      data: ordersWithDomain,
      total,
      currentPage: page,
      pageSize: limit,
      totalOrderNumber: totalOrderSum,
      totalRecievedAmount: totalRecievedAmount,
      totalAmount: totalAmount,
    };
  }

  async topTopics(
    startDate: string,
    endDate: string,
    clientId: string,
    machineIds: string[],
  ): Promise<TopTopicsResponse[]> {
    try {
      const result = await this.orderRepository
        .createQueryBuilder('order')
        .select('topic.id', 'topicId')
        .addSelect('topic.name', 'topicName')
        .addSelect('COUNT(order.id)', 'orderCount')
        .innerJoin('order.topic', 'topic')
        .where('order.clientId = :clientId', { clientId })
        .andWhere('order.createdAt BETWEEN :startDate AND :endDate', {
          startDate: new Date(Number(startDate) * 1000),
          endDate: new Date(Number(endDate) * 1000),
        })
        .andWhere(
          machineIds && machineIds.length > 0
            ? 'order.machineId IN (:...machineIds)'
            : '1=1',
          {
            machineIds,
          },
        )
        .groupBy('topic.id')
        .addGroupBy('topic.name')
        .orderBy('orderCount', 'DESC')
        .limit(10)
        .getRawMany();
      return result.map((row) => ({
        id: row.topicId,
        name: row.topicName,
        total: Number(row.orderCount),
      }));
    } catch (error) {
      this.handleError(error);
    }
  }

  async topFrames(
    startDate: string,
    endDate: string,
    clientId: string,
    machineIds: string[],
  ): Promise<TopFramesResponse[]> {
    try {
      const result = await this.orderRepository
        .createQueryBuilder('order')
        .select('frame.id', 'frameId')
        .addSelect('frame.imageUrl', 'frameImage')
        .addSelect('frame.description', 'frameDescription')
        .addSelect('COUNT(frame.id)', 'frameCount')
        .innerJoin('order.frame', 'frame')
        .where('order.clientId = :clientId', { clientId })
        .andWhere('order.createdAt BETWEEN :startDate AND :endDate', {
          startDate: new Date(Number(startDate) * 1000),
          endDate: new Date(Number(endDate) * 1000),
        })
        .andWhere(
          machineIds && machineIds.length > 0
            ? 'order.machineId IN (:...machineIds)'
            : '1=1',
          {
            machineIds,
          },
        )
        .groupBy('frame.id')
        .orderBy('frameCount', 'DESC')
        .limit(10)
        .getRawMany();
      return result.map((row) => ({
        id: row.frameId,
        description: row.frameDescription,
        frameImage: row.frameImage,
        total: Number(row.frameCount),
      }));
    } catch (error) {
      this.handleError(error);
    }
  }

  async topMachines(
    startDate: string,
    endDate: string,
    clientId: string,
    machineIds: string[],
  ): Promise<TopMachinesResponse[]> {
    try {
      const result = await this.orderRepository
        .createQueryBuilder('order')
        .select('machine.id', 'machineId')
        .addSelect('machine.location', 'machineLocation')
        .addSelect('machine.machineCode', 'machineCode')
        .addSelect('COUNT(machine.id)', 'machineCount')
        .innerJoin('order.machine', 'machine')
        .where('order.clientId = :clientId', { clientId })
        .andWhere(
          machineIds && machineIds.length > 0
            ? 'order.machineId IN (:...machineIds)'
            : '1=1',
          {
            machineIds,
          },
        )
        .andWhere('order.createdAt BETWEEN :startDate AND :endDate', {
          startDate: new Date(Number(startDate) * 1000),
          endDate: new Date(Number(endDate) * 1000),
        })
        .groupBy('machine.id')
        .orderBy('machineCount', 'DESC')
        .limit(10)
        .getRawMany();
      return result.map((row) => ({
        id: row.machineId,
        machineCode: row.machineCode,
        total: Number(row.machineCount),
        location: row.machineLocation,
      }));
    } catch (error) {
      this.handleError(error);
    }
  }

  private calculateDuration(start: moment.Moment, end: moment.Moment): number {
    return end.diff(start, 'day') + 1;
  }

  private calculatePreviousPeriod(
    start: moment.Moment,
    end: moment.Moment,
    duration: number,
  ): { start: moment.Moment; end: moment.Moment } {
    return {
      start: start.clone().subtract(duration, 'day'),
      end: end.clone().subtract(duration, 'day'),
    };
  }

  private async fetchOrdersForPeriods(
    clientId: string,
    start: moment.Moment,
    end: moment.Moment,
    previousStart: moment.Moment,
    previousEnd: moment.Moment,
    machineIds: string[],
  ): Promise<[Order[], Order[]]> {
    const fetchOrders = async (start: moment.Moment, end: moment.Moment) => {
      return this.orderRepository
        .createQueryBuilder('order')
        .where('order.clientId = :clientId', { clientId })
        .andWhere(
          'CONVERT_TZ(order.createdAt, "+00:00", "+07:00") BETWEEN :startDate AND :endDate',
          {
            startDate: start.format('YYYY-MM-DD HH:mm:ss'),
            endDate: end.format('YYYY-MM-DD HH:mm:ss'),
          },
        )
        .andWhere(
          machineIds && machineIds.length > 0
            ? 'order.machineId IN (:...machineIds)'
            : '1=1',
          {
            machineIds,
          },
        )
        .getMany();
    };

    return Promise.all([
      fetchOrders(start, end),
      fetchOrders(previousStart, previousEnd),
    ]);
  }

  private calculateTotalAmount(orders: Order[]): number {
    return orders.reduce(
      (total, order) => total + (Number(order.totalOrderNumber) || 0),
      0,
    );
  }

  private calculateGrowth(current: number, previous: number): number {
    return previous !== 0 ? ((current - previous) / previous) * 100 : 100;
  }

  private calculateTotalByPaymentMethod(
    orders: Order[],
    method: EPaymentMethod,
  ): number {
    return orders
      .filter((order) => order.paymentMethod === method)
      .reduce((total, order) => total + (Number(order.receivedAmount) || 0), 0);
  }

  private buildAnalyticsResponse(
    data: Partial<AnalyticsResponse>,
  ): AnalyticsResponse {
    return {
      totalReceivedAmount: data.totalReceivedAmount || 0,
      totalReceivedAmountPrevious: data.totalReceivedAmountPrevious || 0,
      growthPercentage: data.growthPercentage || 0,
      totalBankTranfer: data.totalBankTranfer || 0,
      totalCash: data.totalCash || 0,
      totalOrders: data.totalOrders || 0,
      totalPreviousOrders: data.totalPreviousOrders || 0,
      growthOrders: data.growthOrders || 0,
    };
  }

  private handleError(error: any): void {
    console.error('Error: ', error);
    throw error;
  }
  async exportOrders(
    startDate: string,
    endDate: string,
    clientId: string,
    machineIds?: string[],
    fileType: 'csv' | 'xlsx' = 'csv', // Thêm tham số fileType
  ): Promise<{
    success: boolean;
    message: string;
    fileName: string;
    fileData: string;
  }> {
    try {
      const start = new Date(Number(startDate) * 1000);
      const end = new Date(Number(endDate) * 1000);

      // Build query conditions
      const whereConditions: any = {
        createdAt: Between(start, end),
        clientId,
      };

      if (machineIds && machineIds.length > 0) {
        whereConditions.machineId = In(machineIds);
      }

      // Fetch orders with all relations
      const orders = await this.orderRepository.find({
        where: whereConditions,
        relations: [
          'promotionCode',
          'promotionCode.promotion',
          'frame',
          'topic',
          'settingSize',
          'machine',
          'images',
        ],
        order: { createdAt: 'DESC' },
        withDeleted: true,
      });

      const headers = [
        'Mã Đơn Hàng', // Order ID
        'Mã Code Đơn Hàng', // Order Code
        'Ngày Tạo', // Created Date
        'Tổng Tiền', // Amount
        'Số Tiền Đã Nhận', // Received Amount
        'Tổng Số Đơn Hàng', // Total Order Number
        'Phương Thức Thanh Toán', // Payment Method
        'Trạng Thái', // Status
        'Số Lượng Ảnh', // Image Number
        'Mã Tham Chiếu', // Reference Code
        'Mô Tả', // Description
        'Mã Máy', // Machine Code
        'Vị Trí Máy', // Machine Location
        'Tên Khung Hình', // Frame Name
        'Tên Chủ Đề', // Topic Name
        'Mã Khuyến Mãi', // Promotion Code
        'Giảm Giá Khuyến Mãi', // Promotion Discount
        'Số Lượng Ảnh', // Image Count
        'Url Ảnh', // Memory Link
      ];

      const data = orders.map((order) => [
        order.id || '',
        order.orderCode || '',
        order.createdAt ? new Date(order.createdAt).toLocaleString() : '',
        (order.amount || 0).toString(),
        (order.receivedAmount || 0).toString(),
        (order.totalOrderNumber || 0).toString(),
        translateEnum(order.paymentMethod || '', paymentMethodTranslations),
        translateEnum(
          order.status || '',
          order.status in orderStatusTranslations
            ? orderStatusTranslations
            : orderAppStatusTranslations,
        ),
        (order.imageNumber || 0).toString(),
        order.refCode || '',
        order.description || '',
        order.machine?.machineCode || '',
        order.machine?.location || '',
        order.frame?.description || '',
        order.topic?.name || '',
        order.promotionCode?.code || '',
        order.promotionCode?.promotion?.discountValue
          ? order.promotionCode.promotion.discountValue.toString()
          : '0',
        order.images ? order.images.length.toString() : '0',
        `${process.env.MEMORY_DOMAIN || ''}${order.id}`,
      ]);

      // Create a worksheet with formatting options
      const ws = XLSX.utils.aoa_to_sheet([headers, ...data]);

      // Define styles
      const headerStyle = {
        font: { bold: true, color: { rgb: 'FFFFFF' } },
        fill: { fgColor: { rgb: '4472C4' } },
        alignment: { horizontal: 'center', vertical: 'center', wrapText: true },
        border: {
          top: { style: 'thin', color: { rgb: '000000' } },
          bottom: { style: 'thin', color: { rgb: '000000' } },
          left: { style: 'thin', color: { rgb: '000000' } },
          right: { style: 'thin', color: { rgb: '000000' } },
        },
      };

      const dataStyle = {
        border: {
          top: { style: 'thin', color: { rgb: '000000' } },
          bottom: { style: 'thin', color: { rgb: '000000' } },
          left: { style: 'thin', color: { rgb: '000000' } },
          right: { style: 'thin', color: { rgb: '000000' } },
        },
        alignment: { vertical: 'center', wrapText: true },
      };

      const alternateRowStyle = {
        ...dataStyle,
        fill: { fgColor: { rgb: 'F2F2F2' } },
      };

      // Apply styles to header row
      const range = XLSX.utils.decode_range(ws['!ref']);

      // Set column widths
      const colWidths = [
        { wch: 36 }, // Mã Đơn Hàng
        { wch: 15 }, // Mã Code Đơn Hàng
        { wch: 20 }, // Ngày Tạo
        { wch: 12 }, // Tổng Tiền
        { wch: 15 }, // Số Tiền Đã Nhận
        { wch: 15 }, // Tổng Số Đơn Hàng
        { wch: 20 }, // Phương Thức Thanh Toán
        { wch: 15 }, // Trạng Thái
        { wch: 12 }, // Số Lượng Ảnh
        { wch: 15 }, // Mã Tham Chiếu
        { wch: 25 }, // Mô Tả
        { wch: 15 }, // Mã Máy
        { wch: 25 }, // Vị Trí Máy
        { wch: 25 }, // Tên Khung Hình
        { wch: 20 }, // Tên Chủ Đề
        { wch: 15 }, // Mã Khuyến Mãi
        { wch: 20 }, // Giảm Giá Khuyến Mãi
        { wch: 12 }, // Số Lượng Ảnh
        { wch: 40 }, // Url Ảnh
      ];

      ws['!cols'] = colWidths;

      // Apply styles to all cells
      for (let row = range.s.r; row <= range.e.r; row++) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellRef = XLSX.utils.encode_cell({ r: row, c: col });
          if (!ws[cellRef]) continue;

          if (row === 0) {
            // Header row
            ws[cellRef].s = headerStyle;
          } else if (row % 2 === 0) {
            // Even rows (alternate color)
            ws[cellRef].s = alternateRowStyle;
          } else {
            // Odd rows
            ws[cellRef].s = dataStyle;
          }
        }
      }

      // Tạo workbook
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, 'Orders');

      // Tạo tên file
      const dateStr = new Date().toISOString().slice(0, 10);
      const fileName = `orders_export_${dateStr}.${fileType}`;

      // Tùy chọn ghi file
      const writeOptions = {
        type: 'buffer' as const,
        bookType: fileType,
        compression: true,
        bookSST: false,
      };

      // Ghi ra buffer
      const buf = XLSX.write(wb, writeOptions);

      // Chuyển đổi buffer thành base64 một cách an toàn
      // Sử dụng phương thức khác để tạo base64
      let fileData = '';

      // Nếu là file Excel, sử dụng cách khác để tạo base64
      if (fileType === 'xlsx') {
        // Ghi trực tiếp ra base64
        fileData = XLSX.write(wb, {
          type: 'base64' as const,
          bookType: fileType,
          compression: true,
        });
      } else {
        // Cho các định dạng khác, sử dụng cách thông thường
        fileData = Buffer.from(buf).toString('base64');
      }

      // Đảm bảo không có dấu ngoặc kép và ký tự không hợp lệ
      fileData = fileData.replace(/^"|"$/g, '').replace(/\s/g, '');

      return {
        success: true,
        message: `Successfully exported ${orders.length} orders`,
        fileName,
        fileData,
      };
    } catch (error) {
      this.handleError(error);
    }
  }
}
