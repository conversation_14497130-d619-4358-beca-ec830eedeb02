import { Module } from '@nestjs/common';
import { AuthModule } from '../client/session/auth.module';
import { MachineModule } from './machine/machine.module';
import { WaitingScreenModule } from './waitingScreen/waitingScreen.module';
import { TopicModule } from './topic/topic.module';
import { SettingSizeModule } from './settingSize/settingSize.module';
import { AppearanceSettingModule } from './appearanceSetting/appearanceSetting.module';
import { BackgroundImageModule } from './backgroundImage/backgroundImage.module';
import { LayoutModule } from './layout/layout.module';
import { FrameModule } from './frame/frame.module';
import { FrameItemModule } from './frameItem/frameItem.module';
import { LayoutItemModule } from './layoutItem/layoutItem.module';
import { PromotionModule } from './promotion/promotion.module';
import { StickerModule } from './sticker/sticker.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { MachineStatusModule } from './machineStatus/machineStatus.module';
import { PrintImageModule } from './printImage/printImage.module';
import { PrintSettingModule } from './printSetting/printSetting.module';
import { ClientAccountModule } from './clientAccount/clientAccount.module';
import { ReupImageModule } from './reupImage/reupImage.module';
import { CostReconciliationModule } from './costReconciliation/costReconciliation.module';

@Module({
  imports: [
    AuthModule,
    ClientAccountModule,
    MachineModule,
    WaitingScreenModule,
    TopicModule,
    SettingSizeModule,
    AppearanceSettingModule,
    BackgroundImageModule,
    LayoutModule,
    FrameModule,
    FrameItemModule,
    LayoutItemModule,
    PromotionModule,
    StickerModule,
    AnalyticsModule,
    MachineStatusModule,
    PrintImageModule,
    PrintSettingModule,
    ReupImageModule,
    CostReconciliationModule,
  ],
})
export class ClientModule {}
