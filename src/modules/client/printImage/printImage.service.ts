import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SBLogger } from '../../logger/logger.service';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { Machine } from 'src/modules/common/entity/machine.entity';

@Injectable()
export class PrintImageService {
  constructor(
    @InjectRepository(Machine)
    private readonly machineRepository: Repository<Machine>,

    private readonly loggerService: SBLogger,
    private readonly firestoreService: FirestoreService,
  ) {}

  async pushToFirestore(
    printData: Record<string, any>,
    userId: string,
  ): Promise<{ message: string }> {
    try {
      const machine = await this.machineRepository.findOne({
        where: { userId, id: printData.machineId },
      });

      if (!machine) {
        throw new NotFoundException('Machine not found.');
      }

      const machineId = machine.id;
      const existingData = await this.firestoreService.getData(
        'ImagePrint',
        machineId,
      );
      if (existingData) {
        return {
          message: 'Print job already exists. No new data was pushed.',
        };
      }

      if (printData.imageUrl?.includes('https')) {
        await this.firestoreService.pushData(
          'ImagePrint',
          machineId,
          printData,
          true,
        );

        return { message: 'Print job pushed to Firestore successfully' };
      }

      return { message: 'Print image not found' };
    } catch (error) {
      this.loggerService.log(
        `Error pushing print job to Firestore: ${error.message}`,
      );
      throw new BadRequestException('Failed to push print job to Firestore.');
    }
  }
}
