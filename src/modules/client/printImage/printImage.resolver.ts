import { Resolver, Mutation, Context, Args } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { PrintImageService } from './printImage.service';
import { PrintImageInput, PrintImageResponse } from './printImage.input';

@Resolver()
@UseGuards(ClientAuthGuard)
export class PrintImageResolver {
  constructor(private readonly printImageService: PrintImageService) {}

  @Mutation(() => PrintImageResponse)
  async clientPrintImage(
    @Args('input') input: PrintImageInput,
    @Context() context: any,
  ): Promise<PrintImageResponse> {
    return await this.printImageService.pushToFirestore(
      input,
      context.req.user.id,
    );
  }
}
