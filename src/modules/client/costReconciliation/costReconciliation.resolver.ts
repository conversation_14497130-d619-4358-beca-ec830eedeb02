import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { CostReconciliationService } from './costReconciliation.service';
import {
  GetCostReconciliationInput,
  CostReconciliationListResponse,
  GetReconciliationCodeByMonthInput,
  ReconciliationCodeResponse,
} from './costReconciliation.input';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermissions,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';

@Resolver()
export class CostReconciliationResolver {
  constructor(private costReconciliationService: CostReconciliationService) {}

  /**
   * Helper method to extract clientId from authentication context
   */
  private getClientIdFromContext(context: any): string {
    const clientId = context.req.user?.id || context.req.user?.clientId;

    if (!clientId) {
      throw new Error('Client ID not found in authentication context');
    }

    return clientId;
  }

  // Cost Reconciliation Resolvers
  @Query(() => CostReconciliationListResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENTS)
  async clientCostReconciliations(
    @Args('input') input: GetCostReconciliationInput,
  ): Promise<CostReconciliationListResponse> {
    return this.costReconciliationService.getCostReconciliations(input);
  }

  // Get reconciliation code by month
  @Query(() => ReconciliationCodeResponse, { nullable: true })
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENTS)
  async getReconciliationCodeByMonth(
    @Args('input') input: GetReconciliationCodeByMonthInput,
    @Context() context: any,
  ): Promise<ReconciliationCodeResponse | null> {
    // Get clientId from context (auto-filled for client users)
    const clientId = input.clientId || this.getClientIdFromContext(context);

    return this.costReconciliationService.getReconciliationCodeByMonth(
      clientId,
      input.month,
    );
  }
}
