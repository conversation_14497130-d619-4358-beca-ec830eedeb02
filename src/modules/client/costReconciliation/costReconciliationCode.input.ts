import { InputType, Field, ObjectType } from '@nestjs/graphql';
import { IsString, IsNumber, Matches } from 'class-validator';

// Input Types
@InputType()
export class GenerateReconciliationCodeInput {
  @Field()
  @IsString()
  @Matches(/^\d{4}-\d{2}$/, {
    message: 'Month must be in format YYYY-MM (e.g., 2025-01)',
  })
  month: string; // Format: YYYY-MM

  @Field()
  @IsNumber()
  amount: number;
}

// Response Types
@ObjectType()
export class GenerateReconciliationCodeResponse {
  @Field()
  refCode: string;

  @Field()
  month: string;

  @Field()
  amount: number;

  @Field()
  generatedAt: string;
}
