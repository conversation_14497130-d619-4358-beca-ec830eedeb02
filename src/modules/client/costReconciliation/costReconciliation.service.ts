import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CostReconciliation } from '../../common/entity/costReconciliation.entity';

import {
  GetCostReconciliationInput,
  CostReconciliationResponse,
  CostReconciliationListResponse,
} from './costReconciliation.input';

import { convertDateRangeFromUnix } from '../../../utils/time.utils';
import {
  generateReconciliationCode,
  parseOrderIds,
  stringifyOrderIds,
  mergeOrderIds,
  calculateDiscountAmount,
  createMonthKeyFromDate,
} from '../../../utils/reconciliation.util';

@Injectable()
export class CostReconciliationService {
  constructor(
    @InjectRepository(CostReconciliation)
    private costReconciliationRepository: Repository<CostReconciliation>,
  ) {}

  // Cost Reconciliation Methods
  async getCostReconciliations(
    input: GetCostReconciliationInput,
  ): Promise<CostReconciliationListResponse> {
    const {
      clientId,
      status,
      startDate,
      endDate,
      page = 1,
      limit = 10,
    } = input;

    const queryBuilder = this.costReconciliationRepository
      .createQueryBuilder('cr')
      .leftJoinAndSelect('cr.client', 'client');

    if (clientId) {
      queryBuilder.andWhere('cr.clientId = :clientId', { clientId });
    }

    if (status) {
      queryBuilder.andWhere('cr.status = :status', { status });
    }

    if (startDate && endDate) {
      const { startDate: startDateObj, endDate: endDateObj } =
        convertDateRangeFromUnix(startDate.toString(), endDate.toString());
      queryBuilder.andWhere(
        'cr.reconciliationDate BETWEEN :startDate AND :endDate',
        {
          startDate: startDateObj,
          endDate: endDateObj,
        },
      );
    }

    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);
    queryBuilder.orderBy('cr.createdAt', 'DESC');

    const [costReconciliations, total] = await queryBuilder.getManyAndCount();

    const data: CostReconciliationResponse[] = costReconciliations.map((cr) => {
      return {
        id: cr.id,
        clientId: cr.clientId,
        clientName: cr.client?.name || '',
        revenue: cr.revenue,
        discountAmount: cr.discountAmount,
        status: cr.status,
        reconciliationDate: cr.reconciliationDate,
        createdAt: cr.createdAt,
        updatedAt: cr.updatedAt,
        createdBy: cr.createdBy,
        updatedBy: cr.updatedBy,
        notes: cr.notes,
        totalOrders: cr.totalOrders,
        processedOrderIds: cr.processedOrderIds,
        reconciliationCode: cr.reconciliationCode,
      };
    });

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  /**
   * Generate a unique reconciliation code that doesn't exist in database
   * @returns Promise<string> Unique reconciliation code
   */
  async generateUniqueReconciliationCode(): Promise<string> {
    let code: string;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 10;

    while (!isUnique && attempts < maxAttempts) {
      code = generateReconciliationCode();

      const existingRecord = await this.costReconciliationRepository.findOne({
        where: { reconciliationCode: code },
      });

      if (!existingRecord) {
        isUnique = true;
      }

      attempts++;
    }

    if (!isUnique) {
      throw new Error(
        'Failed to generate unique reconciliation code after maximum attempts',
      );
    }

    return code;
  }

  /**
   * Find or create cost reconciliation for a specific month
   * If found: add new orders to existing record
   * If not found: create new record with orders
   *
   * @param clientId Client ID
   * @param orderIds Array of new order IDs to add
   * @param revenue Revenue from new orders
   * @param month Month in format YYYY-MM (e.g., "2025-01")
   * @returns Promise<CostReconciliationResponse>
   */
  async findOrCreateMonthlyReconciliation(
    clientId: string,
    orderIds: string[],
    revenue: number,
    month: string, // Format: YYYY-MM
  ): Promise<CostReconciliationResponse> {
    // Parse month and create reconciliation date (first day of month)
    const [year, monthNum] = month.split('-').map(Number);
    const monthDate = new Date(year, monthNum - 1, 1); // First day of month for reconciliation

    // Try to find existing reconciliation for this month
    // More efficient: search by exact reconciliation date (first day of month)
    const existingReconciliation = await this.costReconciliationRepository
      .createQueryBuilder('cr')
      .where('cr.clientId = :clientId', { clientId })
      .andWhere('cr.reconciliationDate = :monthDate', {
        monthDate,
      })
      .getOne();

    if (existingReconciliation) {
      // Found existing record - add new orders to it
      return this.addOrdersToExistingReconciliation(
        existingReconciliation,
        orderIds,
        revenue,
      );
    } else {
      // Not found - create new record
      return this.createNewMonthlyReconciliation(
        clientId,
        orderIds,
        revenue,
        monthDate, // Use first day of month as reconciliation date
      );
    }
  }

  /**
   * Add new orders to existing cost reconciliation record
   */
  private async addOrdersToExistingReconciliation(
    existingRecord: CostReconciliation,
    newOrderIds: string[],
    newRevenue: number,
  ): Promise<CostReconciliationResponse> {
    // Parse existing processed order IDs
    const existingOrderIds = parseOrderIds(existingRecord.processedOrderIds);

    // Merge with new order IDs (removes duplicates)
    const mergedOrderIds = mergeOrderIds(existingOrderIds, newOrderIds);

    // Calculate updated values
    const updatedRevenue = existingRecord.revenue + newRevenue;
    const updatedTotalOrders = mergedOrderIds.length;
    const updatedDiscountAmount = calculateDiscountAmount(
      updatedRevenue,
      updatedTotalOrders,
    );

    // Update the record
    existingRecord.processedOrderIds = stringifyOrderIds(mergedOrderIds);
    existingRecord.revenue = updatedRevenue;
    existingRecord.totalOrders = updatedTotalOrders;
    existingRecord.discountAmount = updatedDiscountAmount;
    existingRecord.updatedBy = 'system';

    const savedRecord =
      await this.costReconciliationRepository.save(existingRecord);

    // Load with client relation for response
    const recordWithClient = await this.costReconciliationRepository.findOne({
      where: { id: savedRecord.id },
      relations: ['client'],
    });

    return {
      id: recordWithClient.id,
      clientId: recordWithClient.clientId,
      clientName: recordWithClient.client?.name || '',
      revenue: recordWithClient.revenue,
      discountAmount: recordWithClient.discountAmount,
      status: recordWithClient.status,
      reconciliationDate: recordWithClient.reconciliationDate,
      createdAt: recordWithClient.createdAt,
      updatedAt: recordWithClient.updatedAt,
      createdBy: recordWithClient.createdBy,
      updatedBy: recordWithClient.updatedBy,
      notes: recordWithClient.notes,
      totalOrders: recordWithClient.totalOrders,
      processedOrderIds: recordWithClient.processedOrderIds,
      reconciliationCode: recordWithClient.reconciliationCode,
    };
  }

  /**
   * Create new monthly cost reconciliation record
   */
  private async createNewMonthlyReconciliation(
    clientId: string,
    orderIds: string[],
    revenue: number,
    reconciliationDate: Date,
  ): Promise<CostReconciliationResponse> {
    // Generate unique reconciliation code
    const reconciliationCode = await this.generateUniqueReconciliationCode();

    // Calculate discount amount
    const totalOrders = orderIds.length;
    const discountAmount = calculateDiscountAmount(revenue, totalOrders);

    // Create new record
    const newRecord = this.costReconciliationRepository.create({
      clientId,
      revenue,
      discountAmount,
      status: 'NOT_RECONCILED' as any, // ECostReconciliationStatus.NOT_RECONCILED
      processedOrderIds: stringifyOrderIds(orderIds),
      totalOrders,
      reconciliationDate,
      reconciliationCode,
      createdBy: 'system',
      notes: `Auto-generated monthly reconciliation for ${reconciliationDate.getFullYear()}-${String(reconciliationDate.getMonth() + 1).padStart(2, '0')} (${totalOrders} orders)`,
    });

    const savedRecord = await this.costReconciliationRepository.save(newRecord);

    // Load with client relation for response
    const recordWithClient = await this.costReconciliationRepository.findOne({
      where: { id: savedRecord.id },
      relations: ['client'],
    });

    return {
      id: recordWithClient.id,
      clientId: recordWithClient.clientId,
      clientName: recordWithClient.client?.name || '',
      revenue: recordWithClient.revenue,
      discountAmount: recordWithClient.discountAmount,
      status: recordWithClient.status,
      reconciliationDate: recordWithClient.reconciliationDate,
      createdAt: recordWithClient.createdAt,
      updatedAt: recordWithClient.updatedAt,
      createdBy: recordWithClient.createdBy,
      updatedBy: recordWithClient.updatedBy,
      notes: recordWithClient.notes,
      totalOrders: recordWithClient.totalOrders,
      processedOrderIds: recordWithClient.processedOrderIds,
      reconciliationCode: recordWithClient.reconciliationCode,
    };
  }

  /**
   * Process order for cost reconciliation
   * This method should be called when an order is completed/paid
   *
   * @param clientId Client ID
   * @param orderId Order ID
   * @param revenue Order revenue (totalOrderNumber)
   * @param orderDate Order creation date
   * @returns Promise<CostReconciliationResponse>
   */
  async processOrderForReconciliation(
    clientId: string,
    orderId: string,
    revenue: number,
    orderDate: Date,
  ): Promise<CostReconciliationResponse> {
    // Create month key from order date
    const monthKey = createMonthKeyFromDate(orderDate);

    // Find or create monthly reconciliation and add this order
    return this.findOrCreateMonthlyReconciliation(
      clientId,
      [orderId],
      revenue,
      monthKey,
    );
  }

  /**
   * Get reconciliation code by month for a specific client
   * @param clientId Client ID
   * @param month Month in format YYYY-MM
   * @returns Reconciliation code and details
   */
  async getReconciliationCodeByMonth(
    clientId: string,
    month: string,
  ): Promise<any> {
    // Parse month and create reconciliation date (first day of month)
    const [year, monthNum] = month.split('-').map(Number);
    const monthDate = new Date(year, monthNum - 1, 1);

    // Find reconciliation for this month
    const reconciliation = await this.costReconciliationRepository
      .createQueryBuilder('cr')
      .leftJoinAndSelect('cr.client', 'client')
      .where('cr.clientId = :clientId', { clientId })
      .andWhere('cr.reconciliationDate = :monthDate', { monthDate })
      .getOne();

    if (!reconciliation) {
      return null;
    }

    return {
      refCode: reconciliation.reconciliationCode,
      month,
      clientId: reconciliation.clientId,
      clientName: reconciliation.client?.name || '',
      revenue: reconciliation.revenue,
      discountAmount: reconciliation.discountAmount,
      totalOrders: reconciliation.totalOrders,
      status: reconciliation.status,
      generatedAt: reconciliation.createdAt,
    };
  }
}
