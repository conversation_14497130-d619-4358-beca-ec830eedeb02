import { Resolver, Mutation, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { CostReconciliationCodeService } from '../../common/module/costReconciliationCode/costReconciliationCode.service';
import {
  GenerateReconciliationCodeInput,
  GenerateReconciliationCodeResponse,
} from './costReconciliationCode.input';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermissions,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';

@Resolver()
export class CostReconciliationCodeResolver {
  constructor(
    private costReconciliationCodeService: CostReconciliationCodeService,
  ) {}

  /**
   * Helper method to extract clientId from authentication context
   */
  private getClientIdFromContext(context: any): string {
    const clientId = context.req.user?.id || context.req.user?.clientId;

    if (!clientId) {
      throw new Error('Client ID not found in authentication context');
    }

    return clientId;
  }

  /**
   * Generate cost reconciliation reference code
   */
  @Mutation(() => GenerateReconciliationCodeResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENTS)
  async generateCostReconciliationCode(
    @Args('input') input: GenerateReconciliationCodeInput,
    @Context() context: any,
  ): Promise<GenerateReconciliationCodeResponse> {
    const clientId = this.getClientIdFromContext(context);

    return this.costReconciliationCodeService.generateReconciliationCode(
      clientId,
      input.month,
      input.amount,
    );
  }

  /**
   * Generate code for current month (helper endpoint)
   */
  @Mutation(() => GenerateReconciliationCodeResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENTS)
  async generateCurrentMonthReconciliationCode(
    @Args('amount') amount: number,
    @Context() context: any,
  ): Promise<GenerateReconciliationCodeResponse> {
    const clientId = this.getClientIdFromContext(context);

    // Get current month in YYYY-MM format
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const currentMonth = `${year}-${month}`;

    return this.costReconciliationCodeService.generateReconciliationCode(
      clientId,
      currentMonth,
      amount,
    );
  }
}
