type Query {
  clientCostReconciliations(input: GetCostReconciliationInput!): CostReconciliationListResponse!
}

type Mutation {
  clientCreateCostReconciliation(input: CreateCostReconciliationInput!): CostReconciliationResponse!
  clientUpdateCostReconciliation(input: UpdateCostReconciliationInput!): CostReconciliationResponse!
  clientGenerateCostReconciliationFromOrders(clientId: ID!, startDate: Float!, endDate: Float!): CostReconciliationResponse!
}

input GetCostReconciliationInput {
  clientId: String
  status: ECostReconciliationStatus
  startDate: Float
  endDate: Float
  page: Int = 1
  limit: Int = 10
}

input CreateCostReconciliationInput {
  clientId: String!
  revenue: Float!
  discountAmount: Float = 0
  status: ECostReconciliationStatus!
  orderIds: String!
  totalOrders: Int = 0
  reconciliationDate: DateTime!
  notes: String
}

input UpdateCostReconciliationInput {
  id: String!
  status: ECostReconciliationStatus
  notes: String
}

type CostReconciliationResponse {
  id: String!
  clientId: String!
  clientName: String!
  revenue: Float!
  discountAmount: Float!
  status: ECostReconciliationStatus!
  orderIds: String!
  orderIdsList: [String!]!
  reconciliationDate: DateTime!
  createdAt: DateTime!
  updatedAt: DateTime!
  createdBy: String
  updatedBy: String
  notes: String
  totalOrders: Int!
}

type CostReconciliationListResponse {
  data: [CostReconciliationResponse!]!
  total: Int!
  page: Int!
  limit: Int!
  totalPages: Int!
}

enum ECostReconciliationStatus {
  PAID
  UNPAID
  NOT_RECONCILED
}
