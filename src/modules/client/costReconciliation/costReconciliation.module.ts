import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CostReconciliation } from '../../common/entity/costReconciliation.entity';
import { CostReconciliationService } from './costReconciliation.service';
import { CostReconciliationResolver } from './costReconciliation.resolver';
import { CostReconciliationCodeService } from '../../common/module/costReconciliationCode/costReconciliationCode.service';
import { CostReconciliationCodeResolver } from './costReconciliationCode.resolver';

@Module({
  imports: [
    TypeOrmModule.forFeature([CostReconciliation]),
  ],
  providers: [
    CostReconciliationService,
    CostReconciliationResolver,
    CostReconciliationCodeService,
    CostReconciliationCodeResolver,
  ],
  exports: [
    CostReconciliationService,
    CostReconciliationCodeService,
  ],
})
export class CostReconciliationModule {}
