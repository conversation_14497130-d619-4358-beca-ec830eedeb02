import { UseGuards } from '@nestjs/common';
import { Args, Context, ID, Mutation, Query, Resolver } from '@nestjs/graphql';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { CreatePrintSettingInput } from './printSetting.input';
import { PrintSettingService } from './printSetting.service';
import { TPrintSettingResponse } from './printSetting.type';
import { PrintSetting } from 'src/modules/common/entity/printSetting.entity';
import {
  PrintSettingResponse,
  UpdatePrintSettingInput,
} from './printSetting.input';

@Resolver(() => PrintSetting)
@UseGuards(ClientAuthGuard)
export class PrintSettingResolver {
  constructor(private printSettingService: PrintSettingService) {}

  @Query(() => [PrintSettingResponse])
  async clientPrintSettings(
    @Context() context: any,
  ): Promise<PrintSettingResponse[]> {
    return this.printSettingService.findAll(context.req.user.id);
  }

  @Query(() => PrintSettingResponse)
  async clientPrintSettingById(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<TPrintSettingResponse> {
    return this.printSettingService.findOneById(id);
  }

  @Mutation(() => PrintSettingResponse)
  async clientCreatePrintSetting(
    @Args('input') input: CreatePrintSettingInput,
    @Context() context: any,
  ): Promise<TPrintSettingResponse> {
    return this.printSettingService.create(input, context.req.user.id);
  }

  @Mutation(() => PrintSettingResponse)
  async clientUpdatePrintSetting(
    @Context() context: any,
    @Args('input') input: UpdatePrintSettingInput,
  ): Promise<TPrintSettingResponse> {
    return this.printSettingService.update(input, context.req.user.id);
  }

  @Mutation(() => PrintSetting)
  async clientDeleteWaitingScreen(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any,
  ): Promise<PrintSettingResponse> {
    return this.printSettingService.delete(id, context.req.user.id);
  }
}
