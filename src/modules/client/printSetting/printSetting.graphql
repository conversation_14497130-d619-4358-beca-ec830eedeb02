type PrintSettingResponse {
  id: ID!
  machineId: String
  type: String
  adjustColor: Int
  printRetry: Int
  border: Int
  sharpness: Int
  overcoatFinish: Int
  printerQuality: Int
  yResolution: Int
  adjGammaR: Int
  adjGammaG: Int
  adjGammaB: Int
  adjBrightnessR: Int
  adjBrightnessG: Int
  adjBrightnessB: Int
  adjContrastR: Int
  adjContrastG: Int
  adjContrastB: Int
  adjChroma: Int
  icmMethod: Int
}

type Query {
  clientPrintSettingById(id: ID!): PrintSettingResponse
  clientPrintSettings: [PrintSettingResponse]
}

type Mutation {
  clientCreatePrintSetting(input: CreatePrintSettingInput): PrintSettingResponse
  clientUpdatePrintSetting(input: UpdatePrintSettingInput): PrintSettingResponse
  clientDeletePrintSetting(id: ID!): PrintSettingResponse
}
