import { Field, InputType, ObjectType } from '@nestjs/graphql';
import { EPrintSettingType } from 'src/enum';

@InputType()
class BaseInputType {
  @Field({ nullable: true })
  machineId?: string;

  @Field({ nullable: true })
  type?: EPrintSettingType;

  @Field({ nullable: true })
  adjustColor?: number;

  @Field({ nullable: true })
  printRetry?: number;

  @Field({ nullable: true })
  border?: number;

  @Field({ nullable: true })
  sharpness?: number;

  @Field({ nullable: true })
  overcoatFinish?: number;

  @Field({ nullable: true })
  printerQuality?: number;

  @Field({ nullable: true })
  yResolution?: number;

  @Field({ nullable: true })
  adjGammaR?: number;

  @Field({ nullable: true })
  adjGammaG?: number;

  @Field({ nullable: true })
  adjGammaB?: number;

  @Field({ nullable: true })
  adjBrightnessR?: number;

  @Field({ nullable: true })
  adjBrightnessG?: number;

  @Field({ nullable: true })
  adjBrightnessB?: number;

  @Field({ nullable: true })
  adjContrastR?: number;

  @Field({ nullable: true })
  adjContrastG?: number;

  @Field({ nullable: true })
  adjContrastB?: number;

  @Field({ nullable: true })
  adjChroma?: number;

  @Field({ nullable: true })
  icmMethod?: number;
}

@InputType()
export class CreatePrintSettingInput extends BaseInputType {}

@InputType()
export class UpdatePrintSettingInput extends BaseInputType {
  @Field()
  id: string;
}

@InputType()
export class GetPrintSettingInput {
  @Field({ nullable: true })
  machineId: string;
}

@ObjectType()
export class PrintSettingResponse {
  @Field()
  id: string;

  @Field({ nullable: true })
  machineId?: string;

  @Field({ nullable: true })
  type?: EPrintSettingType;

  @Field({ nullable: true })
  adjustColor?: number;

  @Field({ nullable: true })
  printRetry?: number;

  @Field({ nullable: true })
  border?: number;

  @Field({ nullable: true })
  sharpness?: number;

  @Field({ nullable: true })
  overcoatFinish?: number;

  @Field({ nullable: true })
  printerQuality?: number;

  @Field({ nullable: true })
  yResolution?: number;

  @Field({ nullable: true })
  adjGammaR?: number;

  @Field({ nullable: true })
  adjGammaG?: number;

  @Field({ nullable: true })
  adjGammaB?: number;

  @Field({ nullable: true })
  adjBrightnessR?: number;

  @Field({ nullable: true })
  adjBrightnessG?: number;

  @Field({ nullable: true })
  adjBrightnessB?: number;

  @Field({ nullable: true })
  adjContrastR?: number;

  @Field({ nullable: true })
  adjContrastG?: number;

  @Field({ nullable: true })
  adjContrastB?: number;

  @Field({ nullable: true })
  adjChroma?: number;

  @Field({ nullable: true })
  icmMethod?: number;

  @Field({ nullable: true })
  createdAt?: string;

  @Field({ nullable: true })
  updatedAt?: string;
}
