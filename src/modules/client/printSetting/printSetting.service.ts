import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EPrintSettingType } from 'src/enum';
import { PrintSetting } from 'src/modules/common/entity/printSetting.entity';
import { Repository } from 'typeorm';
import { SBLogger } from '../../logger/logger.service';
import {
  CreatePrintSettingInput,
  PrintSettingResponse,
  UpdatePrintSettingInput,
} from './printSetting.input';
import { TPrintSettingResponse } from './printSetting.type';

@Injectable()
export class PrintSettingService {
  constructor(
    @InjectRepository(PrintSetting)
    private printSettingRepository: Repository<PrintSetting>,
    private loggerService: SBLogger,
  ) {}

  async findAll(clientId: string): Promise<PrintSettingResponse[]> {
    const res = this.printSettingRepository.find({
      where: {
        clientId,
      },
    });
    return res;
  }

  async findOneById(id: string): Promise<TPrintSettingResponse> {
    const res = await this.printSettingRepository.findOneBy({ id });
    return res;
  }

  async create(
    createPrintSettingInput: CreatePrintSettingInput,
    clientId: string,
  ): Promise<TPrintSettingResponse> {
    this.loggerService.log(
      'START - waitingScreen - create',
      createPrintSettingInput,
    );
    const tmp = this.printSettingRepository.create({
      ...createPrintSettingInput,
      type: EPrintSettingType.Custom,
      clientId,
    });
    const result = await this.printSettingRepository.save(tmp);
    return result;
  }

  async update(
    updatePrintSettingInput: UpdatePrintSettingInput,
    clientId: string,
  ): Promise<TPrintSettingResponse> {
    const { id } = updatePrintSettingInput;

    const printSetting = await this.printSettingRepository.findOne({
      where: { id, clientId },
    });

    if (!printSetting) {
      throw new NotFoundException('PrintSetting not found');
    }

    for (const key in updatePrintSettingInput) {
      if (key !== 'id') {
        printSetting[key] = updatePrintSettingInput[key];
      }
    }

    const updatedPrintSetting =
      await this.printSettingRepository.save(printSetting);

    return {
      ...updatedPrintSetting,
    };
  }

  async delete(id: string, clientId: string): Promise<PrintSettingResponse> {
    const tmp = await this.printSettingRepository.findOneBy({ id, clientId });
    if (!tmp) {
      this.loggerService.log('ERROR - order - delete', 'Order not found');
      throw new NotFoundException('Order not found');
    }
    const deleted = { ...tmp };
    await this.printSettingRepository.remove(tmp);
    return {
      ...deleted,
    };
  }
}
