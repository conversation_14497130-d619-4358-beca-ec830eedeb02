import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LayoutItem } from 'src/modules/common/entity/layoutItem.entity';
import { UpdateLayoutItemInput } from './layoutItem.input';
import { FileUpload } from 'graphql-upload';
import { FileService } from 'src/modules/common/module/file/file.service';

@Injectable()
export class LayoutItemService {
  constructor(
    @InjectRepository(LayoutItem)
    private readonly layoutItemRepository: Repository<LayoutItem>,
    private readonly fileService: FileService,
  ) {}

  async findAllByFormatId(layoutFormatId: string): Promise<LayoutItem[]> {
    return this.layoutItemRepository.find({ where: { layoutFormatId } });
  }

  async update(
    updateLayoutItemInput: UpdateLayoutItemInput,
    image: FileUpload,
  ): Promise<LayoutItem> {
    try {
      const { id, ...updateData } = updateLayoutItemInput;
      const layoutItem = await this.layoutItemRepository.findOne({
        where: { id: updateLayoutItemInput.id },
      });
      if (!layoutItem) {
        throw new Error('Layout item not found');
      }

      Object.assign(layoutItem, updateData);
      if (image) {
        const { createReadStream, filename } = await image;
        const stream = createReadStream();
        const buffer = await new Promise<Buffer>((resolve, reject) => {
          const chunks: Buffer[] = [];
          stream.on('data', (chunk) => chunks.push(chunk));
          stream.on('end', () => resolve(Buffer.concat(chunks)));
          stream.on('error', reject);
        });
        const url = await this.fileService.singleUpload(
          buffer,
          filename,
          `layoutItems/${id}`,
        );
        layoutItem.imageUrl = url;
      }
      return this.layoutItemRepository.save(layoutItem);
    } catch (error) {
      console.log(error);
      throw new Error('Failed to update layout item');
    }
  }
}
