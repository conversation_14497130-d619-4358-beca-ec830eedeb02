type LayoutItem {
  id: ID!
  layoutFormatId: ID!
  imageUrl: String
  topicId: ID
  position: Int
}

type LayoutItemResponse {
  layoutItem: LayoutItem
  topic: Topic
}

type UpdateLayoutItemInput {
  id: ID!
  imageUrl: String
  topicId: ID
  position: Int
}

type Query {
  clientGetLayoutItemsByFormatId(formatId: ID!): [LayoutItemResponse!]!
}

type Mutation {
  clientUpdateLayoutItem(updateLayoutItemInput: UpdateLayoutItemInput!): LayoutItem
}
