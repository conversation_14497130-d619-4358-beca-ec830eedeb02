import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { LayoutItem } from 'src/modules/common/entity/layoutItem.entity';
import { UpdateLayoutItemInput } from './layoutItem.input';
import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { LayoutItemService } from './layoutItem.service';
@Resolver(() => LayoutItem)
export class LayoutItemResolver {
  constructor(private readonly layoutItemService: LayoutItemService) {}

  @Query(() => [LayoutItem])
  async clientGetLayoutItemsByFormatId(@Args('formatId') formatId: string, @Context() context: any): Promise<LayoutItem[]> {
    return this.layoutItemService.findAllByFormatId(formatId);
  }

  @Mutation(() => LayoutItem)
  async clientUpdateLayoutItem(
    @Args('input') input: UpdateLayoutItemInput, 
    @Args({ name: 'image', type: () => GraphQLUpload, nullable: true }) image?: FileUpload,
  ): Promise<LayoutItem> {
    return this.layoutItemService.update(input, image);
  }
}
