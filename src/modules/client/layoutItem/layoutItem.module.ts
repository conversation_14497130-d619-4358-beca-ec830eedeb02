import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LayoutItem } from 'src/modules/common/entity/layoutItem.entity';
import { LayoutItemService } from './layoutItem.service';
import { LayoutItemResolver } from './layoutItem.resolver';
import { FileService } from 'src/modules/common/module/file/file.service';

@Module({
  imports: [TypeOrmModule.forFeature([LayoutItem])],
  providers: [LayoutItemService, LayoutItemResolver, FileService],
})
export class LayoutItemModule {}
