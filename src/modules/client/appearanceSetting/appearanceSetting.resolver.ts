import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { AppearanceSetting } from '../../common/entity/appearanceSetting.entity';
import {
  AppearanceSettingInput,
  UpdateAppearanceSettingInput,
} from './appearanceSetting.input';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { TAppearanceSettingResponse } from './appearanceSetting.type';
import { AppearanceSettingService } from './appearanceSetting.service';
import { FileUpload } from 'graphql-upload';
import { GraphQLUpload } from 'graphql-upload';

@Resolver(() => AppearanceSetting)
@UseGuards(ClientAuthGuard)
export class AppearanceSettingResolver {
  constructor(
    private readonly appearanceSettingService: AppearanceSettingService,
  ) {}

  @Query(() => AppearanceSetting, { nullable: true })
  async clientGetAppearanceSetting(
    @Context() context: any,
  ): Promise<AppearanceSetting | null> {
    return this.appearanceSettingService.findOne(context.req.user.id);
  }

  @Mutation(() => AppearanceSetting)
  async clientCreateAppearanceSetting(
    @Args('input') input: AppearanceSettingInput,
    @Context() context: any,
    @Args({ name: 'logo', type: () => GraphQLUpload, nullable: true })
    logo?: FileUpload,
    @Args({ name: 'background', type: () => GraphQLUpload, nullable: true })
    background?: FileUpload,
  ): Promise<TAppearanceSettingResponse> {
    return this.appearanceSettingService.create(
      input,
      context.req.user.id,
      logo,
      background,
    );
  }

  @Mutation(() => AppearanceSetting)
  async clientUpdateAppearanceSetting(
    @Args('input') input: UpdateAppearanceSettingInput,
    @Context() context: any,
    @Args({ name: 'logo', type: () => GraphQLUpload, nullable: true })
    logo?: FileUpload,
    @Args({ name: 'background', type: () => GraphQLUpload, nullable: true })
    background?: FileUpload,
  ): Promise<TAppearanceSettingResponse> {
    return this.appearanceSettingService.update(
      input,
      context.req.user.id,
      logo,
      background,
    );
  }

  @Mutation(() => Boolean)
  async clientDeleteAppearanceSetting(
    @Args('id') id: string,
    @Context() context: any,
  ): Promise<boolean> {
    return this.appearanceSettingService.delete(id, context.req.user.id);
  }
}
