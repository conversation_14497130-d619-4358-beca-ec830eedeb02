import { InputType, Field } from '@nestjs/graphql';

@InputType()
export class AppearanceSettingInput {
  @Field({ nullable: true })
  primary_color?: string;

  @Field({ nullable: true })
  secondary_color?: string;

  @Field({ nullable: true })
  background_color?: string;

  @Field({ nullable: true })
  primary_text_color?: string;

  @Field({ nullable: true })
  secondary_text_color?: string;

  @Field({ nullable: true })
  secondary_text_color_2?: string;
}

@InputType()
export class UpdateAppearanceSettingInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  primary_color?: string;

  @Field({ nullable: true })
  secondary_color?: string;

  @Field({ nullable: true })
  background_color?: string;

  @Field({ nullable: true })
  primary_text_color?: string;

  @Field({ nullable: true })
  secondary_text_color?: string;

  @Field({ nullable: true })
  removeLogo?: boolean;

  @Field({ nullable: true })
  removeBackgroundImg?: boolean;

  @Field({ nullable: true })
  secondary_text_color_2?: string;
}
