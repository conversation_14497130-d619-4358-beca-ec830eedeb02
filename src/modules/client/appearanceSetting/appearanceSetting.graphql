type AppearanceSetting {
  id: ID!
  primary_color: String
  secondary_color: String
  background_color: String
  primary_text_color: String
  secondary_text_color: String
}

input AppearanceSettingInput {
  logo: String
  background: String
  primary_color: String
  secondary_color: String
  background_color: String
  primary_text_color: String
  secondary_text_color: String
}

input UpdateAppearanceSettingInput {
  id: ID!
  logo: String
  background: String
  primary_color: String
  secondary_color: String
  background_color: String
  primary_text_color: String
  secondary_text_color: String
}

type Query {
  clientGetAppearanceSetting: AppearanceSetting
  clientGetAppearanceSettingById(id: ID!): AppearanceSetting
}

type Mutation {
  clientCreateAppearanceSetting(input: AppearanceSettingInput): AppearanceSetting
  clientUpdateAppearanceSetting(input: UpdateAppearanceSettingInput): AppearanceSetting
  clientDeleteAppearanceSetting(id: ID!): Boolean!
}
