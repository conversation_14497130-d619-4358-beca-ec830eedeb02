import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SBLogger } from '../../logger/logger.service';
import { AppearanceSetting } from '../../common/entity/appearanceSetting.entity';
import { TAppearanceSettingResponse } from './appearanceSetting.type';
import {
  AppearanceSettingInput,
  UpdateAppearanceSettingInput,
} from './appearanceSetting.input';
import { FileService } from 'src/modules/common/module/file/file.service';
@Injectable()
export class AppearanceSettingService {
  constructor(
    @InjectRepository(AppearanceSetting)
    private appearanceSettingRepository: Repository<AppearanceSetting>,
    private loggerService: SBLogger,
    private fileService: FileService,
  ) {}

  async findOne(clientId: string): Promise<TAppearanceSettingResponse> {
    const result = await this.appearanceSettingRepository.findOne({
      where: { clientId },
    });
    return result;
  }

  async create(
    input: AppearanceSettingInput,
    clientId: string,
    logo: any,
    background: any,
  ): Promise<TAppearanceSettingResponse> {
    const tmp = await this.appearanceSettingRepository.create({
      ...input,
      clientId,
    });
    if (logo) {
      const stream = logo.createReadStream();
      const buffer = await new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
      tmp.logo = await this.fileService.singleUpload(
        buffer,
        logo.filename,
        'appearanceSettings',
      );
    }
    if (background) {
      const stream = background.createReadStream();
      const buffer = await new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
      tmp.background = await this.fileService.singleUpload(
        buffer,
        background.filename,
        'appearanceSettings',
      );
    }
    const result = await this.appearanceSettingRepository.save(tmp);
    return result;
  }

  async update(
    input: UpdateAppearanceSettingInput,
    clientId: string,
    logo: any,
    background: any,
  ): Promise<TAppearanceSettingResponse> {
    const { id, removeLogo, removeBackgroundImg, ...updateData } = input;
    const tmp = await this.appearanceSettingRepository.findOne({
      where: { clientId, id },
    });
    if (!tmp) {
      throw new Error('Appearance setting not found');
    }
    Object.assign(tmp, updateData);
    if (removeLogo) {
      tmp.logo = null;
    }
    if (removeBackgroundImg) {
      tmp.background = null;
    }
    if (logo) {
      const stream = logo.createReadStream();
      const buffer = await new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
      tmp.logo = await this.fileService.singleUpload(
        buffer,
        logo.filename,
        `appearanceSettings/${clientId}/${tmp.id}`,
      );
    }
    if (background) {
      const stream = background.createReadStream();
      const buffer = await new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];
        stream.on('data', (chunk) => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
      tmp.background = await this.fileService.singleUpload(
        buffer,
        background.filename,
        `appearanceSettings/${clientId}/${tmp.id}`,
      );
    }
    const result = await this.appearanceSettingRepository.save(tmp);
    return result;
  }

  async delete(id: string, clientId: string): Promise<boolean> {
    const result = await this.appearanceSettingRepository.delete({
      id,
      clientId,
    });
    return result.affected > 0;
  }
}
