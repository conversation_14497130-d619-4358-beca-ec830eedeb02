import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FileService } from 'src/modules/common/module/file/file.service';
import { Sticker } from 'src/modules/common/entity/sticker.entity';
@Injectable()
export class StickerService {
  constructor(
    @InjectRepository(Sticker)
    private readonly stickerRepository: Repository<Sticker>,
    private fileService: FileService,
  ) {}

  async findAll(clientId: string): Promise<Sticker[]> {
    return this.stickerRepository.find({ where: { clientId } });
  }

  async create(clientId: string, image: any): Promise<Sticker> {
    try {
      const sticker = this.stickerRepository.create();
      sticker.clientId = clientId;
      if (image) {
        const stream = image.createReadStream();
        const buffer = await new Promise<Buffer>((resolve, reject) => {
          const chunks: Buffer[] = [];
          stream.on('data', (chunk) => chunks.push(chunk));
          stream.on('end', () => resolve(Buffer.concat(chunks)));
          stream.on('error', reject);
        });
        sticker.image = await this.fileService.singleUpload(
          buffer,
          image.filename,
          `stickers/${clientId}/${sticker.id}`,
        );
      }
      return this.stickerRepository.save(sticker);
    } catch (error) {
      throw error;
    }
  }

  async delete(id: string, clientId: string): Promise<Sticker> {
    const sticker = await this.stickerRepository.findOne({
      where: { id, clientId },
    });
    if (!sticker) {
      throw new Error('Sticker not found');
    }
    await this.stickerRepository.remove(sticker);
    return sticker;
  }
}
