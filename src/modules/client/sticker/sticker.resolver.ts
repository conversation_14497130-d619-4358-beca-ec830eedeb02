import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { Sticker } from 'src/modules/common/entity/sticker.entity';
import { StickerService } from './sticker.service';
import { GraphQLUpload } from 'graphql-upload';
import { FileUpload } from 'graphql-upload';

@Resolver(() => Sticker)
@UseGuards(ClientAuthGuard)
export class StickerResolver {
  constructor(private readonly stickerService: StickerService) {}

  @Query(() => [Sticker])
  async clientGetStickers(@Context() context: any): Promise<Sticker[]> {
    return this.stickerService.findAll(context.req.user.id);
  }

  @Mutation(() => Sticker)
  async clientCreateSticker(
    @Args({ name: 'image', type: () => GraphQLUpload }) image: FileUpload,
    @Context() context: any,
  ): Promise<Sticker> {
    return this.stickerService.create(context.req.user.id, image);
  }

  @Mutation(() => Sticker)
  async clientDeleteSticker(@Args('id') id: string, @Context() context: any): Promise<Sticker> {
    return this.stickerService.delete(id, context.req.user.id);
  }
}
