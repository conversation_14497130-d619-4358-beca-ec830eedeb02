import { Field, InputType, ObjectType } from '@nestjs/graphql';

@InputType()
export class ClientSignInInput {
  @Field()
  email: string;

  @Field()
  password: string;
}

@ObjectType()
export class ClientCurrentSessionResponse {
  @Field()
  clientId: string;

  @Field()
  email: string;

  @Field()
  role: string;
}

// @InputType()
// export class SignInByCodeInput {
//   @Field()
//   machineCode: string;

//   @Field()
//   machinePin: string;
// }
