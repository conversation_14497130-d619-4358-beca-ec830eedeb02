import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { Auth } from '../../common/entity/auth.entity';
import { ClientCurrentSessionResponse, ClientSignInInput } from './auth.input';
import * as bcrypt from 'bcrypt';
import { jwtConstants } from 'src/constants';
import { SBLogger } from '../../logger/logger.service';
import { Client } from 'src/modules/common/entity/client.entity';
import { ClientAccount } from 'src/modules/common/entity/clientAccount.entity';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(Auth)
    private authRepository: Repository<Auth>,
    @InjectRepository(Client)
    private clientsService: Repository<Client>,
    @InjectRepository(ClientAccount)
    private clientAccountsService: Repository<ClientAccount>,
    private jwtService: JwtService,
    private loggerService: SBLogger,
  ) {}

  getToken(ctx: any): string {
    let token = '';
    const authHeader = ctx.req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }
    return token;
  }

  async getCurrentUser(token: string): Promise<Client> {
    try {
      const decoded = await this.jwtService.verifyAsync(token, jwtConstants);
      this.loggerService.log('getCurrentUser', decoded);
      return decoded;
    } catch (err) {
      this.loggerService.log('getCurrentUser - error', err);
      throw new UnauthorizedException();
    }
  }

  async comparePassword(plainTextPassword, hashedPassword) {
    const isMatch = await bcrypt.compare(plainTextPassword, hashedPassword);
    return isMatch;
  }

  async signIn(request: ClientSignInInput): Promise<Auth> {
    try {
      if (!request.email || !request.password) {
        throw new BadRequestException();
      }
      let accountType: 'CLIENT' | 'CLIENT_ACCOUNT' = 'CLIENT';
      let role: 'STAFF' | 'ADMIN' = 'ADMIN';
      let client: any = await this.clientsService.findOneBy({
        email: request.email,
      });
      if (!client) {
        client = await this.clientAccountsService.findOneBy({
          email: request.email,
        });
        accountType = 'CLIENT_ACCOUNT';
      }
      if (!client) throw new UnauthorizedException();
      const isValidPassword = await this.comparePassword(
        request.password,
        client.password,
      );
      if (!isValidPassword) throw new UnauthorizedException();
      if (client.role) role = client.role;
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...result } = client;
      const token = await this.jwtService.signAsync(
        { ...result, accountType },
        jwtConstants,
      );
      return { ...result, token, accountType, role };
    } catch (error) {
      this.loggerService.log('ERROR - signIn - error', error);
      throw new UnauthorizedException(error.message);
    }
  }

  async signOut(token: string): Promise<Auth> {
    const auth = await this.authRepository.findOneBy({ token });
    if (!auth) {
      this.loggerService.log('ERROR - signOut', auth);
      throw new NotFoundException();
    }
    const res = await this.authRepository.remove(auth);
    return res;
  }

  async getCurrentSession(context: any): Promise<ClientCurrentSessionResponse> {
    const { user } = context.req;
    return {
      clientId: user.id,
      email: user.loginAccount.email,
      role: user.loginRole,
    };
  }
}
