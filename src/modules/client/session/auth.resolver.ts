import { Resolver, Query, Args, Context, Mutation } from '@nestjs/graphql';
import { AuthService } from './auth.service';
import { Auth } from '../../common/entity/auth.entity';
import { ClientSignInInput, ClientCurrentSessionResponse } from './auth.input';
import { ExecutionContext, UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';

@Resolver(() => Auth)
export class AuthResolver {
  constructor(private authService: AuthService) {}
  @Mutation(() => Auth)
  async clientSignIn(@Args('input') input: ClientSignInInput): Promise<Auth> {
    return this.authService.signIn(input);
  }

  @Query(() => Auth)
  async clientSignOut(@Context() ctx: ExecutionContext): Promise<Auth> {
    const token = await this.authService.getToken(ctx);
    return this.authService.signOut(token);
  }

  @Query(() => ClientCurrentSessionResponse)
  @UseGuards(ClientAuthGuard)
  async clientCurrentSession(
    @Context() context: any,
  ): Promise<ClientCurrentSessionResponse> {
    return this.authService.getCurrentSession(context);
  }
}
