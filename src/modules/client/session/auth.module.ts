import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthService } from './auth.service';
import { AuthResolver } from './auth.resolver';
import { Auth } from '../../common/entity/auth.entity';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Client } from 'src/modules/common/entity/client.entity';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { ClientAccount } from 'src/modules/common/entity/clientAccount.entity';
@Module({
  imports: [TypeOrmModule.forFeature([Auth, Client, ClientAccount, Machine])],
  providers: [AuthService, JwtService, AuthResolver, SBLogger],
  exports: [AuthService],
})
export class AuthModule {}
