type Promotion {
  id: String!
  name: String  
  machineIds: [String]
  startDate: String
  endDate: String
  usageLimitPerCode: Int
  maxPromotionCodes: Int
  promotionCode: String
  createdAt: String
  updatedAt: String
  codes: [PromotionCode]
}

type PromotionCode {
  id: String!
  code: String
  isUsed: Boolean
  numberUsed: Int
  createdAt: String
  updatedAt: String
}

type ExportPromotionResponse {
  url: String!
}

type PromotionStatistics {
  totalOrders: Float!
  totalRevenue: Float!
}

type PromotionResponse {
  id: String!
  name: String
  machineIds: String
  startDate: String
  endDate: String
  usageLimitPerCode: Float
  maxPromotionCodes: Float
  discountValue: Float
  promotionCode: String
  createdAt: String
  updatedAt: String
  codes: [PromotionCode!]!
  statistics: PromotionStatistics
}

input PromotionOrdersInput {
  promotionId: String!
  page: Int = 1
  limit: Int = 10
}

type PromotionOrderResponse {
  id: String!
  orderCode: String
  amount: Float
  totalOrderNumber: Float
  receivedAmount: Float
  description: String
  refCode: String
  imageNumber: Int
  status: String
  paymentMethod: String
  captureMode: String
  createdAt: DateTime
  denominations: String
  domain: String
  promotionCodeUsed: String
  machineName: String
  machineCode: String
  machineLocation: String
  machineId: String
  frameName: String
  frameDescription: String
  frameImageUrl: String
  frameSize: String
  topicName: String
  topicExtraFee: Float
  settingSizeName: String
  promotionName: String
  promotionDiscount: Float
  imageCount: Int
}

type PromotionOrdersResponse {
  orders: [PromotionOrderResponse!]!
  total: Int!
  page: Int!
  limit: Int!
  totalPages: Int!
}

type Query {
  clientGetPromotions(status: String!): [Promotion!]!
  clientGetPromotion(id: String!): PromotionResponse!
  clientGetPromotionOrders(input: PromotionOrdersInput!): PromotionOrdersResponse!
  clientExportPromotion(id: String!): ExportPromotionResponse!
}

type Mutation {
  clientCreatePromotion(createPromotionInput: CreatePromotionInput!): Promotion
  clientDeletePromotion(id: String!): Promotion
  clientFinishPromotion(id: String!): Promotion
}
