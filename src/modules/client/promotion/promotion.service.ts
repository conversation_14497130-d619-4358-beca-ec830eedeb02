import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  ExportPromotionResponse,
  Promotion,
  PromotionResponse,
  PromotionStatistics,
} from 'src/modules/common/entity/promotion.entity';
import { PromotionCode } from 'src/modules/common/entity/promotionCode.entity';
import { Order } from 'src/modules/clientApp/order/order.entity';
import { CreatePromotionInput } from './promotion.input';
import {
  TPromotionResponse,
  PromotionOrdersInput,
  PromotionOrdersResponse,
  PromotionOrderResponse,
} from './promotion.type';
import * as moment from 'moment';
import { XlsxService } from 'src/modules/common/module/xlsx/xlsx.service';
import { FileService } from 'src/modules/common/module/file/file.service';
import { EImageFileType } from 'src/enum';
@Injectable()
export class PromotionService {
  constructor(
    @InjectRepository(Promotion)
    private readonly promotionRepository: Repository<Promotion>,
    @InjectRepository(PromotionCode)
    private readonly promotionCodeRepository: Repository<PromotionCode>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    private readonly xlsxService: XlsxService,
    private readonly fileService: FileService,
  ) {}

  async getPromotions(
    status: string,
    context: any,
  ): Promise<TPromotionResponse[]> {
    const { user } = context.req;
    const now = Date.now().toString();

    const query = this.promotionRepository.createQueryBuilder('promotion');
    query.where('promotion.clientId = :clientId', { clientId: user.id });
    if (status === 'upcoming') {
      query.where('promotion.startDate > :now', { now });
    } else if (status === 'ongoing') {
      query
        .where('promotion.startDate <= :now', { now })
        .andWhere('promotion.endDate >= :now', { now });
    } else if (status === 'ended') {
      query.where('promotion.endDate < :now', { now });
    }
    return query.orderBy('promotion.startDate', 'ASC').getMany();
  }

  async getPromotionDetail(
    id: string,
    context: any,
  ): Promise<PromotionResponse> {
    const { user } = context;
    const promotion = await this.promotionRepository.findOne({
      where: { id, clientId: user.id },
      relations: ['codes'],
    });
    if (!promotion) {
      throw new Error('Promotion not found');
    }

    // Calculate statistics
    const statistics = await this.calculatePromotionStatistics(id);

    return {
      id: promotion.id,
      name: promotion.name,
      machineIds: promotion.machineIds,
      startDate: promotion.startDate,
      endDate: promotion.endDate,
      usageLimitPerCode: promotion.usageLimitPerCode,
      maxPromotionCodes: promotion.maxPromotionCodes,
      discountValue: promotion.discountValue,
      promotionCode: promotion.promotionCode,
      createdAt: promotion.createdAt,
      updatedAt: promotion.updatedAt,
      codes: promotion.codes,
      statistics,
    };
  }

  private async calculatePromotionStatistics(
    promotionId: string,
  ): Promise<PromotionStatistics> {
    // Get all orders that used this promotion
    const ordersQuery = this.orderRepository
      .createQueryBuilder('order')
      .leftJoin('order.promotionCode', 'promotionCode')
      .leftJoin('promotionCode.promotion', 'promotion')
      .where('promotion.id = :promotionId', { promotionId });

    const orders = await ordersQuery.getMany();

    const totalOrders = orders.length;
    const totalRevenue = orders.reduce(
      (sum, order) => sum + Number(order.totalOrderNumber || 0),
      0,
    );

    return {
      totalOrders,
      totalRevenue,
    };
  }

  async getPromotionOrders(
    input: PromotionOrdersInput,
    context: any,
  ): Promise<PromotionOrdersResponse> {
    const { user } = context;
    const { promotionId, page = 1, limit = 10 } = input;

    // Verify promotion belongs to this client
    const promotion = await this.promotionRepository.findOne({
      where: { id: promotionId, clientId: user.id },
    });
    if (!promotion) {
      throw new Error('Promotion not found');
    }

    // Build query for orders using this promotion with all relations
    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.promotionCode', 'promotionCode')
      .leftJoinAndSelect('promotionCode.promotion', 'promotion')
      .leftJoinAndSelect('order.machine', 'machine')
      .leftJoinAndSelect('order.frame', 'frame')
      .leftJoinAndSelect('order.topic', 'topic')
      .leftJoinAndSelect('order.settingSize', 'settingSize')
      .leftJoinAndSelect('order.images', 'images')
      .where('promotion.id = :promotionId', { promotionId })
      .orderBy('order.createdAt', 'DESC');

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Get results and total count
    const [orders, total] = await queryBuilder.getManyAndCount();

    // Transform to response format with full details
    const orderResponses: PromotionOrderResponse[] = orders.map((order) => ({
      id: order.id,
      orderCode: order.orderCode,
      amount: order.amount,
      totalOrderNumber: order.totalOrderNumber,
      receivedAmount: order.receivedAmount,
      description: order.description,
      refCode: order.refCode,
      imageNumber: order.imageNumber,
      status: order.status,
      paymentMethod: order.paymentMethod,
      captureMode: order.captureMode,
      createdAt: order.createdAt,
      denominations:
        typeof order.denominations === 'string'
          ? order.denominations
          : JSON.stringify(order.denominations),
      domain: `${process.env.MEMORY_DOMAIN || ''}${order.id}`,

      // Promotion info
      promotionCodeUsed: order.promotionCode?.code,
      promotionName: order.promotionCode?.promotion?.name,
      promotionDiscount: order.promotionCode?.promotion?.discountValue,

      // Machine info
      machineCode: order.machine?.machineCode,
      machineLocation: order.machine?.location,
      machineId: order.machineId,

      // Frame info
      frameDescription: order.frame?.description,
      frameImageUrl: order.frame?.imageUrl,
      frameSize: order.frame?.frameSize,

      // Topic info
      topicName: order.topic?.name,
      topicExtraFee: order.topic?.extraFee,

      // Setting size info
      settingSizeName: order.settingSize?.name,

      // Image count
      imageCount: order.images?.length || 0,

      finalImageUrl:
        order.images?.find((img) => img.fileType === EImageFileType.IMAGE_FINAL)
          ?.fileUrl || null,
    }));

    const totalPages = Math.ceil(total / limit);

    return {
      orders: orderResponses,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async exportPromotion(
    id: string,
    context: any,
  ): Promise<ExportPromotionResponse> {
    const { user } = context;
    const promotion = await this.promotionRepository.findOne({
      where: { id, clientId: user.id },
      relations: ['codes'],
    });
    if (!promotion) {
      throw new Error('Promotion not found');
    }
    const xlsx = await this.xlsxService.exportPromotion(promotion);
    const url = await this.fileService.singleUpload(
      xlsx,
      `${promotion.name} - ${Date.now()}.xlsx`,
      `promotions/${promotion.id}`,
    );
    return {
      url,
    };
  }

  async createPromotion(
    input: CreatePromotionInput,
    context: any,
  ): Promise<TPromotionResponse> {
    try {
      const { user } = context;
      const timestamp = moment.utc().valueOf().toString();
      const promotion = await this.promotionRepository.save({
        ...input,
        clientId: user.id,
        promotionCode: `SNAPBOX${timestamp}`,
      });
      await this.generatePromotionCodes(
        promotion.id,
        input.maxPromotionCodes,
        user.id,
      );

      return promotion;
    } catch (error) {
      throw error;
    }
  }

  async deletePromotion(id: string, context: any): Promise<PromotionResponse> {
    try {
      const { user } = context.req;

      const now = Date.now().toString();

      const promotion = await this.promotionRepository.findOneBy({
        id,
        clientId: user.id,
      });
      if (!promotion) {
        throw new NotFoundException(`Promotion with ID ${id} not found`);
      }

      if (Number(promotion.startDate) <= Number(now)) {
        throw new BadRequestException(
          'Only promotions that have not started can be deleted',
        );
      }

      await this.promotionRepository.delete(id);
      return promotion;
    } catch (error) {
      throw error;
    }
  }

  private async generatePromotionCodes(
    promotionId: string,
    quantity: number,
    clientId: string,
  ): Promise<void> {
    const codes = [];

    for (let i = 0; i < quantity; i++) {
      let uniqueCode: string;

      do {
        uniqueCode = this.generateNumericCode(8);
      } while (await this.isCodeExists(uniqueCode, clientId));

      codes.push(
        this.promotionCodeRepository.create({
          code: uniqueCode,
          promotionId,
        }),
      );
    }

    await this.promotionCodeRepository.save(codes);
  }
  private async isCodeExists(code: string, clientId: string): Promise<boolean> {
    const count = await this.promotionCodeRepository.count({
      where: { code, promotion: { clientId }, isUsed: false },
    });
    return count > 0;
  }

  private generateNumericCode(length: number): string {
    const min = Math.pow(10, length - 1);
    const max = Math.pow(10, length) - 1;
    return Math.floor(Math.random() * (max - min + 1) + min).toString();
  }

  async finishPromotion(
    id: string,
    clientId: string,
  ): Promise<TPromotionResponse> {
    try {
      const promotion = await this.promotionRepository.findOne({
        where: { id, clientId: clientId },
        relations: ['codes'],
      });
      if (!promotion) {
        throw new Error('Promotion not found');
      }

      promotion.endDate = Date.now().toString();

      return this.promotionRepository.save(promotion);
    } catch (error) {
      throw error;
    }
  }
}
