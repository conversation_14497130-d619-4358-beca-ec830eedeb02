import { Field, ObjectType, InputType, Int } from '@nestjs/graphql';
import { Promotion } from "src/modules/common/entity/promotion.entity";
import { PromotionCode } from "src/modules/common/entity/promotionCode.entity";
import { Order } from "src/modules/clientApp/order/order.entity";

export type TPromotionResponse = Promotion & { codes: TPromotionCodeResponse[] };
export type TPromotionCodeResponse = PromotionCode;



@InputType()
export class PromotionOrdersInput {
  @Field()
  promotionId: string;

  @Field(() => Int, { nullable: true, defaultValue: 1 })
  page?: number;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  limit?: number;
}

@ObjectType()
export class PromotionOrderResponse {
  @Field()
  id: string;

  @Field({ nullable: true })
  orderCode?: string;

  @Field({ nullable: true })
  amount?: number;

  @Field({ nullable: true })
  totalOrderNumber?: number;

  @Field({ nullable: true })
  receivedAmount?: number;

  @Field({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  refCode?: string;

  @Field({ nullable: true })
  imageNumber?: number;

  @Field({ nullable: true })
  status?: string;

  @Field({ nullable: true })
  paymentMethod?: string;

  @Field({ nullable: true })
  captureMode?: string;

  @Field({ nullable: true })
  createdAt?: Date;

  @Field({ nullable: true })
  denominations?: string;

  @Field({ nullable: true })
  domain?: string; // Memory domain link

  // Relations
  @Field({ nullable: true })
  promotionCodeUsed?: string;

  @Field({ nullable: true })
  machineName?: string;

  @Field({ nullable: true })
  machineCode?: string;

  @Field({ nullable: true })
  machineLocation?: string;

  @Field({ nullable: true })
  machineId?: string;

  @Field({ nullable: true })
  frameName?: string;

  @Field({ nullable: true })
  frameDescription?: string;

  @Field({ nullable: true })
  frameImageUrl?: string;

  @Field({ nullable: true })
  frameSize?: string;

  @Field({ nullable: true })
  topicName?: string;

  @Field({ nullable: true })
  topicExtraFee?: number;

  @Field({ nullable: true })
  settingSizeName?: string;

  @Field({ nullable: true })
  promotionName?: string;

  @Field({ nullable: true })
  promotionDiscount?: number;

  @Field({ nullable: true })
  imageCount?: number;

  @Field({ nullable: true })
  finalImageUrl?: string;
}

@ObjectType()
export class PromotionOrdersResponse {
  @Field(() => [PromotionOrderResponse])
  orders: PromotionOrderResponse[];

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  totalPages: number;
}
