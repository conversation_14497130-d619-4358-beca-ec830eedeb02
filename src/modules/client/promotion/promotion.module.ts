import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Promotion } from '../../common/entity/promotion.entity';
import { PromotionResolver } from './promotion.resolver';
import { PromotionService } from './promotion.service';
import { PromotionCode } from 'src/modules/common/entity/promotionCode.entity';
import { Order } from 'src/modules/clientApp/order/order.entity';
import { XlsxService } from 'src/modules/common/module/xlsx/xlsx.service';
import { FileService } from 'src/modules/common/module/file/file.service';
@Module({
  imports: [TypeOrmModule.forFeature([Promotion, PromotionCode, Order])],
  providers: [
    PromotionService,
    JwtService,
    SBLogger,
    PromotionResolver,
    XlsxService,
    FileService,
  ],
  exports: [PromotionService],
})
export class PromotionModule {}
