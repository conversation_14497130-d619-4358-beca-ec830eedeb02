import { Query, Resolver, Args, Context, Mutation } from '@nestjs/graphql';
import { PromotionService } from './promotion.service';
import {
  Promotion,
  PromotionResponse,
  ExportPromotionResponse,
} from 'src/modules/common/entity/promotion.entity';
import { CreatePromotionInput } from './promotion.input';
import {
  PromotionOrdersInput,
  PromotionOrdersResponse,
} from './promotion.type';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { UseGuards } from '@nestjs/common';

@Resolver()
@UseGuards(ClientAuthGuard)
export class PromotionResolver {
  constructor(private readonly promotionService: PromotionService) {}

  @Query(() => [Promotion])
  clientGetPromotions(@Args('status') status: string, @Context() context: any) {
    return this.promotionService.getPromotions(status, context);
  }

  @Query(() => PromotionResponse)
  clientGetPromotion(
    @Args('id') id: string,
    @Context() context: any,
  ): Promise<PromotionResponse> {
    return this.promotionService.getPromotionDetail(id, context.req);
  }

  @Query(() => ExportPromotionResponse)
  clientExportPromotion(@Args('id') id: string, @Context() context: any) {
    return this.promotionService.exportPromotion(id, context.req);
  }

  @Query(() => PromotionOrdersResponse)
  clientGetPromotionOrders(
    @Args('input') input: PromotionOrdersInput,
    @Context() context: any,
  ): Promise<PromotionOrdersResponse> {
    return this.promotionService.getPromotionOrders(input, context.req);
  }

  @Mutation(() => Promotion)
  clientCreatePromotion(
    @Args('createPromotionInput') createPromotionInput: CreatePromotionInput,
    @Context() context: any,
  ) {
    return this.promotionService.createPromotion(
      createPromotionInput,
      context.req,
    );
  }

  @Mutation(() => Promotion)
  clientDeletePromotion(
    @Args('id') id: string,
    @Context() context: any,
  ): Promise<PromotionResponse> {
    return this.promotionService.deletePromotion(id, context);
  }

  @Mutation(() => Promotion)
  clientFinishPromotion(@Args('id') id: string, @Context() context: any) {
    return this.promotionService.finishPromotion(id, context.req.user.id);
  }
}
