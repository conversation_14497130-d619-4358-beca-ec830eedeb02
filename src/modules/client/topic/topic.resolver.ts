import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { TopicService } from './topic.service';
import { Topic } from '../../common/entity/topic.entity';
import { CreateTopicInput, UpdateTopicInput } from './topic.input';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { TTopicResponse } from './topic.type';
@Resolver(() => Topic)
@UseGuards(ClientAuthGuard)
export class TopicResolver {
  constructor(private readonly topicService: TopicService) {}

  @Query(() => [Topic])
  async clientGetTopics(@Context() context: any): Promise<Topic[]> {
    return this.topicService.findAll(context.req.user.id);
  }

  @Query(() => Topic)
  async clientGetTopic(
    @Args('id') id: string,
    @Context() context: any,
  ): Promise<Topic> {
    return this.topicService.findOne(id, context.req.user.id);
  }

  @Mutation(() => Topic)
  async clientCreateTopic(
    @Args('input') input: CreateTopicInput,
    @Context() context: any,
  ): Promise<TTopicResponse> {
    return this.topicService.create(input, context.req.user.id);
  }

  @Mutation(() => Topic)
  async clientUpdateTopic(
    @Args('input') input: UpdateTopicInput,
  ): Promise<TTopicResponse> {
    return this.topicService.update(input);
  }

  @Mutation(() => Topic)
  async clientDeleteTopic(@Args('id') id: string): Promise<Topic> {
    return this.topicService.delete(id);
  }
}
