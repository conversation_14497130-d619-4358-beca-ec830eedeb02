import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateTopicInput, UpdateTopicInput } from './topic.input';
import { Topic } from '../../common/entity/topic.entity';
import { TTopicResponse } from './topic.type';

@Injectable()
export class TopicService {
  constructor(
    @InjectRepository(Topic)
    private readonly topicRepository: Repository<Topic>,
  ) {}

  async findAll(clientId: string): Promise<Topic[]> {
    return this.topicRepository.find({
      where: { clientId },
      relations: ['frames'],
    });
  }

  async findOne(id: string, clientId: string): Promise<Topic> {
    return this.topicRepository.findOne({ where: { id, clientId } });
  }

  async create(
    createTopicInput: CreateTopicInput,
    clientId: string,
  ): Promise<TTopicResponse> {
    const topic = this.topicRepository.create(createTopicInput);
    topic.clientId = clientId;
    return this.topicRepository.save(topic);
  }

  async update(updateTopicInput: UpdateTopicInput): Promise<TTopicResponse> {
    const { id, ...updates } = updateTopicInput;
    const topic = await this.topicRepository.findOne({ where: { id } });
    if (!topic) {
      throw new Error('Topic not found');
    }

    Object.assign(topic, updates);
    return this.topicRepository.save(topic);
  }

  async delete(id: string): Promise<Topic> {
    const topic = await this.topicRepository.findOne({ where: { id } });
    if (!topic) {
      throw new Error('Topic not found');
    }
    await this.topicRepository.remove(topic);
    return topic;
  }
}
