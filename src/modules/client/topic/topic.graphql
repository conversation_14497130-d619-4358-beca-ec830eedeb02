type Topic {
  id: ID!
  name: String
  extraFee: Float
  isActive: Boolean
  createdAt: String
  updatedAt: String
}

input CreateTopicInput {
  name: String!
  extraFee: Float
}

input UpdateTopicInput {
  id: ID!
  name: String
  extraFee: Float
  isActive: Bo<PERSON>an
}

type Query {
  clientGetTopics: [Topic!]!
  clientGetTopic(id: ID!): Topic!
}

type Mutation {
  clientCreateTopic(createTopicInput: CreateTopicInput!): Topic
  clientUpdateTopic(updateTopicInput: UpdateTopicInput!): Topic
  clientDeleteTopic(id: ID!): Topic
}
