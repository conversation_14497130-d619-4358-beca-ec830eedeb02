import { InputType, Field, Float } from '@nestjs/graphql';

@InputType()
export class CreateTopicInput {
  @Field()
  name: string;

  @Field(() => Float, { defaultValue: 0 })
  extraFee: number;
}

@InputType()
export class UpdateTopicInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field(() => Float, { nullable: true })
  extraFee?: number; // Phụ phí

  @Field({ nullable: true })
  isActive?: boolean;
}
