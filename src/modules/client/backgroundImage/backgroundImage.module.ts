import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { BackgroundImage } from '../../common/entity/backgroundImage.entity';
import { BackgroundImageResolver } from './backgroundImage.resolver';
import { BackgroundImageService } from './backgroundImage.service';
import { CommonBackgroundImageService } from '../../common/service/backgroundImage.service';
import { FileService } from '../../common/module/file/file.service';
import { SBLogger } from '../../logger/logger.service';

@Module({
  imports: [TypeOrmModule.forFeature([BackgroundImage])],
  providers: [
    BackgroundImageResolver,
    BackgroundImageService,
    CommonBackgroundImageService,
    FileService,
    JwtService,
    SBLogger,
  ],
  exports: [BackgroundImageService],
})
export class BackgroundImageModule {}
