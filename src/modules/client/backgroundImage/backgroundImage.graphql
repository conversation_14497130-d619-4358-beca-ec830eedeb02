scalar Upload

input UploadBackgroundImageInput {
  description: String
}

type UploadBackgroundImageResponse {
  id: String!
  fileName: String!
  fileUrl: String!
  description: String
  message: String!
}

type BackgroundImageResponse {
  id: String!
  fileName: String!
  fileUrl: String!
  description: String
  createdAt: DateTime!
  updatedAt: DateTime!
}

type DeleteBackgroundImageResponse {
  success: Boolean!
  message: String!
}

type BackgroundImageListResponse {
  images: [BackgroundImageResponse!]!
  total: Int!
  message: String!
}

type Mutation {
  clientUploadBackgroundImage(
    input: UploadBackgroundImageInput!
    image: Upload!
  ): UploadBackgroundImageResponse!

  clientDeleteBackgroundImage(id: String!): DeleteBackgroundImageResponse!
}

type Query {
  clientGetBackgroundImages: BackgroundImageListResponse!
  clientGetBackgroundImage(id: String!): BackgroundImageResponse
}
