import { Resolver, Mutation, Query, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { ClientAuthGuard } from '../../../guards/clientAuth.guard';
import { BackgroundImageService } from './backgroundImage.service';
import {
  UploadBackgroundImageInput,
  UploadBackgroundImageResponse,
  BackgroundImageResponse,
  DeleteBackgroundImageResponse,
  BackgroundImageListResponse,
} from './backgroundImage.input';

@Resolver()
@UseGuards(ClientAuthGuard)
export class BackgroundImageResolver {
  constructor(
    private readonly backgroundImageService: BackgroundImageService,
  ) {}

  @Mutation(() => UploadBackgroundImageResponse)
  async clientUploadBackgroundImage(
    @Args('input') input: UploadBackgroundImageInput,
    @Args({ name: 'image', type: () => GraphQLUpload }) file: FileUpload,
    @Context() context: any,
  ): Promise<UploadBackgroundImageResponse> {
    const clientId = context.req.user.id;
    return this.backgroundImageService.uploadBackgroundImage(
      input,
      file,
      clientId,
    );
  }

  @Query(() => BackgroundImageListResponse)
  async clientGetBackgroundImages(
    @Context() context: any,
  ): Promise<BackgroundImageListResponse> {
    const clientId = context.req.user.id;
    return this.backgroundImageService.getBackgroundImages(clientId);
  }

  @Query(() => BackgroundImageResponse, { nullable: true })
  async clientGetBackgroundImage(
    @Args('id') id: string,
    @Context() context: any,
  ): Promise<BackgroundImageResponse> {
    const clientId = context.req.user.id;
    return this.backgroundImageService.getBackgroundImage(id, clientId);
  }

  @Mutation(() => DeleteBackgroundImageResponse)
  async clientDeleteBackgroundImage(
    @Args('id') id: string,
    @Context() context: any,
  ): Promise<DeleteBackgroundImageResponse> {
    const clientId = context.req.user.id;
    return this.backgroundImageService.deleteBackgroundImage(id, clientId);
  }
}
