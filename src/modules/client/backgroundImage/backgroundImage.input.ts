import { Field, InputType, ObjectType } from '@nestjs/graphql';

@InputType()
export class UploadBackgroundImageInput {
  @Field({ nullable: true })
  description?: string;
}

@ObjectType()
export class UploadBackgroundImageResponse {
  @Field()
  id: string;

  @Field()
  fileName: string;

  @Field()
  fileUrl: string;

  @Field({ nullable: true })
  description?: string;

  @Field()
  message: string;
}

@ObjectType()
export class BackgroundImageResponse {
  @Field()
  id: string;

  @Field()
  fileName: string;

  @Field()
  fileUrl: string;

  @Field({ nullable: true })
  description?: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

@ObjectType()
export class DeleteBackgroundImageResponse {
  @Field()
  success: boolean;

  @Field()
  message: string;
}

@ObjectType()
export class BackgroundImageListResponse {
  @Field(() => [BackgroundImageResponse])
  images: BackgroundImageResponse[];

  @Field()
  total: number;

  @Field()
  message: string;
}
