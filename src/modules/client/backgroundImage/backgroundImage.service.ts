import {
  Injectable,
  BadRequestException,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BackgroundImage } from '../../common/entity/backgroundImage.entity';
import { FileUpload } from 'graphql-upload';
import { FileService } from '../../common/module/file/file.service';
import { CommonBackgroundImageService } from '../../common/service/backgroundImage.service';
import {
  UploadBackgroundImageInput,
  UploadBackgroundImageResponse,
  BackgroundImageResponse,
  DeleteBackgroundImageResponse,
  BackgroundImageListResponse,
} from './backgroundImage.input';

@Injectable()
export class BackgroundImageService {
  private readonly logger = new Logger(BackgroundImageService.name);

  constructor(
    @InjectRepository(BackgroundImage)
    private backgroundImageRepository: Repository<BackgroundImage>,
    private fileService: FileService,
    private commonBackgroundImageService: CommonBackgroundImageService,
  ) {}

  async uploadBackgroundImage(
    input: UploadBackgroundImageInput,
    file: FileUpload,
    clientId: string,
  ): Promise<UploadBackgroundImageResponse> {
    try {
      this.logger.log(`Uploading background image for client: ${clientId}`);

      // Validate file
      this.validateFile(file);

      const { createReadStream, filename, mimetype } = file;
      this.validateFileType(mimetype);

      // Convert stream to buffer
      const buffer = await this.streamToBuffer(createReadStream());

      // Generate unique filename
      const uniqueFileName = this.generateUniqueFileName(filename);

      // Upload file
      const fileUrl = await this.fileService.singleUpload(
        buffer,
        uniqueFileName,
        `backgroundImages/${clientId}`,
      );

      // Save to database
      const backgroundImage = this.backgroundImageRepository.create({
        fileName: uniqueFileName,
        fileUrl,
        description: input.description,
        clientId,
      });

      const savedImage =
        await this.backgroundImageRepository.save(backgroundImage);

      this.logger.log(
        `Background image uploaded successfully: ${savedImage.id}`,
      );

      return {
        id: savedImage.id,
        fileName: savedImage.fileName,
        fileUrl: savedImage.fileUrl,
        description: savedImage.description,
        message: 'Background image uploaded successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to upload background image: ${error.message}`);
      throw error;
    }
  }

  async getBackgroundImages(
    clientId: string,
  ): Promise<BackgroundImageListResponse> {
    try {
      this.logger.log(`Getting background images for client: ${clientId}`);

      const images = await this.backgroundImageRepository.find({
        where: { clientId },
        order: { createdAt: 'DESC' },
      });

      return {
        images: images.map((image) => ({
          id: image.id,
          fileName: image.fileName,
          fileUrl: image.fileUrl,
          description: image.description,
          createdAt: image.createdAt,
          updatedAt: image.updatedAt,
        })),
        total: images.length,
        message:
          images.length > 0
            ? 'Background images retrieved successfully'
            : 'No background images found',
      };
    } catch (error) {
      this.logger.error(`Failed to get background images: ${error.message}`);
      throw error;
    }
  }

  async getBackgroundImage(
    id: string,
    clientId: string,
  ): Promise<BackgroundImageResponse> {
    try {
      this.logger.log(
        `Getting background image: ${id} for client: ${clientId}`,
      );

      const image = await this.backgroundImageRepository.findOne({
        where: { id, clientId },
      });

      if (!image) {
        throw new NotFoundException('Background image not found');
      }

      return {
        id: image.id,
        fileName: image.fileName,
        fileUrl: image.fileUrl,
        description: image.description,
        createdAt: image.createdAt,
        updatedAt: image.updatedAt,
      };
    } catch (error) {
      this.logger.error(`Failed to get background image: ${error.message}`);
      throw error;
    }
  }

  async deleteBackgroundImage(
    id: string,
    clientId: string,
  ): Promise<DeleteBackgroundImageResponse> {
    try {
      this.logger.log(
        `Deleting background image: ${id} for client: ${clientId}`,
      );

      const image = await this.backgroundImageRepository.findOne({
        where: { id, clientId },
      });

      if (!image) {
        throw new NotFoundException('Background image not found');
      }

      // Delete from database
      await this.backgroundImageRepository.delete({ id, clientId });

      this.logger.log(`Background image deleted successfully: ${id}`);

      return {
        success: true,
        message: 'Background image deleted successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to delete background image: ${error.message}`);
      throw error;
    }
  }

  private validateFile(file: FileUpload): void {
    if (!file) {
      throw new BadRequestException('File is required');
    }
  }

  private validateFileType(mimetype: string): void {
    const allowedTypes = [
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
    ];
    if (!allowedTypes.includes(mimetype)) {
      throw new BadRequestException(
        'Unsupported file type. Only JPG, PNG, GIF, WEBP are allowed',
      );
    }
  }

  private streamToBuffer(stream: NodeJS.ReadableStream): Promise<Buffer> {
    return new Promise<Buffer>((resolve, reject) => {
      const chunks: Buffer[] = [];
      stream.on('data', (chunk) => chunks.push(chunk));
      stream.on('end', () => resolve(Buffer.concat(chunks)));
      stream.on('error', reject);
    });
  }

  private generateUniqueFileName(originalFileName: string): string {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = originalFileName.split('.').pop();
    return `bg_${timestamp}_${randomString}.${extension}`;
  }
}
