type Frame {
  id: ID!
  description: String
  imageUrl: String!
  frameSize: String!
  numberImage: Int!
  numberPicture: Int!
  topicId: ID!
  orientation: String!
  dateColor: String
  clientId: ID!
  createdAt: String!
  updatedAt: String!
  frameItems: [FrameItem!]!
}

input FrameInput {
  description: String
  frameSize: String!
  numberImage: Int!
  numberPicture: Int!
  topicId: ID!
  orientation: String!
  dateColor: String
  frameItems: [FrameItemInput!]!
}

input UpdateFrameInput {
  id: ID!
  description: String
  frameSize: String
  numberImage: Int
  numberPicture: Int
  orientation: String
  dateColor: String
  frameItems: [FrameItemInput!]
}

input FrameItemInput {
  itemType: String!
  xCoordinate: Int!
  yCoordinate: Int!
  width: Int!
  height: Int!
  parentId: ID
  position: Int!
}

type FrameItem {
  id: ID!
  itemType: String!
  xCoordinate: Int!
  yCoordinate: Int!
  width: Int!
  height: Int!
  parentId: ID
  position: Int!
}

type Query {
  clientGetFrames(
    topicId: ID
    frameSize: String
  ): [Frame!]!
  
  clientGetFrame(
    id: ID!
  ): Frame!

  clientFrameUsedStatistic: FrameStatisticResponse!
}

type Mutation {
  clientCreateFrame(
    input: FrameInput!
  ): Frame!
  
  clientUpdateFrame(
    input: UpdateFrameInput!
  ): Frame!
  
  clientDeleteFrame(
    id: ID!
  ): Frame!
}
