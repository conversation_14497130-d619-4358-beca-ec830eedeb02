import {
  InputType,
  Field,
  PartialType,
  OmitType,
  ObjectType,
} from '@nestjs/graphql';
import { IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { EFrameItemType, EFrameOrientation, EFrameSize } from 'src/enum';
import { Frame } from 'src/modules/common/entity/frame.entity';

@InputType()
export class BaseItemInput {
  @Field()
  itemType: EFrameItemType;

  @Field()
  x_coordinate: number;

  @Field()
  y_coordinate: number;

  @Field()
  width: number;

  @Field()
  height: number;

  @Field({ nullable: true })
  parentId?: string;

  @Field({ nullable: true })
  itemId?: string;

  @Field({ defaultValue: 0 })
  position: number;

  @Field({ nullable: true })
  angle?: number;
}

@InputType()
export class FrameInput {
  @Field({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  imageUrl?: string;

  @Field()
  frameSize: EFrameSize;

  @Field()
  numberImage: number;

  @Field()
  numberPicture: number;

  @Field()
  topicId: string;

  @Field()
  orientation: EFrameOrientation;

  @Field({ nullable: true })
  dateColor?: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BaseItemInput)
  @Field(() => [BaseItemInput])
  frameItems: BaseItemInput[];
}

@InputType()
export class UpdateItemInput extends PartialType(BaseItemInput) {
  @Field()
  id: string;

  @Field({ nullable: true })
  isDeleted?: boolean;
}

@InputType()
export class UpdateFrameInput extends PartialType(
  OmitType(FrameInput, ['frameItems']),
) {
  @Field()
  id: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateItemInput)
  @Field(() => [UpdateItemInput])
  frameItems: UpdateItemInput[];
}

@ObjectType()
class FrameUsageStats {
  @Field(() => Frame)
  frame: Frame;

  @Field()
  usedCount: number;
}

@ObjectType()
export class FrameStatisticResponse {
  @Field(() => [FrameUsageStats])
  mostUsed: FrameUsageStats[];

  @Field(() => [FrameUsageStats])
  leastUsed: FrameUsageStats[];
}
