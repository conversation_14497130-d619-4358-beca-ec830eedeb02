import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { Frame } from '../../common/entity/frame.entity';
import {
  FrameInput,
  FrameStatisticResponse,
  UpdateFrameInput,
} from './frame.input';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { FrameService } from './frame.service';
import { FileUpload, GraphQLUpload } from 'graphql-upload';

@Resolver(() => Frame)
@UseGuards(ClientAuthGuard)
export class FrameResolver {
  constructor(private readonly frameService: FrameService) {}

  @Query(() => [Frame])
  async clientGetFrames(
    @Context() context: any,
    @Args('topicID', { nullable: true }) topicId?: string,
    @Args('frameSize', { nullable: true }) frameSize?: string,
  ): Promise<Frame[]> {
    return this.frameService.findAll(context.req.user.id, topicId, frameSize);
  }

  @Query(() => Frame)
  async clientGetFrame(
    @Args('id') id: string,
    @Context() context: any,
  ): Promise<Frame> {
    return this.frameService.findOne(id, context.req.user.id);
  }

  @Query(() => FrameStatisticResponse)
  async clientFrameUsedStatistic(
    @Context() context: any,
  ): Promise<FrameStatisticResponse> {
    return this.frameService.statistic(context.req.user.id);
  }

  @Mutation(() => Frame)
  async clientCreateFrame(
    @Args('input') input: FrameInput,
    @Context() context: any,
    @Args({ name: 'image', type: () => GraphQLUpload, nullable: true })
    image?: FileUpload,
  ): Promise<Frame> {
    return this.frameService.create(input, context.req.user.id, image);
  }

  @Mutation(() => Frame)
  async clientUpdateFrame(
    @Args('input') input: UpdateFrameInput,
    @Context() context: any,
    @Args({ name: 'image', type: () => GraphQLUpload, nullable: true })
    image?: FileUpload,
  ): Promise<Frame> {
    return this.frameService.update(input, context.req.user.id, image);
  }

  @Mutation(() => Frame)
  async clientDeleteFrame(
    @Args('id') id: string,
    @Context() context: any,
  ): Promise<Frame> {
    return this.frameService.delete(id, context.req.user.id);
  }
}
