import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Frame } from '../../common/entity/frame.entity';
import { Client } from '../../common/entity/client.entity';
import { Machine } from '../../common/entity/machine.entity';
import { MachineStatus } from '../../common/entity/machineStatus.entity';
import { FileService } from 'src/modules/common/module/file/file.service';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { FrameService } from './frame.service';
import { FrameResolver } from './frame.resolver';
import { FrameItem } from 'src/modules/common/entity/frameItem.entity';
import { Order } from 'src/modules/clientApp/order/order.entity';
import { MachineStatusService } from '../machineStatus/machineStatus.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Frame,
      Order,
      Client,
      FrameItem,
      Machine,
      MachineStatus,
    ]),
  ],
  providers: [
    FrameService,
    FrameResolver,
    JwtService,
    SBLogger,
    FileService,
    MachineStatusService,
    FirestoreService,
  ],
  exports: [FrameService],
})
export class FrameModule {}
