import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { SBLogger } from '../../logger/logger.service';
import { Frame } from '../../common/entity/frame.entity';
import { FrameItem } from '../../common/entity/frameItem.entity';
import { FrameInput, UpdateFrameInput } from './frame.input';
import { FileService } from 'src/modules/common/module/file/file.service';
import { FileUpload } from 'graphql-upload';
import * as dayjs from 'dayjs';
import { Order } from 'src/modules/clientApp/order/order.entity';
import { Machine } from '../../common/entity/machine.entity';
import { MachineStatusService } from '../machineStatus/machineStatus.service';

@Injectable()
export class FrameService {
  constructor(
    @InjectRepository(Frame)
    private frameRepository: Repository<Frame>,

    @InjectRepository(FrameItem)
    private frameItemRepository: Repository<FrameItem>,

    @InjectRepository(Order)
    private orderRepository: Repository<Order>,

    @InjectRepository(Machine)
    private machineRepository: Repository<Machine>,

    private loggerService: SBLogger,
    private fileService: FileService,
    private machineStatusService: MachineStatusService,
  ) {}

  /**
   * Lấy danh sách Frame theo điều kiện
   */
  async findAll(
    clientId: string,
    topicId?: string,
    size?: string,
  ): Promise<Frame[]> {
    const whereCondition: any = { clientId };
    if (topicId) whereCondition.topicId = topicId;
    if (size) whereCondition.frameSize = size;

    const result = await this.frameRepository.find({
      where: whereCondition,
      relations: ['frameItems'],
    });

    return result;
  }

  /**
   * Lấy thông tin Frame theo ID
   */
  async findOne(id: string, clientId: string): Promise<Frame> {
    const frame = await this.frameRepository.findOne({
      where: { id, clientId },
      relations: ['frameItems'],
    });

    if (!frame) throw new NotFoundException(`Frame with ID ${id} not found.`);
    return frame;
  }

  async statistic(clientId: string): Promise<{
    mostUsed: { frame: Frame; usedCount: number }[];
    leastUsed: { frame: Frame; usedCount: number }[];
  }> {
    const today = dayjs().endOf('day').toDate();
    const last30days = dayjs().subtract(30, 'day').startOf('day').toDate();
    const orders = await this.orderRepository
      .createQueryBuilder('order')
      .select('order.frameId', 'frameId')
      .addSelect('COUNT(order.id)', 'orderCount')
      .leftJoin(Frame, 'frame', 'frame.id = order.frameId')
      .where(
        'order.clientId = :clientId AND order.createdAt BETWEEN :last30days AND :today',
        {
          clientId,
          last30days,
          today,
        },
      )
      .groupBy('order.frameId')
      .getRawMany();
    if (orders.length === 0) {
      return { mostUsed: [], leastUsed: [] };
    }
    const usedFrames = await this.frameRepository.find({
      where: {
        id: In(orders.map((order) => order.frameId)),
      },
    });
    const frameUsage = usedFrames.map((frame) => ({
      frame: frame,
      usedCount:
        orders.find((order) => order.frameId === frame.id)?.orderCount || 0,
    }));
    const mostUsed = frameUsage
      .sort((a, b) => b.usedCount - a.usedCount)
      .slice(0, 5);
    const leastUsed = frameUsage
      .sort((a, b) => a.usedCount - b.usedCount)
      .slice(0, 5);
    return { mostUsed, leastUsed };
  }

  /**
   * Tạo mới Frame
   */
  async create(
    input: FrameInput,
    clientId: string,
    image?: FileUpload,
  ): Promise<Frame> {
    try {
      const frame = this.frameRepository.create({ ...input, clientId });

      if (image) {
        frame.imageUrl = await this.uploadImage(
          image,
          `frames/${clientId}/${Date.now().toString()}`,
        );
      }

      frame.frameItems = input.frameItems.map((item) => {
        const frameItem = this.frameItemRepository.create(item);
        frameItem.frame = frame;
        return frameItem;
      });
      return await this.frameRepository.save(frame);
    } catch (error) {
      this.loggerService.log('Error - frame - create', error);
      throw error;
    }
  }

  async update(
    input: UpdateFrameInput,
    clientId: string,
    image?: FileUpload,
  ): Promise<Frame> {
    try {
      let frame = await this.findOne(input.id, clientId);

      if (image) {
        frame.imageUrl = await this.uploadImage(
          image,
          `frames/${clientId}/${frame.id}`,
        );
      }

      Object.assign(frame, input);

      frame = await this.processFrameItems(frame, input.frameItems);

      return await this.frameRepository.save(frame);
    } catch (error) {
      this.loggerService.log('Error - frame - update', error);
      throw error;
    }
  }

  async delete(id: string, clientId: string): Promise<Frame> {
    try {
      const frame = await this.findOne(id, clientId);
      await this.frameRepository.softDelete(id);

      // Sync all client machines after successful frame deletion (non-blocking)
      this.syncAllClientMachines(clientId).catch((error) => {
        this.loggerService.log('Error - frame - delete - sync failed', {
          frameId: id,
          clientId,
          error: error.message || error,
        });
      });

      return frame;
    } catch (error) {
      this.loggerService.log('Error - frame - delete', error);
      throw error;
    }
  }

  private async processFrameItems(
    frame: Frame,
    frameItems: UpdateFrameInput['frameItems'],
  ) {
    if (!frameItems) return;

    const itemsToDelete = frameItems.filter(
      (item) => item.id && item.isDeleted,
    );
    const itemsToUpdate = frameItems.filter(
      (item) => item.id && !item.isDeleted,
    );
    const itemsToAdd = frameItems.filter((item) => !item.id);

    if (itemsToDelete.length) {
      await this.frameItemRepository.delete(
        itemsToDelete.map((item) => item.id),
      );
    }

    if (itemsToUpdate.length) {
      for (const item of itemsToUpdate) {
        const existingItem = await this.frameItemRepository.findOne({
          where: { id: item.id },
        });
        if (!existingItem)
          throw new NotFoundException(
            `FrameItem with ID ${item.id} not found.`,
          );
        Object.assign(existingItem, item);
        existingItem.position = item.position ?? existingItem.position;
        existingItem.frame = frame;
        await this.frameItemRepository.save(existingItem);
      }
    }

    const newItems = itemsToAdd.map((item) => {
      const frameItem = this.frameItemRepository.create(item);
      frameItem.frame = frame;
      return frameItem;
    });
    if (newItems.length > 0) {
      const clientFrameItems = await this.frameItemRepository.save(newItems);
      frame.frameItems = [...(frame.frameItems || []), ...clientFrameItems];
    }
    frame.frameItems = await this.frameItemRepository.find({
      where: { frame: { id: frame.id } },
    });
    return frame;
  }

  private async uploadImage(image: FileUpload, path: string): Promise<string> {
    const buffer = await this.streamToBuffer(image.createReadStream());
    return await this.fileService.singleUpload(buffer, image.filename, path);
  }

  private async streamToBuffer(stream: NodeJS.ReadableStream): Promise<Buffer> {
    return new Promise<Buffer>((resolve, reject) => {
      const chunks: Buffer[] = [];
      stream.on('data', (chunk) => chunks.push(chunk));
      stream.on('end', () => resolve(Buffer.concat(chunks)));
      stream.on('error', reject);
    });
  }

  /**
   * Get list of machine IDs for a client
   */
  private async getClientMachineIds(clientId: string): Promise<string[]> {
    try {
      const result = await this.machineRepository
        .createQueryBuilder('machine')
        .select('machine.id')
        .where('machine.userId = :clientId', { clientId })
        .getMany();

      return result.map((machine) => machine.id);
    } catch (error) {
      this.loggerService.log('Error - frame - getClientMachineIds', error);
      return [];
    }
  }

  /**
   * Sync all machines for a client
   */
  private async syncAllClientMachines(clientId: string): Promise<void> {
    try {
      const machineIds = await this.getClientMachineIds(clientId);

      if (machineIds.length === 0) {
        this.loggerService.log(
          'Info - frame - syncAllClientMachines',
          `No machines found for client ${clientId}`,
        );
        return;
      }

      // Sync each machine in parallel
      const syncPromises = machineIds.map((machineId) =>
        this.machineStatusService
          .pushFirestoreSync(machineId, clientId)
          .catch((error) => {
            this.loggerService.log('Error - frame - syncMachine', {
              machineId,
              error: error.message || error,
            });
            // Don't throw error to avoid interrupting other machine syncs
          }),
      );

      await Promise.all(syncPromises);
      this.loggerService.log(
        'Info - frame - syncAllClientMachines',
        `Successfully synced ${machineIds.length} machines for client ${clientId}`,
      );
    } catch (error) {
      this.loggerService.log('Error - frame - syncAllClientMachines', {
        clientId,
        error: error.message || error,
      });
      // Don't throw error to avoid interrupting frame deletion
    }
  }
}
