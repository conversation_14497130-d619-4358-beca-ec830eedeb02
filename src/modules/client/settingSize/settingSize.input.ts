import { InputType, Field, Float, Int } from '@nestjs/graphql';
import { ESettingSizeStatus, ESettingSizeType } from 'src/enum';

@InputType()
export class CreateSettingSizeInput {
  @Field({ nullable: true })
  name?: string;

  @Field()
  settingSizeType: ESettingSizeType;

  @Field(() => String, { nullable: true })
  machineIds?: string;

  @Field(() => Int, { nullable: true })
  smallSizePrice2?: number;

  @Field(() => Int, { nullable: true })
  smallSizePrice4?: number;

  @Field(() => Int, { nullable: true })
  smallSizePrice6?: number;

  @Field(() => Int, { nullable: true })
  smallSizePrice8?: number;

  @Field(() => Int, { nullable: true })
  smallSizePrice10?: number;

  @Field(() => Int, { nullable: true })
  largeSizePrice2?: number;
  
  @Field(() => Int, { nullable: true })
  largeSizePrice3?: number;

  @Field(() => Int, { nullable: true })
  largeSizePrice4?: number;

  @Field(() => Int, { nullable: true })
  largeSizePrice5?: number;

  @Field(() => Int, { nullable: true })
  largeSizePrice6?: number;

  @Field({ nullable: true })
  status?: ESettingSizeStatus;
}

@InputType()
export class UpdateSettingSizeInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field(() => String, { nullable: true })
  machineIds?: string;

  @Field(() => Int, { nullable: true })
  smallSizePrice2?: number;

  @Field(() => Int, { nullable: true })
  smallSizePrice4?: number;

  @Field(() => Int, { nullable: true })
  smallSizePrice6?: number;

  @Field(() => Int, { nullable: true })
  smallSizePrice8?: number;

  @Field(() => Int, { nullable: true })
  smallSizePrice10?: number;

  @Field(() => Int, { nullable: true })
  largeSizePrice2?: number;
  
  @Field(() => Int, { nullable: true })
  largeSizePrice3?: number;

  @Field(() => Int, { nullable: true })
  largeSizePrice4?: number;

  @Field(() => Int, { nullable: true })
  largeSizePrice5?: number;

  @Field(() => Int, { nullable: true })
  largeSizePrice6?: number;

  @Field({ nullable: true })
  status?: ESettingSizeStatus;
}
