type SettingSize {
  id: ID!
  name: String!
  settingSizeType: String!
  machineIds: String
  clientId: String
  createdAt: String
  updatedAt: String
  smallSizePrice2: Int
  smallSizePrice4: Int
  smallSizePrice6: Int
  smallSizePrice8: Int
  smallSizePrice10: Int
  largeSizePrice2: Int
  largeSizePrice3: Int
  largeSizePrice4: Int
  largeSizePrice5: Int
  largeSizePrice6: Int
}

input CreateSettingSizeInput {
  name: String!
  settingSizeType: String
  machineIds: String
  smallSizePrice: Float
  largeSizePrice: Float
}

input UpdateSettingSizeInput {
  id: ID!
  name: String
  machineIds: String
  smallSizePrice: Float
  largeSizePrice: Float
}

type Query {
  clientGetSettingSizes: [SettingSize!]!
  clientGetSettingSize(id: ID!): SettingSize!
}

type Mutation {
  clientCreateSettingSize(createSettingSizeInput: CreateSettingSizeInput!): SettingSize
  clientUpdateSettingSize(updateSettingSizeInput: UpdateSettingSizeInput!): SettingSize
  clientDeleteSettingSize(id: ID!): SettingSize
}
