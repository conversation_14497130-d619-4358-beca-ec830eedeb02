import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateSettingSizeInput, UpdateSettingSizeInput } from './settingSize.input';
import { TSettingSizeResponse } from './settingSize.type';
import { SettingSize } from 'src/modules/common/entity/settingSize.entity';
import { ESettingSizeType } from 'src/enum';

@Injectable()
export class SettingSizeService {
  constructor(
    @InjectRepository(SettingSize)
    private readonly settingSizeRepository: Repository<SettingSize>,
  ) {}

  async findAll(clientId: string): Promise<SettingSize[]> {
    return this.settingSizeRepository.find({ where: { clientId } });
  }

  async findOne(id: string, clientId: string): Promise<SettingSize> {
    return this.settingSizeRepository.findOne({ where: { id, clientId } });
  }

  async create(createSettingSizeInput: CreateSettingSizeInput, clientId: string): Promise<TSettingSizeResponse> {
    const settingSize = this.settingSizeRepository.create(createSettingSizeInput);
    settingSize.clientId = clientId;
    settingSize.settingSizeType = ESettingSizeType.Custom;
    return this.settingSizeRepository.save(settingSize);
  }

  async update(updateSettingSizeInput: UpdateSettingSizeInput): Promise<TSettingSizeResponse> {
    const { id, ...updates } = updateSettingSizeInput;
    const settingSize = await this.settingSizeRepository.findOne({ where: { id } });
    if (!settingSize) {
      throw new Error('SettingSize not found');
    }

    Object.assign(settingSize, updates);
    return this.settingSizeRepository.save(settingSize);
  }

  async delete(id: string): Promise<SettingSize> {
    const settingSize = await this.settingSizeRepository.findOne({ where: { id } });
    if (!settingSize) {
      throw new Error('SettingSize not found');
    }
    await this.settingSizeRepository.remove(settingSize);
    return settingSize;
  }
}
