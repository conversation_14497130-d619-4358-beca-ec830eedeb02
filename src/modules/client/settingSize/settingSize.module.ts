import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { SettingSize } from '../../common/entity/settingSize.entity';
import { SettingSizeService } from './settingSize.service';
import { SettingSizeResolver } from './settingSize.resolver';

@Module({
  imports: [TypeOrmModule.forFeature([SettingSize])],
  providers: [SettingSizeService, SettingSizeResolver, JwtService, SBLogger],
  exports: [SettingSizeService],
})
export class SettingSizeModule {}
