import { Resolver, Query, Mutation, Args, Context } from '@nestjs/graphql';
import { SettingSize } from '../../common/entity/settingSize.entity';
import { CreateSettingSizeInput, UpdateSettingSizeInput } from './settingSize.input';
import { UseGuards } from '@nestjs/common';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { TSettingSizeResponse } from './settingSize.type';
import { SettingSizeService } from './settingSize.service';
@Resolver(() => SettingSize)
@UseGuards(ClientAuthGuard)
export class SettingSizeResolver {
  constructor(private readonly settingSizeService: SettingSizeService) {}

  @Query(() => [SettingSize])
  async clientGetSettingSizes(
    @Context() context: any,
  ): Promise<SettingSize[]> {
    return this.settingSizeService.findAll(context.req.user.id);
  }

  @Query(() => SettingSize)
  async clientGetSettingSize(
    @Args('id') id: string,
    @Context() context: any,
  ): Promise<SettingSize> {
    return this.settingSizeService.findOne(id, context.req.user.id);
  }

  @Mutation(() => SettingSize)
  async clientCreateSettingSize(
    @Args('input') input: CreateSettingSizeInput,
    @Context() context: any,
  ): Promise<TSettingSizeResponse> {
    return this.settingSizeService.create(input, context.req.user.id);
  } 

  @Mutation(() => SettingSize)
  async clientUpdateSettingSize(@Args('input') input: UpdateSettingSizeInput): Promise<TSettingSizeResponse> {
    return this.settingSizeService.update(input);
  }

  @Mutation(() => SettingSize)
  async clientDeleteSettingSize(@Args('id') id: string): Promise<SettingSize> {
    return this.settingSizeService.delete(id);
  }
} 
