import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MachineStatus } from 'src/modules/common/entity/machineStatus.entity';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { EMachineStatus } from 'src/enum';
@Injectable()
export class MachineStatusService {
  constructor(
    @InjectRepository(MachineStatus)
    private readonly machineStatusRepository: Repository<MachineStatus>,
    private firestoreService: FirestoreService,
  ) {}

  async getMachineStatuses(context: any): Promise<MachineStatus[]> {
    const { user } = context.req;

    return this.machineStatusRepository.find({
      where: {
        clientId: user.id,
      },
      relations: ['machine'],
      order: {
        createdAt: 'DESC',
      },
    });
  }

  async pushFirestoreSync(
    machineId: string,
    clientId: string,
  ): Promise<boolean> {
    try {
      const machineStatus = await this.machineStatusRepository.findOne({
        where: { machineId, clientId },
        relations: ['machine'],
      });
      if (!machineStatus) {
        throw new NotFoundException('Machine not found');
      }

      const data = {
        isUpdated: true,
      };

      await this.firestoreService.pushData(
        `SyncMachines`,
        machineId,
        data,
        false,
      );
      machineStatus.status = EMachineStatus.PROCESSING;
      await this.machineStatusRepository.save(machineStatus);
      return true;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
