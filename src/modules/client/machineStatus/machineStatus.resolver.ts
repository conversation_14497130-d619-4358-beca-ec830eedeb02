import { Query, Resolver, Args, Context, Mutation } from '@nestjs/graphql';
import { ClientAuthGuard } from 'src/guards/clientAuth.guard';
import { UseGuards } from '@nestjs/common';
import { MachineStatus } from 'src/modules/common/entity/machineStatus.entity';
import { MachineStatusService } from './machineStatus.service';

@Resolver()
@UseGuards(ClientAuthGuard)
export class MachineStatusResolver {
  constructor(private readonly machineStatusService: MachineStatusService) {}

  @Query(() => [MachineStatus])
  clientGetMachineStatuses(@Context() context: any): Promise<MachineStatus[]> {
    return this.machineStatusService.getMachineStatuses(context);
  }

  @Mutation(() => Boolean)
  clientUpdateMachineStatus(
    @Context() context: any,
    @Args('machineId') input: string,
  ): Promise<boolean> {
    return this.machineStatusService.pushFirestoreSync(
      input,
      context.req.user.id,
    );
  }
}
