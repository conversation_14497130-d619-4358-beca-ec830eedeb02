import { Field, InputType, ObjectType } from '@nestjs/graphql';

@InputType()
export class AdminCreateClientAccountInput {
  @Field()
  name: string;

  @Field()
  email: string;

  @Field()
  password: string;
}

@InputType()
export class AdminUpdateClientAccountInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  password?: string;

  @Field({ nullable: true })
  email?: string;
}

@InputType()
export class AdminGetClientAccountInput {
  @Field({ nullable: true })
  name: string;

  @Field({ nullable: true })
  email?: string;
}

@ObjectType()
export class AdminClientAccountResponse {
  @Field()
  id: string;

  @Field()
  clientId: string;

  @Field()
  name: string;

  @Field()
  email: string;

  @Field()
  createdAt: string;
}
