type AdminClientAccountResponse {
  id: ID!
  clientId: ID!
  name: String!
  email: String!
  createdAt: String!
}

input AdminCreateClientAccountInput {
  name: String!
  email: String!
  password: String!
}

input AdminUpdateClientAccountInput {
  id: ID
  name: String!
  password: String!
}

input AdminGetClientAccountInput {
  name: String!
  email: String!
}

type AdminClientGenerateFramesRes {
  isSuccess: Boolean!
}

type Query {
  adminClientAccountById(id: ID!, clientId: ID!): AdminClientAccountResponse
}

type Mutation {
  adminClientAccounts(input: AdminGetClientAccountInput, clientId: ID!): [AdminClientAccountResponse]
  adminCreateClientAccount(input: AdminCreateClientAccountInput, clientId: ID!): AdminClientAccountResponse
  adminUpdateClientAccount(input: AdminUpdateClientAccountInput, clientId: ID!): AdminClientAccountResponse
  adminDeleteClientAccount(id: ID!, clientId: ID!): AdminClientAccountResponse
}
