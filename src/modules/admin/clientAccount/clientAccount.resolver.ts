import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import {
  AdminCreateClientAccountInput,
  AdminGetClientAccountInput,
  AdminUpdateClientAccountInput,
  AdminClientAccountResponse,
} from './clientAccount.input';
import { ClientAccountService } from './clientAccount.service';
import { Client } from '../../common/entity/client.entity';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { ClientAccount } from 'src/modules/common/entity/clientAccount.entity';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermissions,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';

@Resolver(() => Client)
@UseGuards(AuthGuard)
export class ClientAccountResolver {
  constructor(private clientAccountService: ClientAccountService) {}

  @Mutation(() => [AdminClientAccountResponse])
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_DETAILS)
  async adminClientAccounts(
    @Args('input') input: AdminGetClientAccountInput,
    @Args('clientId', { type: () => ID }) clientId: string,
  ): Promise<AdminClientAccountResponse[]> {
    return this.clientAccountService.getClientAccounts(input, clientId);
  }

  @Query(() => ClientAccount)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_DETAILS)
  async adminClientAccountById(
    @Args('id', { type: () => ID }) id: string,
    @Args('clientId', { type: () => ID }) clientId: string,
  ): Promise<ClientAccount> {
    return this.clientAccountService.findOneById(id, clientId);
  }

  @Mutation(() => ClientAccount)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminCreateClientAccount(
    @Args('input') input: AdminCreateClientAccountInput,
    @Args('clientId', { type: () => ID }) clientId: string,
  ): Promise<ClientAccount> {
    return this.clientAccountService.create(input, clientId);
  }

  @Mutation(() => ClientAccount)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminUpdateClientAccount(
    @Args('input') input: AdminUpdateClientAccountInput,
    @Args('clientId', { type: () => ID }) clientId: string,
  ): Promise<ClientAccount> {
    return this.clientAccountService.update(input, clientId);
  }

  @Mutation(() => ClientAccount)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminDeleteClientAccount(
    @Args('id', { type: () => ID }) id: string,
    @Args('clientId', { type: () => ID }) clientId: string,
  ): Promise<ClientAccount> {
    return this.clientAccountService.delete(id, clientId);
  }
}
