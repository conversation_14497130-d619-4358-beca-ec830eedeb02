import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientAccountService } from './clientAccount.service';
import { ClientAccountResolver } from './clientAccount.resolver';
import { Client } from '../../common/entity/client.entity';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { ClientAccount } from 'src/modules/common/entity/clientAccount.entity';
@Module({
  imports: [TypeOrmModule.forFeature([Client, ClientAccount])],
  providers: [
    JwtService,
    ClientAccountService,
    ClientAccountResolver,
    SBLogger,
  ],
  exports: [ClientAccountService],
})
export class ClientAccountModule {}
