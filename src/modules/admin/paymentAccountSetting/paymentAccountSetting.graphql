type PaymentAccountSetting {
  id: ID!
  bankName: String
  type: String
  apiId: String
  apiKey: String
  checkSum: String
  isActive: Boolean
  name: String
  clientId: String
  createdAt: String
  updatedAt: String
  mBank: MBank
  bankOwnerName: String
  bankAccountNumber: String
  mBankId: String
}

input CreatePaymentAccountSettingInput {
  bankName: String!
  type: String!
  apiId: String
  apiKey: String
  checkSum: String
  mBankId: String
  bankOwnerName: String
  bankAccountNumber: String
  name: String
  clientId: String!
  isActive: Boolean!
}

input UpdatePaymentAccountSettingInput {
  id: ID!
  bankName: String
  apiId: String
  name: String
  apiKey: String
  checkSum: String
  clientId: String
  isActive: Boolean
  mBankId: String
  bankOwnerName: String
  bankAccountNumber: String
}

type Query {
  adminGetPaymentAccountSettings(clientId: String!): [PaymentAccountSetting!]!
  adminGetPaymentAccountSettingById(id: ID!): PaymentAccountSetting
}

type Mutation {
  adminCreatePaymentAccountSetting(input: CreatePaymentAccountSettingInput!): PaymentAccountSetting!
  adminUpdatePaymentAccountSetting(input: UpdatePaymentAccountSettingInput!): PaymentAccountSetting!
  adminDeletePaymentAccountSetting(id: ID!): Boolean!
}
