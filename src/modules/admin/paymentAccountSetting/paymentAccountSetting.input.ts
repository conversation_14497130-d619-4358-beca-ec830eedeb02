import { InputType, Field, ID, registerEnumType } from '@nestjs/graphql';

export enum EPaymentAccountSettingType {
  PAYOS = 'PAYOS',
  BANK = 'BANK',
}

registerEnumType(EPaymentAccountSettingType, {
  name: 'EPaymentAccountSettingType',
});

@InputType()
export class CreatePaymentAccountSettingInput {
  @Field()
  bankName: string;

  @Field({ nullable: true })
  apiId: string;

  @Field({ nullable: true })
  apiKey: string;

  @Field({ nullable: true })
  checkSum: string;

  @Field()
  isActive: boolean;

  @Field()
  clientId: string;

  @Field({ nullable: true })
  name: string;

  @Field({ nullable: true })
  mBankId: string;

  @Field({ nullable: true })
  bankOwnerName: string;

  @Field({ nullable: true })
  bankAccountNumber: string;

  @Field()
  type: EPaymentAccountSettingType;
}

@InputType()
export class UpdatePaymentAccountSettingInput extends CreatePaymentAccountSettingInput {
  @Field(() => ID)
  id: string;
}
