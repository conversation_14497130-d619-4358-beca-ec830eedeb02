import { Resolver, Mutation, Query, Args, ID } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { PaymentAccountSetting } from '../../common/entity/paymentAccountSetting.entity';
import { PaymentAccountSettingService } from './paymentAccountSetting.service';
import {
  CreatePaymentAccountSettingInput,
  UpdatePaymentAccountSettingInput,
} from './paymentAccountSetting.input';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermissions,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';

@Resolver(() => PaymentAccountSetting)
@UseGuards(AuthGuard)
export class PaymentAccountSettingResolver {
  constructor(
    private readonly paymentAccountSettingService: PaymentAccountSettingService,
  ) {}

  @Query(() => [PaymentAccountSetting])
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_DETAILS)
  async adminGetPaymentAccountSettings(
    @Args('clientId', { type: () => String }) clientId: string,
  ): Promise<PaymentAccountSetting[]> {
    return this.paymentAccountSettingService.findByClientId(clientId);
  }

  @Query(() => PaymentAccountSetting, { nullable: true })
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_DETAILS)
  async adminGetPaymentAccountSettingById(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<PaymentAccountSetting | null> {
    return this.paymentAccountSettingService.findById(id);
  }

  @Mutation(() => PaymentAccountSetting)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminCreatePaymentAccountSetting(
    @Args('input') input: CreatePaymentAccountSettingInput,
  ): Promise<PaymentAccountSetting> {
    return this.paymentAccountSettingService.create(input);
  }

  @Mutation(() => PaymentAccountSetting)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminUpdatePaymentAccountSetting(
    @Args('input') input: UpdatePaymentAccountSettingInput,
  ): Promise<PaymentAccountSetting> {
    return this.paymentAccountSettingService.update(input);
  }

  @Mutation(() => Boolean)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminDeletePaymentAccountSetting(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<boolean> {
    return this.paymentAccountSettingService.delete(id);
  }
}
