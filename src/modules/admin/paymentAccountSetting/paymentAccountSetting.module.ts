import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PaymentAccountSettingService } from './paymentAccountSetting.service';
import { PaymentAccountSettingResolver } from './paymentAccountSetting.resolver';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { PaymentAccountSetting } from '../../common/entity/paymentAccountSetting.entity';
import { PayosService } from 'src/modules/common/module/payos/payos.service';
import { Client } from '../../common/entity/client.entity';

@Module({
  imports: [TypeOrmModule.forFeature([PaymentAccountSetting, Client])],
  providers: [
    PaymentAccountSettingService,
    JwtService,
    PaymentAccountSettingResolver,
    SBLogger,
    PayosService,
  ],
  exports: [PaymentAccountSettingService],
})
export class PaymentAccountSettingModule {}
