import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PaymentAccountSetting } from '../../common/entity/paymentAccountSetting.entity';
import {
  CreatePaymentAccountSettingInput,
  UpdatePaymentAccountSettingInput,
  EPaymentAccountSettingType,
} from './paymentAccountSetting.input';
import { PayosService } from 'src/modules/common/module/payos/payos.service';

@Injectable()
export class PaymentAccountSettingService {
  constructor(
    private readonly payosService: PayosService,
    @InjectRepository(PaymentAccountSetting)
    private readonly paymentAccountSettingRepository: Repository<PaymentAccountSetting>,
  ) {}

  async findByClientId(clientId: string): Promise<PaymentAccountSetting[]> {
    return this.paymentAccountSettingRepository.find({
      where: { clientId },
      relations: ['mBank'],
    });
  }

  async findById(id: string): Promise<PaymentAccountSetting | null> {
    const paymentAccountSetting =
      await this.paymentAccountSettingRepository.findOne({
        where: { id },
        relations: ['mBank'],
      });

    if (!paymentAccountSetting) {
      throw new NotFoundException(
        `PaymentAccountSetting with ID ${id} not found`,
      );
    }

    return paymentAccountSetting;
  }

  async create(
    input: CreatePaymentAccountSettingInput,
  ): Promise<PaymentAccountSetting> {
    if (input.isActive) {
      await this.deactivateExistingActiveRecord(input.clientId);
    }

    await this.verifyInforPayment(input);

    const newPaymentAccountSetting =
      this.paymentAccountSettingRepository.create(input);

    return this.paymentAccountSettingRepository.save(newPaymentAccountSetting);
  }

  async update(
    input: UpdatePaymentAccountSettingInput,
  ): Promise<PaymentAccountSetting> {
    const paymentAccountSetting =
      await this.paymentAccountSettingRepository.findOne({
        where: { id: input.id },
      });

    if (!paymentAccountSetting) {
      throw new NotFoundException(
        `PaymentAccountSetting with ID ${input.id} not found`,
      );
    }

    if (input.isActive) {
      await this.deactivateExistingActiveRecord(paymentAccountSetting.clientId);
    }

    await this.verifyInforPayment(input);

    Object.assign(paymentAccountSetting, input);

    return this.paymentAccountSettingRepository.save(paymentAccountSetting);
  }

  async delete(id: string): Promise<boolean> {
    const paymentAccountSetting = await this.findById(id);

    const result = await this.paymentAccountSettingRepository.remove(
      paymentAccountSetting,
    );

    return !!result;
  }

  private async deactivateExistingActiveRecord(
    clientId: string,
  ): Promise<void> {
    const activeSetting = await this.paymentAccountSettingRepository.findOne({
      where: { clientId, isActive: true },
    });

    if (activeSetting) {
      activeSetting.isActive = false;
      await this.paymentAccountSettingRepository.save(activeSetting);
    }
  }

  private async verifyInforPayment(input: any) {
    if (input.type === EPaymentAccountSettingType.PAYOS) {
      if (!input.apiId || !input.apiKey || !input.checkSum) {
        throw new BadRequestException(
          'Missing required fields for payment authentication.',
        );
      }
      const resPaymentConfirm = await this.payosService.confirmCallbackUrl(
        input.apiId,
        input.apiKey,
      );

      if (resPaymentConfirm.code && resPaymentConfirm.code !== '00') {
        throw new BadRequestException(
          'Unable to create authenticated payment information',
        );
      }
    }

    if (input.type === EPaymentAccountSettingType.BANK) {
      if (!input.mBankId || !input.bankOwnerName || !input.bankAccountNumber) {
        throw new BadRequestException(
          'Missing required fields for payment authentication.',
        );
      }
    }
  }
}
