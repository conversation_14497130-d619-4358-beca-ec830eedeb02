type ClientInfo {
  id: String!
  name: String!
  ownerName: String
}

type MachineInfo {
  id: String!
  machineCode: String!
  location: String
  machineName: String
}

type TopicInfo {
  id: String!
  name: String!
  extraFee: Float
}

type PromotionInfo {
  id: String
  name: String
  code: String
  discountValue: Float
}

type ImageStatusInfo {
  hasVideo: Boolean!
  hasMergedImage: Boolean!
  hasRegularImages: Boolean!
  totalImages: Int!
  videoCount: Int!
  mergedImageCount: Int!
  regularImageCount: Int!
  isComplete: Boolean!
}

type AppOrderSummary {
  totalOrders: Int!
  totalRevenue: Float!
  totalReceivedAmount: Float!
  totalDiscountAmount: Float!
}

type AppOrderDetailResponse {
  id: String!
  orderCode: String
  amount: Float
  totalOrderNumber: Float
  receivedAmount: Float
  description: String
  refCode: String
  imageNumber: Int
  status: String
  paymentMethod: String
  captureMode: String
  createdAt: DateTime
  denominations: String
  discountAmount: Float
  client: ClientInfo
  machine: MachineInfo
  topic: TopicInfo
  promotion: PromotionInfo
  finalImageUrl: String
  imageDomain: String
  clientName: String
  imageStatus: ImageStatusInfo
}

type AppOrderListResponse {
  orders: [AppOrderDetailResponse!]!
  total: Int!
  page: Int!
  limit: Int!
  totalPages: Int!
  summary: AppOrderSummary!
}

input AppOrderListInput {
  search: String
  clientId: String
  startDate: String
  endDate: String
  page: Int = 1
  limit: Int = 10
}

type Query {
  adminGetAppOrderById(id: String!): AppOrderDetailResponse
  adminGetAppOrderList(input: AppOrderListInput!): AppOrderListResponse!
}
