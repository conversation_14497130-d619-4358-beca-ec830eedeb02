import { Resolver, Query, Args } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermissions,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';
import { AppOrderService } from './appOrder.service';
import {
  AppOrderDetailResponse,
  AppOrderListInput,
  AppOrderListResponse,
} from './appOrder.input';

@Resolver('AppOrder')
@UseGuards(AuthGuard)
export class AppOrderResolver {
  constructor(private readonly appOrderService: AppOrderService) {}

  @Query(() => AppOrderDetailResponse, { nullable: true })
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_ORDERS)
  async adminGetAppOrderById(
    @Args('id') id: string,
  ): Promise<AppOrderDetailResponse | null> {
    return this.appOrderService.getAppOrderById(id);
  }

  @Query(() => AppOrderListResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_ORDERS)
  async adminGetAppOrderList(
    @Args('input') input: AppOrderListInput,
  ): Promise<AppOrderListResponse> {
    return this.appOrderService.getAppOrderList(input);
  }
}
