import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { AppOrderResolver } from './appOrder.resolver';
import { AppOrderService } from './appOrder.service';
import { Order } from '../../clientApp/order/order.entity';
import { Client } from '../../common/entity/client.entity';
import { Machine } from '../../common/entity/machine.entity';
import { Topic } from '../../common/entity/topic.entity';
import { PromotionCode } from '../../common/entity/promotionCode.entity';
import { AppImage } from '../../clientApp/uploadImage/image.entity';
import { jwtConstants } from '../../../constants';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Order,
      Client,
      Machine,
      Topic,
      PromotionCode,
      AppImage,
    ]),
    JwtModule.register(jwtConstants),
  ],
  providers: [AppOrderResolver, AppOrderService],
  exports: [AppOrderService],
})
export class AppOrderModule {}
