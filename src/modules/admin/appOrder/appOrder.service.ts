import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order } from '../../clientApp/order/order.entity';
import { Client } from '../../common/entity/client.entity';
import { Machine } from '../../common/entity/machine.entity';
import { Topic } from '../../common/entity/topic.entity';
import { PromotionCode } from '../../common/entity/promotionCode.entity';
import { AppImage } from '../../clientApp/uploadImage/image.entity';
import { EImageFileType } from '../../../enum';
import { convertDateRangeFromUnix } from '../../../utils/time.utils';
import {
  AppOrderDetailResponse,
  AppOrderListInput,
  AppOrderListResponse,
  AppOrderSummary,
  ImageStatusInfo,
} from './appOrder.input';

@Injectable()
export class AppOrderService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(Client)
    private readonly clientRepository: Repository<Client>,
    @InjectRepository(Machine)
    private readonly machineRepository: Repository<Machine>,
    @InjectRepository(Topic)
    private readonly topicRepository: Repository<Topic>,
    @InjectRepository(PromotionCode)
    private readonly promotionCodeRepository: Repository<PromotionCode>,
    @InjectRepository(AppImage)
    private readonly appImageRepository: Repository<AppImage>,
  ) {}

  async getAppOrderById(id: string): Promise<AppOrderDetailResponse | null> {
    const order = await this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.machine', 'machine')
      .leftJoinAndSelect('order.topic', 'topic')
      .leftJoinAndSelect('order.promotionCode', 'promotionCode')
      .leftJoinAndSelect('promotionCode.promotion', 'promotion')
      .leftJoinAndSelect('order.client', 'client')
      .where('order.id = :id', { id })
      .getOne();

    if (!order) {
      return null;
    }

    // Calculate discount amount
    const discountAmount =
      Number(order.promotionCode?.promotion?.discountValue) || 0;

    // Get final image URL
    const finalImageUrl = await this.getFinalImageUrl(order.id);

    // Get image status
    const imageStatus = await this.getImageStatus(order.id);

    // Get image domain
    const imageDomain = this.getImageDomain(order.id);

    // Get client name
    const clientName = order.client?.name || 'Unknown';

    return {
      id: order.id,
      orderCode: order.orderCode,
      amount: order.amount,
      totalOrderNumber: order.totalOrderNumber,
      receivedAmount: order.receivedAmount,
      description: order.description,
      refCode: order.refCode,
      imageNumber: order.imageNumber,
      status: order.status,
      paymentMethod: order.paymentMethod,
      captureMode: order.captureMode,
      createdAt: order.createdAt,
      denominations:
        typeof order.denominations === 'string'
          ? order.denominations
          : JSON.stringify(order.denominations),
      discountAmount,
      client: order.client
        ? {
            id: order.client.id,
            name: order.client.name,
            ownerName: order.client.ownerName || null,
          }
        : null,
      machine: order.machine
        ? {
            id: order.machine.id,
            machineCode: order.machine.machineCode,
            location: order.machine.location,
            machineName: order.machine.machineCode, // Using machineCode as name for now
          }
        : null,
      topic: order.topic
        ? {
            id: order.topic.id,
            name: order.topic.name,
            extraFee: order.topic.extraFee,
          }
        : null,
      promotion: order.promotionCode
        ? {
            id: order.promotionCode.promotion?.id,
            name: order.promotionCode.promotion?.name,
            code: order.promotionCode.code,
            discountValue: order.promotionCode.promotion?.discountValue,
          }
        : null,
      finalImageUrl,
      imageDomain,
      clientName,
      imageStatus,
    };
  }

  async getAppOrderList(
    input: AppOrderListInput,
  ): Promise<AppOrderListResponse> {
    const {
      search,
      clientId,
      startDate,
      endDate,
      page = 1,
      limit = 10,
    } = input;

    const queryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.machine', 'machine')
      .leftJoinAndSelect('order.topic', 'topic')
      .leftJoinAndSelect('order.promotionCode', 'promotionCode')
      .leftJoinAndSelect('promotionCode.promotion', 'promotion')
      .leftJoinAndSelect('order.client', 'client');

    // Search by order ID
    if (search) {
      queryBuilder.andWhere(
        'order.id LIKE :search OR order.orderCode LIKE :search',
        {
          search: `%${search}%`,
        },
      );
    }

    // Filter by client
    if (clientId) {
      queryBuilder.andWhere('order.clientId = :clientId', { clientId });
    }

    // Filter by date range - Convert Unix timestamp strings to Date objects
    const {
      startDate: startDateObj,
      endDate: endDateObj,
      errors,
    } = convertDateRangeFromUnix(startDate, endDate);

    if (errors && errors.length > 0) {
      throw new Error(`Invalid date range: ${errors.join(', ')}`);
    }

    if (startDateObj) {
      queryBuilder.andWhere('order.createdAt >= :startDate', {
        startDate: startDateObj,
      });
    }
    if (endDateObj) {
      queryBuilder.andWhere('order.createdAt <= :endDate', {
        endDate: endDateObj,
      });
    }

    // Order by creation date (newest first)
    queryBuilder.orderBy('order.createdAt', 'DESC');

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Get results and total count
    const [orders, total] = await queryBuilder.getManyAndCount();

    // Transform orders to response format
    const transformedOrders = await Promise.all(
      orders.map(async (order) => {
        const discountAmount =
          Number(order.promotionCode?.promotion?.discountValue) || 0;

        // Get final image URL for each order
        const finalImageUrl = await this.getFinalImageUrl(order.id);

        // Get image status for each order
        const imageStatus = await this.getImageStatus(order.id);

        // Get image domain for each order
        const imageDomain = this.getImageDomain(order.id);

        // Get client name
        const clientName = order.client?.name || 'Unknown';

        return {
          id: order.id,
          orderCode: order.orderCode,
          amount: order.amount,
          totalOrderNumber: order.totalOrderNumber,
          receivedAmount: order.receivedAmount,
          description: order.description,
          refCode: order.refCode,
          imageNumber: order.imageNumber,
          status: order.status,
          paymentMethod: order.paymentMethod,
          captureMode: order.captureMode,
          createdAt: order.createdAt,
          denominations:
            typeof order.denominations === 'string'
              ? order.denominations
              : JSON.stringify(order.denominations),
          discountAmount,
          client: order.client
            ? {
                id: order.client.id,
                name: order.client.name,
                ownerName: order.client.ownerName || null,
              }
            : null,
          machine: order.machine
            ? {
                id: order.machine.id,
                machineCode: order.machine.machineCode,
                location: order.machine.location,
                machineName: order.machine.machineCode,
              }
            : null,
          topic: order.topic
            ? {
                id: order.topic.id,
                name: order.topic.name,
                extraFee: order.topic.extraFee,
              }
            : null,
          promotion: order.promotionCode
            ? {
                id: order.promotionCode.promotion?.id,
                name: order.promotionCode.promotion?.name,
                code: order.promotionCode.code,
                discountValue: order.promotionCode.promotion?.discountValue,
              }
            : null,
          finalImageUrl,
          imageDomain,
          clientName,
          imageStatus,
        };
      }),
    );

    // Calculate summary for all orders (not just current page)
    const summaryQueryBuilder = this.orderRepository
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.promotionCode', 'promotionCode')
      .leftJoinAndSelect('promotionCode.promotion', 'promotion');

    // Apply same filters for summary calculation
    if (search) {
      summaryQueryBuilder.andWhere(
        'order.id LIKE :search OR order.orderCode LIKE :search',
        { search: `%${search}%` },
      );
    }
    if (clientId) {
      summaryQueryBuilder.andWhere('order.clientId = :clientId', { clientId });
    }
    // Use the same converted dates for summary calculation
    if (startDateObj) {
      summaryQueryBuilder.andWhere('order.createdAt >= :startDate', {
        startDate: startDateObj,
      });
    }
    if (endDateObj) {
      summaryQueryBuilder.andWhere('order.createdAt <= :endDate', {
        endDate: endDateObj,
      });
    }

    const allFilteredOrders = await summaryQueryBuilder.getMany();

    // Calculate summary
    const summary: AppOrderSummary = {
      totalOrders: allFilteredOrders.length,
      totalRevenue: allFilteredOrders.reduce((sum, order) => {
        const amount = Number(order.amount) || 0;
        return sum + amount;
      }, 0),
      totalReceivedAmount: allFilteredOrders.reduce((sum, order) => {
        const receivedAmount = Number(order.receivedAmount) || 0;
        return sum + receivedAmount;
      }, 0),
      totalDiscountAmount: allFilteredOrders.reduce((sum, order) => {
        const discount =
          Number(order.promotionCode?.promotion?.discountValue) || 0;
        return sum + discount;
      }, 0),
    };

    const totalPages = Math.ceil(total / limit);

    return {
      orders: transformedOrders,
      total,
      page,
      limit,
      totalPages,
      summary,
    };
  }

  private async getFinalImageUrl(orderId: string): Promise<string | null> {
    const finalImage = await this.appImageRepository.findOne({
      where: {
        orderId,
        fileType: EImageFileType.IMAGE_FINAL,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    return finalImage?.fileUrl || null;
  }

  private async getImageStatus(orderId: string): Promise<ImageStatusInfo> {
    const images = await this.appImageRepository.find({
      where: { orderId },
    });

    const videoCount = images.filter(
      (img) => img.fileType === EImageFileType.VIDEO,
    ).length;
    const mergedImageCount = images.filter(
      (img) => img.fileType === EImageFileType.IMAGE_FINAL,
    ).length;
    const regularImageCount = images.filter(
      (img) => img.fileType === EImageFileType.IMAGE,
    ).length;

    const hasVideo = videoCount > 0;
    const hasMergedImage = mergedImageCount > 0;
    const hasRegularImages = regularImageCount > 0;
    const isComplete = hasVideo && hasMergedImage && hasRegularImages;

    return {
      hasVideo,
      hasMergedImage,
      hasRegularImages,
      totalImages: images.length,
      videoCount,
      mergedImageCount,
      regularImageCount,
      isComplete,
    };
  }

  private getImageDomain(orderId: string): string {
    const memoryDomain =
      process.env.MEMORY_DOMAIN || 'https://memory.snapbox.vn/';
    return `${memoryDomain}${orderId}`;
  }
}
