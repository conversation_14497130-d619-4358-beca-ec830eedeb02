import { Field, ObjectType, InputType, Int } from '@nestjs/graphql';

@ObjectType()
export class ClientInfo {
  @Field()
  id: string;

  @Field()
  name: string;

  @Field({ nullable: true })
  ownerName?: string;
}

@ObjectType()
export class MachineInfo {
  @Field()
  id: string;

  @Field()
  machineCode: string;

  @Field({ nullable: true })
  location?: string;

  @Field({ nullable: true })
  machineName?: string;
}

@ObjectType()
export class TopicInfo {
  @Field()
  id: string;

  @Field()
  name: string;

  @Field({ nullable: true })
  extraFee?: number;
}

@ObjectType()
export class PromotionInfo {
  @Field({ nullable: true })
  id?: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  code?: string;

  @Field({ nullable: true })
  discountValue?: number;
}

@ObjectType()
export class AppOrderSummary {
  @Field()
  totalOrders: number;

  @Field()
  totalRevenue: number;

  @Field()
  totalReceivedAmount: number;

  @Field()
  totalDiscountAmount: number;
}

@ObjectType()
export class ImageStatusInfo {
  @Field()
  hasVideo: boolean;

  @Field()
  hasMergedImage: boolean;

  @Field()
  hasRegularImages: boolean;

  @Field()
  totalImages: number;

  @Field()
  videoCount: number;

  @Field()
  mergedImageCount: number;

  @Field()
  regularImageCount: number;

  @Field()
  isComplete: boolean; // true if has at least 1 video, 1 merged image, and 1+ regular images
}

@ObjectType()
export class AppOrderDetailResponse {
  @Field()
  id: string;

  @Field({ nullable: true })
  orderCode?: string;

  @Field({ nullable: true })
  amount?: number;

  @Field({ nullable: true })
  totalOrderNumber?: number;

  @Field({ nullable: true })
  receivedAmount?: number;

  @Field({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  refCode?: string;

  @Field({ nullable: true })
  imageNumber?: number;

  @Field({ nullable: true })
  status?: string;

  @Field({ nullable: true })
  paymentMethod?: string;

  @Field({ nullable: true })
  captureMode?: string;

  @Field({ nullable: true })
  createdAt?: Date;

  @Field({ nullable: true })
  denominations?: string;

  @Field({ nullable: true })
  discountAmount?: number;

  @Field(() => ClientInfo, { nullable: true })
  client?: ClientInfo;

  @Field(() => MachineInfo, { nullable: true })
  machine?: MachineInfo;

  @Field(() => TopicInfo, { nullable: true })
  topic?: TopicInfo;

  @Field(() => PromotionInfo, { nullable: true })
  promotion?: PromotionInfo;

  @Field({ nullable: true })
  finalImageUrl?: string;

  @Field({ nullable: true })
  imageDomain?: string; // Link ảnh domain

  @Field({ nullable: true })
  clientName?: string; // Tên client

  @Field(() => ImageStatusInfo, { nullable: true })
  imageStatus?: ImageStatusInfo; // Status thể hiện đã có đủ ảnh chưa
}

@InputType()
export class AppOrderListInput {
  @Field({ nullable: true })
  search?: string; // Search by order ID

  @Field({ nullable: true })
  clientId?: string; // Filter by client

  @Field({ nullable: true })
  startDate?: string; // Unix timestamp string - Filter by creation date range

  @Field({ nullable: true })
  endDate?: string; // Unix timestamp string - Filter by creation date range

  @Field(() => Int, { nullable: true, defaultValue: 1 })
  page?: number;

  @Field(() => Int, { nullable: true, defaultValue: 10 })
  limit?: number;
}

@ObjectType()
export class AppOrderListResponse {
  @Field(() => [AppOrderDetailResponse])
  orders: AppOrderDetailResponse[];

  @Field(() => Int)
  total: number;

  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => AppOrderSummary)
  summary: AppOrderSummary;
}
