scalar Upload

input AdminCreateImageInput {
  orderId: String!
  captureMode: ECaptureMode
}

type AdminUploadResponse {
  message: String!
  domain: String!
}

type AdminCreateTimeLapseResponse {
  isSuccess: Boolean!
}

type Mutation {
  adminCreateImage(
    input: AdminCreateImageInput!
    file: Upload
  ): AdminUploadResponse!
  adminCreateTimeLapse(
    input: AdminCreateImageInput!
    file: Upload
  ): AdminCreateTimeLapseResponse!
}
