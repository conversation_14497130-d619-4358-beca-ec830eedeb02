import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppImage } from './image.entity';
import { ImageResolver } from './image.resolver';
import { ImageService } from './image.service';
import { FileService } from 'src/modules/common/module/file/file.service';
import { Order } from '../../clientApp/order/order.entity';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';

@Module({
  imports: [TypeOrmModule.forFeature([AppImage, Order])],
  providers: [JwtService, SBLogger, ImageResolver, ImageService, FileService],
})
export class ImageModule {}
