import * as AdmZip from 'adm-zip';
import {
  Injectable,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppImage } from './image.entity';
import { Order } from '../../clientApp/order/order.entity';
import { AdminCreateImageInput } from './image.input';
import { FileUpload } from 'graphql-upload';
import { FileService } from '../../common/module/file/file.service';
import { EImageFileType } from '../../../enum';

interface UploadResult {
  message: string;
  domain: string;
}

@Injectable()
export class ImageService {
  constructor(
    @InjectRepository(AppImage)
    private readonly imageRepository: Repository<AppImage>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    private readonly fileService: FileService,
  ) {}

  async create(
    input: AdminCreateImageInput,
    file?: FileUpload,
  ): Promise<UploadResult> {
    try {
      await this.validateInput(input, file);
      const orderRecord = await this.getOrderRecord(input.orderId);

      const { createReadStream, mimetype } = file;
      this.validateFileType(mimetype);

      const zipBuffer = await this.streamToBuffer(createReadStream());
      const processedImages = await this.processZipFile(
        zipBuffer,
        orderRecord.id,
      );
      const savedImages = await this.imageRepository.save(processedImages);

      return this.createUploadResult(savedImages.length, input);
    } catch (error) {
      console.log('error', error);

      this.handleError(error);
    }
  }

  async createTimeLapse(
    input: AdminCreateImageInput,
    uploadedFile?: FileUpload,
  ): Promise<{ isSuccess: boolean }> {
    try {
      await this.validateInput(input, uploadedFile);
      const { createReadStream, filename } = uploadedFile;
      const buffer = await this.streamToBuffer(createReadStream());
      const fileType = this.detectTypeImage(filename);
      if (!fileType) {
        throw new BadRequestException(`Unsupported file: ${filename}`);
      }
      const { finalBuffer, finalFileName } = await this.processFile(
        buffer,
        filename,
      );
      const existedImage = await this.imageRepository.findOne({
        where: {
          fileName: finalFileName,
          fileType,
          orderId: input.orderId,
        },
      });
      if (existedImage) return { isSuccess: true };
      const uploadedUrl = await this.fileService.singleUpload(
        finalBuffer,
        finalFileName,
        `appImage/${input.orderId}`,
      );
      const file = this.imageRepository.create({
        fileName: finalFileName,
        fileUrl: uploadedUrl,
        fileType: fileType,
        orderId: input.orderId,
      });
      await this.imageRepository.save(file);
      return { isSuccess: true };
    } catch (error) {
      console.log('error', error);

      this.handleError(error);
    }
  }

  private async validateInput(
    input: AdminCreateImageInput,
    file?: FileUpload,
  ): Promise<void> {
    if (!input?.orderId) {
      throw new BadRequestException('Order ID is required.');
    }
    if (!file) {
      throw new BadRequestException('File is required');
    }
  }

  private async processZipFile(
    buffer: Buffer,
    orderId: string,
  ): Promise<AppImage[]> {
    const zip = new AdmZip(buffer);
    const processedEntries = await Promise.all(
      zip.getEntries().map((entry) => this.processZipEntry(entry, orderId)),
    );

    return processedEntries.filter(
      (entry): entry is AppImage => entry !== null,
    );
  }

  private async processZipEntry(
    entry: AdmZip.IZipEntry,
    orderId: string,
  ): Promise<AppImage | null> {
    if (entry.isDirectory) return null;

    try {
      const fileBuffer = entry.getData();
      const fileName = entry.entryName;

      const fileType = this.detectTypeImage(fileName);
      if (!fileType) {
        console.warn(`Unsupported file: ${fileName}`);
        return null;
      }

      const { finalBuffer, finalFileName } = await this.processFile(
        fileBuffer,
        fileName,
      );
      const existedImage = await this.imageRepository.findOne({
        where: {
          fileName: finalFileName,
          fileType,
          orderId: orderId,
        },
      });
      if (existedImage) return null;
      const uploadedUrl = await this.fileService.singleUpload(
        finalBuffer,
        finalFileName,
        `appImage/${orderId}`,
      );

      return this.imageRepository.create({
        fileName: finalFileName,
        fileUrl: uploadedUrl,
        fileType: fileType,
        orderId: orderId,
      });
    } catch (err) {
      console.error(`Failed to process file: ${entry.entryName}`, err);
      return null;
    }
  }

  private async processFile(
    fileBuffer: Buffer,
    fileName: string,
  ): Promise<{ finalBuffer: Buffer; finalFileName: string }> {
    return {
      finalBuffer: fileBuffer,
      finalFileName: fileName,
    };
  }

  private createUploadResult(
    savedCount: number,
    input: AdminCreateImageInput,
  ): UploadResult {
    return {
      message: `Uploaded and saved ${savedCount} files from ZIP.`,
      domain: `${process.env.MEMORY_DOMAIN}${input.orderId}`,
    };
  }

  private handleError(error: any): never {
    if (error.code === 'ER_DUP_ENTRY') {
      throw new ConflictException('Duplicate entry for image and order.');
    }
    throw error;
  }

  private async getOrderRecord(orderID: string): Promise<Order> {
    const orderRecord = await this.orderRepository.findOne({
      where: { id: orderID },
      relations: ['images'],
    });

    if (!orderRecord) {
      throw new BadRequestException('Order record not found.');
    }

    return orderRecord;
  }

  private streamToBuffer(stream: NodeJS.ReadableStream): Promise<Buffer> {
    return new Promise<Buffer>((resolve, reject) => {
      const chunks: Buffer[] = [];
      stream.on('data', (chunk) => chunks.push(chunk));
      stream.on('end', () => resolve(Buffer.concat(chunks)));
      stream.on('error', reject);
    });
  }

  private validateFileType(mimetype: string): void {
    if (mimetype !== 'application/zip') {
      throw new BadRequestException(
        'Invalid file type. Only ZIP files are allowed.',
      );
    }
  }

  private detectTypeImage(fileName: string): EImageFileType | null {
    const baseName = fileName.split('.')[0].split('/').pop()?.split('.')[0];

    if (!baseName) {
      return null;
    }

    if (baseName.startsWith('flipped_')) {
      return EImageFileType.IMAGE;
    } else if (baseName.includes('merged-image-')) {
      return EImageFileType.IMAGE_FINAL;
    } else if (baseName.startsWith('timelapse_')) {
      return EImageFileType.VIDEO;
    } else {
      return null;
    }
  }
}
