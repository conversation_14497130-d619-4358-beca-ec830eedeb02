import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { ImageService } from './image.service';
import {
  AdminCreateImageInput,
  AdminUploadResponse,
  AdminCreateTimeLapseResponse,
} from './image.input';
import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermissions,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';

@Resolver()
@UseGuards(AuthGuard)
export class ImageResolver {
  constructor(private readonly imageService: ImageService) {}

  @Mutation(() => AdminUploadResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.UPLOAD_ORDER_IMAGES)
  async adminCreateImage(
    @Args('input') input: AdminCreateImageInput,
    @Args({ name: 'file', type: () => GraphQLUpload }) file: FileUpload,
  ): Promise<AdminUploadResponse> {
    return this.imageService.create(input, file);
  }

  @Mutation(() => AdminCreateTimeLapseResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.UPLOAD_ORDER_IMAGES)
  async adminCreateTimeLapse(
    @Args('input') input: AdminCreateImageInput,
    @Args({ name: 'file', type: () => GraphQLUpload }) file: FileUpload,
  ): Promise<AdminCreateTimeLapseResponse> {
    return this.imageService.createTimeLapse(input, file);
  }
}
