import { Field, InputType } from '@nestjs/graphql';
import { ERoles } from 'src/enum/role';

@InputType()
export class CreateUserInput {
  @Field()
  name: string;

  @Field()
  email: string;

  @Field()
  password: string;

  @Field()
  role: ERoles;
}

@InputType()
export class UpdateUserInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  password: string;

  @Field({ nullable: true })
  role: ERoles;
}
