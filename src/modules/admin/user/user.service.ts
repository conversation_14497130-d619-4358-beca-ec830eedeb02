import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CreateUserInput, UpdateUserInput } from './user.input';
import { User } from './user.entity';
import * as bcrypt from 'bcrypt';
import * as moment from 'moment';
import { ERoles } from 'src/enum/role';
import { SBLogger } from 'src/modules/logger/logger.service';

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private loggerService: SBLogger,
  ) {}

  async findAll(): Promise<User[]> {
    const res = await this.userRepository.find({
      order: {
        createdAt: 'DESC',
      },
    });
    return res;
  }

  async findOneById(id: string): Promise<User> {
    const res = this.userRepository.findOneBy({ id });
    return res;
  }

  async create(createUserInput: CreateUserInput): Promise<User> {

    const existing = await this.userRepository.findOneBy({
      email: createUserInput.email,
    });
    if (existing) {
      throw new BadRequestException('Email existing!');
    }

    const hashPassword = await bcrypt.hash(createUserInput.password, 10);
    const user = this.userRepository.create({
      ...createUserInput,
      password: hashPassword,
      createdAt: moment.utc().valueOf().toString(),
      role: createUserInput.role || ERoles.ADMIN,
    });
    return this.userRepository.save(user);
  }

  async update(updateUserInput: UpdateUserInput): Promise<User> {
    const user = await this.userRepository.findOneBy({
      id: updateUserInput.id,
    });
    if (!user) {
      this.loggerService.log('ERROR - user - update', 'User not found');
      throw new NotFoundException('User not found');
    }
    const hashPassword = updateUserInput.password
      ? await bcrypt.hash(updateUserInput.password, 10)
      : updateUserInput.password;
    const res = this.userRepository.save({
      ...user,
      ...updateUserInput,
      email: user.email,
      password: hashPassword,
      updatedAt: moment.utc().valueOf().toString(),
    });
    return res;
  }

  async delete(id: string): Promise<User> {
    const user = await this.userRepository.findOneBy({ id });
    if (!user) {
      this.loggerService.log('ERROR - user - delete', 'User not found');
      throw new NotFoundException('User not found');
    }
    return this.userRepository.remove(user);
  }
}
