type User {
  id: ID!
  name: String!
  email: String!
  role: String!
}

input CreateUserInput {
  name: String!
  email: String!
  password: String!
}

input UpdateUserInput {
  id: ID
  name: String!
  password: String!
}

type Query {
  adminUsers: [User]
  adminUserById(id: ID!): User
}

type Mutation {
  adminCreateUser(input: CreateUserInput): User
  adminUpdateUser(input: UpdateUserInput): User
  adminDeleteUser(id: ID!): User
}
