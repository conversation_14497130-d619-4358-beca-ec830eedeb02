import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import { jwtConstants } from 'src/constants';
import { Repository } from 'typeorm';
import { Auth } from '../../common/entity/auth.entity';
import { SBLogger } from '../../logger/logger.service';
import { User } from '../user/user.entity';
import { SignInInput } from './auth.input';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(Auth)
    private authRepository: Repository<Auth>,
    @InjectRepository(User)
    private usersService: Repository<User>,
    private jwtService: JwtService,
    private loggerService: SBLogger,
  ) {}

  getToken(ctx: any): string {
    let token = '';
    const authHeader = ctx.req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }
    return token;
  }

  async getCurrentUser(token: string): Promise<User> {
    try {
      const decoded = await this.jwtService.verifyAsync(token, jwtConstants);
      return decoded;
    } catch (_err) {
      throw new UnauthorizedException();
    }
  }

  async comparePassword(plainTextPassword, hashedPassword) {
    const isMatch = await bcrypt.compare(plainTextPassword, hashedPassword);
    return isMatch;
  }

  async signIn(request: SignInInput): Promise<Auth> {
    try {
      if (!request.email || !request.password) {
        throw new BadRequestException();
      }

      const user = await this.usersService.findOneBy({ email: request.email });
      if (!user) throw new UnauthorizedException();
      const isValidPassword = await this.comparePassword(
        request.password,
        user.password,
      );
      if (!user || !isValidPassword) throw new UnauthorizedException();
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { password, ...result } = user;
      // Đảm bảo role được lưu đúng định dạng từ enum
      const userRole = user.role;
      const token = await this.jwtService.signAsync(
        {
          ...result,
          role: userRole, // Giữ nguyên role từ database
        },
        jwtConstants,
      );
      return { ...result, token, role: userRole };
    } catch (error) {
      this.loggerService.log('ERROR - signIn - error', error);
      throw new UnauthorizedException(error.message);
    }
  }

  async signOut(token: string): Promise<Auth> {
    const auth = await this.authRepository.findOneBy({ token });
    if (!auth) {
      this.loggerService.log('ERROR - signOut', auth);
      throw new NotFoundException();
    }
    const res = await this.authRepository.remove(auth);
    return res;
  }
}
