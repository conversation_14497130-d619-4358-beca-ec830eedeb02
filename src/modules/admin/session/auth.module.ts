import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '../user/user.entity';
import { AuthService } from './auth.service';
import { AuthResolver } from './auth.resolver';
import { Auth } from '../../common/entity/auth.entity';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Client } from '../../common/entity/client.entity';

@Module({
  imports: [TypeOrmModule.forFeature([User, Auth, Client])],
  providers: [AuthService, JwtService, AuthResolver, SBLogger],
  exports: [AuthService],
})
export class AuthModule {}
