import { Resolver, Query, Args, Context, Mutation } from '@nestjs/graphql';
import { AuthService } from './auth.service';
import { Auth } from '../../common/entity/auth.entity';
import { SignInInput } from './auth.input';
import { ExecutionContext, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';

@Resolver(() => Auth)
export class AuthResolver {
  constructor(private authService: AuthService) {}
  @Mutation(() => Auth)
  async adminSignIn(@Args('input') input: SignInInput): Promise<Auth> {
    return this.authService.signIn(input);
  }

  @Query(() => Auth)
  @UseGuards(AuthGuard)
  async adminSignOut(@Context() ctx: ExecutionContext): Promise<Auth> {
    const token = await this.authService.getToken(ctx);
    return this.authService.signOut(token);
  }
}
