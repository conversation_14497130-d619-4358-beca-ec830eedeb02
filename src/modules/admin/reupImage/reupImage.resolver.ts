import { Resolver, Mutation, Context, Args } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { AdminReupImageService } from './reupImage.service';
import { AdminReupImageInput, AdminReupImageResponse } from './reupImage.input';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermission,
  ModPermissions,
} from 'src/decorators/modPermissions.decorator';

@Resolver()
@UseGuards(AuthGuard)
export class AdminReupImageResolver {
  constructor(private readonly reupImageService: AdminReupImageService) {}

  @Mutation(() => AdminReupImageResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.UPLOAD_ORDER_IMAGES)
  async adminReupImage(
    @Args('input') input: AdminReupImageInput,
    @Context() context: any,
  ): Promise<AdminReupImageResponse> {
    return await this.reupImageService.reupImage(
      input.machineId,
      input.orderId,
      context.req.user.id,
    );
  }
}
