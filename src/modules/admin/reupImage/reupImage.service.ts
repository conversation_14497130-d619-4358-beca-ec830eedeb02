import { Injectable } from '@nestjs/common';
import { ReupImageCommonService } from 'src/modules/common/service/reupImage.common.service';

@Injectable()
export class AdminReupImageService {
  constructor(
    private readonly reupImageCommonService: ReupImageCommonService,
  ) {}

  /**
   * Process a request to reupload images for an order (admin version)
   * @param machineId ID of the machine
   * @param orderId ID of the order
   * @param adminId ID of the admin making the request
   * @returns Message indicating the result of the operation
   */
  async reupImage(
    machineId: string,
    orderId: string,
    adminId: string,
  ): Promise<{ message: string }> {
    // Delegate to common service with isAdmin=true
    return this.reupImageCommonService.processReuploadRequest(
      machineId,
      orderId,
      adminId,
      true, // This is an admin request
    );
  }
}
