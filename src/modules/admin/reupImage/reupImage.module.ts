import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminReupImageService } from './reupImage.service';
import { AdminReupImageResolver } from './reupImage.resolver';
import { Machine } from '../../common/entity/machine.entity';
import { Order } from 'src/modules/clientApp/order/order.entity';
import { AppImage } from 'src/modules/clientApp/uploadImage/image.entity';
import { Frame } from 'src/modules/common/entity/frame.entity';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { SBLogger } from 'src/modules/logger/logger.service';
import { JwtService } from '@nestjs/jwt';
import { ReupImageCommonService } from 'src/modules/common/service/reupImage.common.service';

@Module({
  imports: [TypeOrmModule.forFeature([Machine, Order, AppImage, Frame])],
  providers: [
    JwtService,
    AdminReupImageService,
    AdminReupImageResolver,
    FirestoreService,
    SBLogger,
    ReupImageCommonService,
  ],
  exports: [AdminReupImageService],
})
export class AdminReupImageModule {}
