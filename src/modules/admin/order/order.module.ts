import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { Order } from './order.entity';
import { OrderService } from './order.service';
import { OrderResolver } from './order.resolver';
import { MachineService } from '../machine/machine.service';
import { User } from '../user/user.entity';
import { Machine } from '../../common/entity/machine.entity';
import { MachineBrand } from '../machineBrand/machineBrand.entity';
import { Client } from '../../common/entity/client.entity';
import { SBLogger } from 'src/modules/logger/logger.service';
import { PrintSetting } from 'src/modules/common/entity/printSetting.entity';
import { MachineStatus } from 'src/modules/common/entity/machineStatus.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Order,
      User,
      Client,
      Machine,
      MachineBrand,
      PrintSetting,
      MachineStatus
    ]),
  ],
  providers: [
    JwtService,
    SBLogger,
    OrderService,
    OrderResolver,
    MachineService,
  ],
  exports: [OrderService],
})
export class OrderModule {}
