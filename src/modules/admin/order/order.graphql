type Order {
  id: ID!
  orderId: String!
  userId: String!
  paymentIds: String!
  machineIds: Boolean!
  discount: Int!
  totalAmount: Int!
  advancePayment: Int!
  appointedDate: String!
  status: String!
  editContent: String!
}

type User {
  id: ID!
  name: String!
  email: String!
}
type MachineBrand {
  id: ID!
  brandCode: String!
  name: String!
  description: String!
}
type MachineResponse {
  id: ID!
  machineBrand: MachineBrand!
  machineCode: String!
  havePayScreen: Boolean!
  status: String!
}
type OrderResponse {
  id: ID!
  orderId: String!
  user: User!
  paymentIds: String!
  machines: [MachineResponse]!
  discount: Int!
  totalAmount: Int!
  advancePayment: Int!
  appointedDate: String!
  status: String!
  editContent: String!
}

type Query {
  adminOrderById(id: ID!): OrderResponse
}

type Mutation {
  adminOrders(input: GetOrderInput): [OrderResponse]
  adminCreateOrder(input: CreateOrderInput): OrderResponse
  adminUpdateOrder(input: UpdateOrderInput): OrderResponse
  adminDeleteOrder(id: ID!): Order
}
