import { Field, ObjectType } from '@nestjs/graphql';
import { EOrderStatus } from '../../../enum';
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity()
@ObjectType()
export class Order {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column()
  @Field()
  orderId: string;

  @Column()
  @Field()
  userId: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  paymentIds: number;

  @Column({ length: 10000 })
  @Field()
  machineIds: string;

  @Column({ default: 0 })
  @Field()
  discount: number;

  @Column({ default: 0 })
  @Field()
  totalAmount: number;

  @Column({ default: 0 })
  @Field()
  advancePayment: number;

  @Column()
  @Field()
  appointedDate: string;

  @Column({ default: EOrderStatus.IN_PROGRESS })
  @Field()
  status: string;

  @Column({ type: 'longtext', nullable: true })
  @Field({ nullable: true })
  editContent: number;

  @Column()
  @Field()
  createdAt: string;

  @Column()
  @Field()
  createdBy: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  updatedAt: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  updatedBy: string;
}
