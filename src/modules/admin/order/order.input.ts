import { Field, InputType, ObjectType } from '@nestjs/graphql';
import { EOrderStatus } from 'src/enum';
import { MachineResponse } from '../../common/entity/machine.entity';
import { Client } from '../../common/entity/client.entity';

@InputType()
export class CreateOrderInput {
  @Field()
  userId: string;

  @Field()
  appointedDate: string;

  @Field()
  machineIds?: string;

  @Field()
  discount: number;
}

@InputType()
export class UpdateOrderInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  appointedDate: string;

  @Field({ nullable: true })
  discount: number;

  @Field({ nullable: true })
  status: EOrderStatus;
}

@InputType()
export class GetOrderInput {
  @Field({ nullable: true })
  orderId: string;

  @Field({ nullable: true })
  status?: EOrderStatus;

  @Field({ nullable: true })
  userId: string;
}

@ObjectType()
export class OrderResponse {
  @Field()
  id: string;

  @Field()
  orderId: string;

  @Field()
  user: Client;

  @Field({ nullable: true })
  paymentIds: string;

  @Field(() => [MachineResponse])
  machines: MachineResponse[];

  @Field()
  discount: number;

  @Field()
  totalAmount: number;

  @Field()
  advancePayment: number;

  @Field()
  appointedDate: string;

  @Field()
  status: string;

  @Field({ nullable: true })
  editContent: string;
}
