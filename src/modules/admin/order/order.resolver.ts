import { Resolver, Mutation, Query, Args, Context, ID } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { Order } from './order.entity';
import { OrderService } from './order.service';
import { TOrderResponse } from './order.type';
import {
  CreateOrderInput,
  GetOrderInput,
  OrderResponse,
  UpdateOrderInput,
} from './order.input';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermissions,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';

@Resolver(() => Order)
@UseGuards(AuthGuard)
export class OrderResolver {
  constructor(private orderService: OrderService) {}

  @Mutation(() => [OrderResponse])
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_ORDERS)
  async adminOrders(
    @Args('input') input: GetOrderInput,
  ): Promise<TOrderResponse[]> {
    return this.orderService.getOrders(input);
  }

  @Query(() => OrderResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_ORDERS)
  async adminOrderById(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<TOrderResponse> {
    return this.orderService.findOneById(id);
  }

  @Mutation(() => OrderResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminCreateOrder(
    @Args('input') input: CreateOrderInput,
    @Context() context: any,
  ): Promise<TOrderResponse> {
    return this.orderService.create(input, context.req.user);
  }

  @Mutation(() => OrderResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminUpdateOrder(
    @Args('input') input: UpdateOrderInput,
    @Context() context: any,
  ): Promise<TOrderResponse> {
    return this.orderService.update(input, context.req.user);
  }

  @Mutation(() => Order)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminDeleteOrder(
    @Args('id', { type: () => ID }) id: string,
    @Context() context: any,
  ): Promise<Order> {
    return this.orderService.delete(id, context.req.user);
  }
}
