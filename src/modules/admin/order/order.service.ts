import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  CreateOrderInput,
  GetOrderInput,
  UpdateOrderInput,
} from './order.input';
import { Order } from './order.entity';
import * as moment from 'moment';
import { SBLogger } from '../../logger/logger.service';
import { User } from '../user/user.entity';
import { Machine } from '../../common/entity/machine.entity';
import { TOrderResponse } from './order.type';
import { MachineService } from '../machine/machine.service';
import { Client } from '../../common/entity/client.entity';
import { TMachineResponse } from '../machine/machine.type';
import { EOrderStatus, EProductionStatus } from 'src/enum';

@Injectable()
export class OrderService {
  constructor(
    @InjectRepository(Order)
    private orderRepository: Repository<Order>,

    @InjectRepository(Machine)
    private machineRepository: Repository<Machine>,
    @InjectRepository(Client)
    private clientRepository: Repository<Client>,

    private machineService: MachineService,
    private loggerService: SBLogger,
  ) {}

  async checkMachines(ids: string[]) {
    const machines = await this.machineService.findByIds(ids);
    const notAvailableMachine = machines.filter((el) => !el.orderId);
    if (notAvailableMachine.length < ids.length) {
      this.loggerService.log(
        'ERROR - order - checkMachines',
        'Some of machines not available',
      );
      throw new BadRequestException('Some of machines not available');
    }
    return machines;
  }
  async checkUser(id: string) {
    const tmp = await this.clientRepository.findOneBy({ id });
    if (!tmp) {
      this.loggerService.log('ERROR - order - checkUser', 'User Invalid!');
      throw new BadRequestException('User Invalid!');
    }
    return tmp;
  }

  updateMachinesWithOrder(
    machines: TMachineResponse[],
    order: TOrderResponse,
    isRemove: boolean,
    user: User,
  ) {
    machines.forEach((el) => {
      const updated = {
        ...el,
        orderId: isRemove ? null : order.orderId,
        userId: isRemove ? null : order.userId,
      };
      this.loggerService.log(
        'START - order - updateMachinesWithOrder',
        updated,
      );
      this.machineService.update(updated, user);
    });
  }

  async findAll(): Promise<Order[]> {
    const res = this.orderRepository.find();
    return res;
  }

  async findOneById(id: string): Promise<TOrderResponse> {
    const result = await this.orderRepository.findOneBy({ id });

    const machineIds = result.machineIds.split(',');
    const machines = await this.machineService.findByIds(machineIds);
    const userInformation = await this.clientRepository.findOneBy({
      id: result.userId,
    });

    const convertRes = {
      ...result,
      user: userInformation,
      machines,
    };
    return convertRes;
  }

  async create(
    createOrderInput: CreateOrderInput,
    user: User,
  ): Promise<TOrderResponse> {
    const createdAt = moment.utc().valueOf().toString();
    const orderId = `SNAPBOX${createdAt}`;
    const machineIds = createOrderInput.machineIds.split(',');

    const machines = await this.checkMachines(machineIds);
    const userInformation = await this.checkUser(createOrderInput.userId);

    const totalAmount = machines.reduce(
      (total, machine) => total + machine.price,
      0,
    );

    const tmp = this.orderRepository.create({
      ...createOrderInput,
      orderId,
      createdAt,
      totalAmount,
      createdBy: user.id.toString(),
    });
    const result = await this.orderRepository.save(tmp);

    const deliveredMachinesCount = machines.filter(
      (machine) => machine.productionStatus === EProductionStatus.DELIVERED,
    ).length;

    if (deliveredMachinesCount > 0) {
      await this.incrementClientTotalMachines(
        createOrderInput.userId,
        deliveredMachinesCount,
      );
    }

    // await this.incrementClientTotalOrders(createOrderInput.userId);

    const convertResult = {
      ...result,
      user: userInformation,
      machines,
    };

    this.updateMachinesWithOrder(machines, convertResult, false, user);
    return convertResult;
  }

  async update(
    updateOrderInput: UpdateOrderInput,
    user: User,
  ): Promise<TOrderResponse> {
    const existingOrder = await this.orderRepository.findOneBy({
      id: updateOrderInput.id,
    });
    if (!existingOrder) {
      this.loggerService.log('ERROR - order - update', 'Order not found');
      throw new NotFoundException('Order not found');
    }

    if (
      updateOrderInput.status &&
      !Object.values(EOrderStatus).includes(updateOrderInput.status)
    ) {
      throw new BadRequestException(
        `Invalid status provided. Allowed values: ${Object.values(
          EOrderStatus,
        ).join(', ')}`,
      );
    }

    const updatedOrder = {
      ...existingOrder,
      ...updateOrderInput,
      updatedAt: moment.utc().valueOf().toString(),
      updatedBy: user.id.toString(),
    };

    const result = await this.orderRepository.save(updatedOrder);
    await this.handleClientUpdates(existingOrder, updatedOrder);

    const convertedResult = await this.convertRef([result]);
    return convertedResult[0];
  }

  async delete(id: string, user: User): Promise<Order> {
    const tmp = await this.orderRepository.findOneBy({ id });
    if (!tmp) {
      this.loggerService.log('ERROR - order - delete', 'Order not found');
      throw new NotFoundException('Order not found');
    }
    if (tmp.paymentIds) {
      this.loggerService.log('ERROR - order - delete', 'Order have been paid!');
      throw new BadRequestException('Order have been paid!');
    }

    const machines = await this.machineService.findByIds(
      tmp.machineIds.split(','),
    );
    const res = this.orderRepository.remove(tmp);
    this.updateMachinesWithOrder(machines, tmp, true, user);
    return res;
  }

  async convertRef(result: Order[]): Promise<TOrderResponse[]> {
    let tmp = await this.mappingProp(
      result,
      'userId',
      'user',
      this.clientRepository,
    );
    tmp = await this.mappingMachine(tmp);
    return tmp;
  }
  async mappingMachine(clients): Promise<TOrderResponse[]> {
    const idsString = clients.map((el) => el.machineIds);
    const ids = idsString.join(',').split(',');
    if (ids.length === 0) return clients;

    const mapping = {};
    (await this.machineService.findByIds(ids)).map((el) => {
      mapping[el.id] = el;
    });
    return clients.map((el) => {
      const machineIds = el.machineIds.split(',');
      const machines = machineIds.map((el) => mapping[el]);
      return {
        ...el,
        machines,
      };
    });
  }
  async mappingProp(
    clients,
    prop,
    targetProp,
    repository,
  ): Promise<TOrderResponse[]> {
    const ids = clients.map((el) => el[prop]);
    if (ids.length === 0) return clients;

    const mapping = {};
    (await repository.findByIds(ids)).map((el) => {
      mapping[el.id] = el;
    });
    return clients.map((el) => ({
      ...el,
      [targetProp]: mapping[el[prop]],
    }));
  }

  private async handleClientUpdates(
    previousOrder: Order,
    updatedOrder: Order,
  ): Promise<void> {
    const isCompletedNow = updatedOrder.status === 'COMPLETED';
    const wasCompletedBefore = previousOrder.status === 'COMPLETED';

    if (updatedOrder.userId && isCompletedNow !== wasCompletedBefore) {
      const client = await this.clientRepository.findOneBy({
        id: updatedOrder.userId,
      });

      if (client) {
        client.totalOrders = client.totalOrders || 0;

        if (isCompletedNow) {
          client.totalOrders += 1;
        } else if (wasCompletedBefore) {
          client.totalOrders = Math.max(client.totalOrders - 1, 0);
        }

        await this.clientRepository.save(client);
      }
    }
  }

  private async incrementClientTotalOrders(clientId: string): Promise<void> {
    const client = await this.clientRepository.findOneBy({ id: clientId });
    if (!client) {
      this.loggerService.log(
        'ERROR - incrementClientTotalOrders',
        'Client not found',
      );
      throw new NotFoundException('Client not found');
    }

    client.totalOrders = (client.totalOrders || 0) + 1;

    await this.clientRepository.save(client);
  }

  private async incrementClientTotalMachines(
    clientId: string,
    count: number,
  ): Promise<void> {
    const client = await this.clientRepository.findOneBy({ id: clientId });
    if (!client) {
      this.loggerService.log(
        'ERROR - incrementClientTotalMachines',
        'Client not found',
      );
      throw new NotFoundException('Client not found');
    }

    client.totalMachines = (client.totalMachines || 0) + count;
    await this.clientRepository.save(client);
  }

  async getOrders(input: GetOrderInput): Promise<TOrderResponse[]> {
    const clients = await this.orderRepository.find({
      where: input,
      order: {
        createdAt: 'DESC',
      },
    });

    const result = await this.convertRef(clients);
    return result;
  }
}
