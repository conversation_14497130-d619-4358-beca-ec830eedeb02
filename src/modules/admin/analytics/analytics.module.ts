import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SBLogger } from '../../logger/logger.service';
import { AnalyticsService } from './analytics.service';
import { AnalyticsResolver } from './analytics.resolver';
import { Order } from 'src/modules/clientApp/order/order.entity';
import { JwtService } from '@nestjs/jwt';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { Topic } from 'src/modules/common/entity/topic.entity';
import { PromotionCode } from 'src/modules/common/entity/promotionCode.entity';
import { SettingSize } from 'src/modules/common/entity/settingSize.entity';
import { Frame } from 'src/modules/common/entity/frame.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Order,
      Machine,
      Topic,
      PromotionCode,
      SettingSize,
      Frame,
    ]),
  ],
  providers: [AnalyticsService, AnalyticsResolver, SBLogger, JwtService],
  exports: [],
})
export class AnalyticsModule {}
