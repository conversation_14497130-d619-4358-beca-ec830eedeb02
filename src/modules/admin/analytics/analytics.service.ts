import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, In, Repository } from 'typeorm';
import { SBLogger } from '../../logger/logger.service';
import { Order } from 'src/modules/clientApp/order/order.entity';
import {
  AdminAnalyticsOrdersDataResponse,
  AdminAnalyticsResponse,
  AdminRevenueChartResponse,
} from './analytics.input';
import { EPaymentMethod } from 'src/enum';
import * as moment from 'moment-timezone';

@Injectable()
export class AnalyticsService {
  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    private readonly logger: SBLogger,
  ) {}

  async revenue(
    startDate: string,
    endDate: string,
    clientId: string,
  ): Promise<AdminAnalyticsResponse> {
    try {
      const start = moment
        .unix(parseInt(startDate))
        .tz('Asia/Ho_Chi_Minh')
        .startOf('day');
      const end = moment
        .unix(parseInt(endDate))
        .tz('Asia/Ho_Chi_Minh')
        .endOf('day');
      // Tính toán khoảng thời gian
      const duration = this.calculateDuration(start, end);
      const previousPeriod = this.calculatePreviousPeriod(start, end, duration);
      // Lấy đơn hàng hiện tại và kỳ trước
      const [orders, previousOrders] = await this.fetchOrdersForPeriods(
        clientId,
        start,
        end,
        previousPeriod.start,
        previousPeriod.end,
      );

      // Tính toán các thông số
      const totalReceivedAmount = this.calculateTotalAmount(orders);
      const totalReceivedAmountPrevious =
        this.calculateTotalAmount(previousOrders);

      const growthPercentage = this.calculateGrowth(
        totalReceivedAmount,
        totalReceivedAmountPrevious,
      );
      const growthOrders = this.calculateGrowth(
        orders.length,
        previousOrders.length,
      );

      // Tính toán theo phương thức thanh toán
      const totalBankTranfer = this.calculateTotalByPaymentMethod(
        orders,
        EPaymentMethod.ONLINE,
      );
      const totalCash = this.calculateTotalByPaymentMethod(
        orders,
        EPaymentMethod.OFFLINE,
      );

      // Trả về kết quả
      return this.buildAnalyticsResponse({
        totalReceivedAmount,
        totalReceivedAmountPrevious,
        growthPercentage,
        totalBankTranfer,
        totalCash,
        totalOrders: orders.length,
        totalPreviousOrders: previousOrders.length,
        growthOrders,
      });
    } catch (error) {
      this.handleError(error);
    }
  }

  async revenueChart(
    startDate: string,
    endDate: string,
    clientId: string,
  ): Promise<AdminRevenueChartResponse> {
    try {
      const start = moment
        .unix(parseInt(startDate))
        .tz('Asia/Ho_Chi_Minh')
        .startOf('day');
      const end = moment
        .unix(parseInt(endDate))
        .tz('Asia/Ho_Chi_Minh')
        .endOf('day');
      const isSameDay = moment(start.format('YYYY-MM-DD')).isSame(
        end.format('YYYY-MM-DD'),
        'day',
      );

      // Tính toán khoảng thời gian
      const duration = this.calculateDuration(start, end);
      const previousPeriod = this.calculatePreviousPeriod(start, end, duration);

      const currentResults = await this.getRevenueData(
        start,
        end,
        clientId,
        isSameDay,
      );
      const previousResults = await this.getRevenueData(
        previousPeriod.start,
        previousPeriod.end,
        clientId,
        isSameDay,
      );
      return {
        current: currentResults.map((result) => ({
          time: result.group,
          value: parseInt(result.totalOrders),
          revenue: parseInt(result.totalAmount),
        })),
        previous: previousResults.map((result) => ({
          time: result.group,
          value: parseInt(result.totalOrders),
          revenue: parseInt(result.totalAmount),
        })),
      };
    } catch (error) {
      this.handleError(error);
    }
  }

  private async getRevenueData(
    startDate: moment.Moment,
    endDate: moment.Moment,
    clientId: string,
    isSameDay: boolean,
  ): Promise<any[]> {
    const queryBuilder = this.orderRepository.createQueryBuilder('order');

    queryBuilder.select(
      isSameDay
        ? "DATE_FORMAT(CONVERT_TZ(order.createdAt, '+00:00', '+07:00'), '%H:00') as `group`"
        : "DATE_FORMAT(CONVERT_TZ(order.createdAt, '+00:00', '+07:00'), '%Y-%m-%d') as `group`",
    );
    queryBuilder.addSelect('SUM(order.totalOrderNumber)', 'totalAmount');
    queryBuilder.addSelect('COUNT(order.id)', 'totalOrders');
    queryBuilder.where('order.clientId = :clientId', { clientId });
    queryBuilder.andWhere(
      'CONVERT_TZ(order.createdAt, "+00:00", "+07:00") BETWEEN :start AND :end',
      {
        start: startDate.format('YYYY-MM-DD HH:mm:ss'),
        end: endDate.format('YYYY-MM-DD HH:mm:ss'),
      },
    );
    queryBuilder.groupBy('`group`');
    queryBuilder.orderBy('`group`', 'ASC');

    return queryBuilder.getRawMany();
  }

  async orders(
    startDate: string,
    endDate: string,
    params: any,
    clientId: string,
  ): Promise<AdminAnalyticsOrdersDataResponse> {
    const {
      paymentType,
      page = 1,
      limit = 10,
      frameIds,
      topicIds,
      machineIds,
    } = params;

    const start = new Date(Number(startDate) * 1000); // Chuyển từ UNIX timestamp sang Date
    const end = new Date(Number(endDate) * 1000);

    const whereConditions: any = {
      createdAt: Between(start, end),
      clientId,
    };

    if (paymentType) {
      whereConditions.paymentMethod = paymentType;
    }

    if (frameIds && frameIds.length > 0) {
      whereConditions.frameId = In(frameIds);
    }

    if (topicIds && topicIds.length > 0) {
      whereConditions.topicId = In(topicIds);
    }

    if (machineIds && machineIds.length > 0) {
      whereConditions.machineId = In(machineIds);
    }

    // Sử dụng repository để thực hiện truy vấn
    const [orders, total] = await this.orderRepository.findAndCount({
      where: whereConditions,
      relations: [
        'promotionCode',
        'promotionCode.promotion',
        'frame',
        'topic',
        'settingSize',
        'machine',
        'images',
      ], // Liên kết các bảng liên quan
      skip: (page - 1) * limit,
      withDeleted: true,
      relationLoadStrategy: 'query',
      take: limit,
      order: { createdAt: 'DESC' },
    });

    const totalSum = await this.orderRepository
      .createQueryBuilder('order')
      .select('SUM(order.totalOrderNumber)', 'sum')
      .addSelect('SUM(order.receivedAmount)', 'totalRecievedAmount')
      .addSelect('SUM(order.amount)', 'totalAmount')
      .where(whereConditions)
      .getRawOne();

    const totalOrderSum = totalSum?.sum || 0;
    const totalRecievedAmount = totalSum?.totalRecievedAmount || 0;
    const totalAmount = totalSum?.totalAmount || 0;
    const ordersWithDomain = orders.map((order) => ({
      ...order,
      domain: `${process.env.MEMORY_DOMAIN}${order.id}`,
    }));

    return {
      data: ordersWithDomain,
      total,
      currentPage: page,
      pageSize: limit,
      totalOrderNumber: totalOrderSum,
      totalRecievedAmount: totalRecievedAmount,
      totalAmount,
    };
  }

  private calculateDuration(start: moment.Moment, end: moment.Moment): number {
    return end.diff(start, 'day') + 1;
  }

  private calculatePreviousPeriod(
    start: moment.Moment,
    end: moment.Moment,
    duration: number,
  ): { start: moment.Moment; end: moment.Moment } {
    return {
      start: start.clone().subtract(duration, 'day'),
      end: end.clone().subtract(duration, 'day'),
    };
  }

  private async fetchOrdersForPeriods(
    clientId: string,
    start: moment.Moment,
    end: moment.Moment,
    previousStart: moment.Moment,
    previousEnd: moment.Moment,
  ): Promise<[Order[], Order[]]> {
    const fetchOrders = async (start: moment.Moment, end: moment.Moment) => {
      return this.orderRepository
        .createQueryBuilder('order')
        .where('order.clientId = :clientId', { clientId })
        .andWhere(
          'CONVERT_TZ(order.createdAt, "+00:00", "+07:00") BETWEEN :startDate AND :endDate',
          {
            startDate: start.format('YYYY-MM-DD HH:mm:ss'),
            endDate: end.format('YYYY-MM-DD HH:mm:ss'),
          },
        )
        .getMany();
    };

    return Promise.all([
      fetchOrders(start, end),
      fetchOrders(previousStart, previousEnd),
    ]);
  }

  private calculateTotalAmount(orders: Order[]): number {
    return orders.reduce(
      (total, order) => total + (Number(order.totalOrderNumber) || 0),
      0,
    );
  }

  private calculateGrowth(current: number, previous: number): number {
    return previous !== 0 ? ((current - previous) / previous) * 100 : 100;
  }

  private calculateTotalByPaymentMethod(
    orders: Order[],
    method: EPaymentMethod,
  ): number {
    return orders
      .filter((order) => order.paymentMethod === method)
      .reduce((total, order) => total + (Number(order.receivedAmount) || 0), 0);
  }

  private buildAnalyticsResponse(
    data: Partial<AdminAnalyticsResponse>,
  ): AdminAnalyticsResponse {
    return {
      totalReceivedAmount: data.totalReceivedAmount || 0,
      totalReceivedAmountPrevious: data.totalReceivedAmountPrevious || 0,
      growthPercentage: data.growthPercentage || 0,
      totalBankTranfer: data.totalBankTranfer || 0,
      totalCash: data.totalCash || 0,
      totalOrders: data.totalOrders || 0,
      totalPreviousOrders: data.totalPreviousOrders || 0,
      growthOrders: data.growthOrders || 0,
    };
  }

  private handleError(error: any): void {
    console.error('Error: ', error);
    throw error;
  }
}
