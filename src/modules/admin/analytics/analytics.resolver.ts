import { Resolver, Query, Args } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { AnalyticsService } from './analytics.service';
import {
  AdminAnalyticsOrdersDataResponse,
  AdminAnalyticsResponse,
  AdminParamsInput,
  AdminRevenueChartResponse,
} from './analytics.input';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermissions,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';

@Resolver('Analytics')
@UseGuards(AuthGuard)
export class AnalyticsResolver {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Query(() => AdminAnalyticsResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_DETAILS)
  async adminClientRevenue(
    @Args('clientId') clientId: string,
    @Args('startDate') startDate: string,
    @Args('endDate') endDate: string,
  ): Promise<AdminAnalyticsResponse> {
    return this.analyticsService.revenue(startDate, endDate, clientId);
  }

  @Query(() => AdminAnalyticsOrdersDataResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_ORDERS)
  async adminClientAnalyticsOrders(
    @Args('clientId') clientId: string,
    @Args('startDate') startDate: string,
    @Args('endDate') endDate: string,
    @Args('paramsInput', { type: () => AdminParamsInput })
    paramsInput: AdminParamsInput,
  ): Promise<AdminAnalyticsOrdersDataResponse> {
    return this.analyticsService.orders(
      startDate,
      endDate,
      paramsInput,
      clientId,
    );
  }

  @Query(() => AdminRevenueChartResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_DETAILS)
  async adminClientGetRevenueChart(
    @Args('clientId') clientId: string,
    @Args('startDate') startDate: string,
    @Args('endDate') endDate: string,
  ): Promise<AdminRevenueChartResponse> {
    return this.analyticsService.revenueChart(startDate, endDate, clientId);
  }
}
