import { Field, InputType, ObjectType, ID } from '@nestjs/graphql';
import { ECaptureMode, EPaymentMethod } from 'src/enum';
import { PromotionCode } from 'src/modules/common/entity/promotionCode.entity';
import { Frame } from 'src/modules/common/entity/frame.entity';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { SettingSize } from 'src/modules/common/entity/settingSize.entity';
import { Topic } from 'src/modules/common/entity/topic.entity';
import { AppImage } from 'src/modules/clientApp/uploadImage/image.entity';

@ObjectType()
export class AdminAnalyticsResponse {
  @Field({ defaultValue: 0 })
  totalReceivedAmount: number;

  @Field({ defaultValue: 0 })
  totalReceivedAmountPrevious: number;

  @Field({ defaultValue: 0 })
  growthPercentage: number;

  @Field({ defaultValue: 0 })
  totalBankTranfer: number;

  @Field({ defaultValue: 0 })
  totalCash: number;

  @Field({ defaultValue: 0 })
  totalOrders: number;

  @Field({ defaultValue: 0 })
  totalPreviousOrders: number;

  @Field({ defaultValue: 0 })
  growthOrders: number;
}

@InputType()
export class AdminParamsInput {
  @Field(() => String, { nullable: true })
  paymentType?: string;

  @Field(() => [String], { nullable: true })
  frameIds?: string[];

  @Field(() => [String], { nullable: true })
  machineIds?: string[];

  @Field(() => [String], { nullable: true })
  topicIds?: string[];

  @Field(() => Number, { defaultValue: 1 })
  page?: number;

  @Field(() => Number, { defaultValue: 10 })
  limit?: number;
}

@ObjectType()
export class AdminOrderObjectType {
  @Field(() => ID)
  id: string;

  @Field({ nullable: true })
  orderCode: string;

  @Field({ nullable: true })
  amount: number;

  @Field({ nullable: true })
  totalOrderNumber: number;

  @Field({ nullable: true })
  receivedAmount: number;

  @Field({ nullable: true })
  description: string;

  @Field({ nullable: true })
  refCode: string;

  @Field({ nullable: true })
  imageNumber: number;

  @Field({ nullable: true })
  status: string;

  @Field({ nullable: true })
  paymentMethod: EPaymentMethod;

  @Field({ nullable: true })
  captureMode: ECaptureMode;

  @Field({ nullable: true })
  machineId: string;

  @Field({ nullable: true })
  clientId: string;

  @Field({ nullable: true })
  settingSizeId: string;

  @Field({ nullable: true })
  promotionId: string | null;

  @Field({ nullable: true })
  promotionCodeId: string | null;

  @Field({ nullable: true })
  topicId: string | null;

  @Field({ nullable: true })
  frameId: string;

  @Field({ nullable: true })
  createdAt: Date;

  @Field({ nullable: true })
  updatedAt: Date;

  @Field(() => PromotionCode, { nullable: true })
  promotionCode: PromotionCode;

  @Field(() => Frame, { nullable: true })
  frame: Frame;

  @Field(() => Topic, { nullable: true })
  topic: Topic;

  @Field(() => SettingSize, { nullable: true })
  settingSize: SettingSize;

  @Field(() => Machine, { nullable: true })
  machine: Machine;

  @Field(() => [AppImage], { nullable: true })
  images: AppImage[];

  @Field({ nullable: true })
  domain: string;
}

@ObjectType()
export class AdminAnalyticsOrdersDataResponse {
  @Field(() => [AdminOrderObjectType])
  data: AdminOrderObjectType[];

  @Field()
  total: number;

  @Field()
  currentPage: number;

  @Field()
  pageSize: number;

  @Field()
  totalOrderNumber: number;

  @Field()
  totalRecievedAmount: number;

  @Field()
  totalAmount: number;
}

@ObjectType()
export class AdminRevenueChart {
  @Field({ nullable: true })
  time: string;

  @Field({ nullable: true })
  value: number;

  @Field({ nullable: true })
  revenue: number;
}

@ObjectType()
export class AdminRevenueChartResponse {
  @Field(() => [AdminRevenueChart])
  current: AdminRevenueChart[];

  @Field(() => [AdminRevenueChart])
  previous: AdminRevenueChart[];
}
