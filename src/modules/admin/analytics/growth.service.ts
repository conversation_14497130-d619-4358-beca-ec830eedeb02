import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';

@Injectable()
export class GrowthService {
  async calculateGrowthPercentage(
    startDate: string,
    endDate: string,
    getValue: (start: string, end: string) => Promise<number>,
  ): Promise<number> {
    // Chuyển đổi ngày sử dụng dayjs
    const start = dayjs(startDate);
    const end = dayjs(endDate);

    if (!start.isValid() || !end.isValid()) {
      throw new Error('Invalid date format');
    }

    // Tính khoảng cách giữa start và end
    const duration = end.diff(start, 'day') + 1;

    // Xác định khoảng thời gian của kỳ trước
    const previousStart = start.subtract(duration, 'day');
    const previousEnd = end.subtract(duration, 'day');

    // Lấy giá trị của kỳ hiện tại
    const currentValue = await getValue(
      start.format('YYYY-MM-DD'),
      end.format('YYYY-MM-DD'),
    );

    // Lấy giá trị của kỳ trước
    const previousValue = await getValue(
      previousStart.format('YYYY-MM-DD'),
      previousEnd.format('YYYY-MM-DD'),
    );

    if (previousValue === 0) {
      // Nếu giá trị kỳ trước bằng 0, tránh chia cho 0
      return currentValue > 0 ? 100 : 0;
    }

    // Tính % tăng trưởng
    return ((currentValue - previousValue) / previousValue) * 100;
  }
}
