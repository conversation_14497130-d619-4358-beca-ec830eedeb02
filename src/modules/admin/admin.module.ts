import { Module } from '@nestjs/common';
import { ClientModule } from './client/client.module';
import { MachineModule } from './machine/machine.module';
import { MachineBrandModule } from './machineBrand/machineBrand.module';
import { OrderModule } from './order/order.module';
import { UserModule } from './user/user.module';
import { AuthModule } from './session/auth.module';
import { PaymentAccountSettingModule } from './paymentAccountSetting/paymentAccountSetting.module';
import { ClientAccountModule } from './clientAccount/clientAccount.module';
import { AnalyticsModule } from './analytics/analytics.module';
import { ImageModule } from './uploadImage/image.module';
import { MBankModule } from './mBank/machineBrand.module';
import { AdminReupImageModule } from './reupImage/reupImage.module';
import { AppOrderModule } from './appOrder/appOrder.module';

@Module({
  imports: [
    AnalyticsModule,
    MachineModule,
    ClientModule,
    ClientAccountModule,
    MachineBrandModule,
    OrderModule,
    UserModule,
    AuthModule,
    PaymentAccountSettingModule,
    ImageModule,
    MBankModule,
    AdminReupImageModule,
    AppOrderModule,
  ],
})
export class AdminModule {}
