import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MachineService } from './machine.service';
import { MachineResolver } from './machine.resolver';
import { Machine } from '../../common/entity/machine.entity';
import { JwtService } from '@nestjs/jwt';
import { User } from '../user/user.entity';
import { Client } from '../../common/entity/client.entity';
import { MachineBrand } from '../machineBrand/machineBrand.entity';
import { SBLogger } from 'src/modules/logger/logger.service';
import { PrintSetting } from 'src/modules/common/entity/printSetting.entity';
import { MachineStatus } from 'src/modules/common/entity/machineStatus.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Machine,
      MachineBrand,
      User,
      Client,
      PrintSetting,
      MachineStatus
    ]),
  ],
  providers: [JwtService, MachineService, MachineResolver, SBLogger],
  exports: [MachineService],
})
export class MachineModule {}
