import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { Machine, MachineResponse } from '../../common/entity/machine.entity';
import { MachineService } from './machine.service';
import {
  CreateMachineInput,
  GetMachineInput,
  UpdateMachineInput,
} from './machine.input';
import { TMachineResponse } from './machine.type';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermissions,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';

@Resolver(() => Machine)
@UseGuards(AuthGuard)
export class MachineResolver {
  constructor(private machineService: MachineService) {}

  @Mutation(() => [MachineResponse])
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_DETAILS)
  async adminMachines(
    @Args('input') input: GetMachineInput,
  ): Promise<TMachineResponse[]> {
    return this.machineService.getMachines(input);
  }

  @Query(() => MachineResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_DETAILS)
  async adminMachineById(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<TMachineResponse> {
    return this.machineService.findOneById(id);
  }

  @Mutation(() => MachineResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminCreateMachine(
    @Args('input') input: CreateMachineInput,
    @Context() context: any,
  ): Promise<TMachineResponse> {
    return this.machineService.create(input, context.req.user);
  }

  @Mutation(() => MachineResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminUpdateMachine(
    @Args('input') input: UpdateMachineInput,
    @Context() context: any,
  ): Promise<TMachineResponse> {
    return this.machineService.update(input, context.req.user);
  }

  @Mutation(() => Machine)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminDeleteMachine(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<Machine> {
    return this.machineService.delete(id);
  }
}
