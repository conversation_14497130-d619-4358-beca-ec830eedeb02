import { Field, InputType } from '@nestjs/graphql';
import { EProductionStatus } from 'src/enum';

@InputType()
export class CreateMachineInput {
  @Field()
  machineId: string;

  @Field()
  price: number;

  @Field({ nullable: true })
  productionStatus?: EProductionStatus;

  @Field({ nullable: true })
  havePayScreen: boolean;

  @Field({ nullable: false })
  expiryDate: string;

  @Field({ nullable: true })
  description: string;
}

@InputType()
export class UpdateMachineInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  machineId?: string;

  @Field({ nullable: true })
  price?: number;

  @Field({ nullable: true })
  productionStatus?: EProductionStatus;

  @Field({ nullable: true })
  havePayScreen: boolean;

  @Field({ nullable: true })
  renewalDate: string;

  @Field({ nullable: true })
  orderId?: string;

  @Field({ nullable: true })
  userId?: string;

  @Field({ nullable: true })
  expiryDate: string;

  @Field({ nullable: true })
  description: string;

  @Field({ nullable: true })
  remotePin?: string;
}

@InputType()
export class GetMachineInput {
  @Field({ nullable: true })
  machineId: string;

  @Field({ nullable: true })
  machineCode: string;

  @Field({ nullable: true })
  productionStatus?: EProductionStatus;

  @Field({ nullable: true })
  havePayScreen: boolean;

  @Field({ nullable: true })
  haveOrder: boolean;

  @Field({ nullable: true })
  userId: string;

  @Field({ nullable: true })
  expiryDate: string;

  @Field({ nullable: true })
  description: string;
}
