type MachineBrand {
  id: ID!
  brandCode: String!
  name: String!
  description: String!
}

type User {
  id: ID!
  name: String!
  email: String!
}

type MachineResponse {
  id: ID!
  machineBrand: MachineBrand!
  machineCode: String!
  user: User!
  orderId: String!
  rentalDate: String!
  renewalDate: String!
  havePayScreen: Boolean!
  productionStatus: String!
  price: Int!
  expiryDate: String!
  description: String!
}

type Machine {
  id: ID!
  machineCode: String!
  rentalDate: String!
  renewalDate: String!
  havePayScreen: Boolean!
  productionStatus: String!
  price: Int!
}

input CreateMachineInput {
  machineId: String!
  havePayScreen: Boolean!
  productionStatus: String!
  price: Int!
  expiryDate: String!
  description: String!
}

input UpdateMachineInput {
  id: ID!
  machineId: String
  havePayScreen: Boolean
  productionStatus: String
  price: Int
  expiryDate: String
  description: String
  remotePin: String
  renewalDate: String
  orderId: String
  userId: String
}

input GetMachineInput {
  machineId: String
  machineCode: String
  productionStatus: String
  havePayScreen: Boolean
  haveOrder: Boolean
  userId: String
  expiryDate: String
  description: String
}

type Query {
  adminMachineById(id: ID!): MachineResponse
}

type Mutation {
  adminMachines(input: GetMachineInput): [MachineResponse]
  adminCreateMachine(input: CreateMachineInput): MachineResponse
  adminUpdateMachine(input: UpdateMachineInput): MachineResponse
  adminDeleteMachine(id: ID!): Machine
}
