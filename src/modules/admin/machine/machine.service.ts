import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  CreateMachineInput,
  GetMachineInput,
  UpdateMachineInput,
} from './machine.input';
import { Machine } from '../../common/entity/machine.entity';
import * as moment from 'moment';
import { SBLogger } from '../../logger/logger.service';
import { User } from '../user/user.entity';
import { TMachineResponse } from './machine.type';
import { get } from 'lodash';
import { Client } from '../../common/entity/client.entity';
import { MachineBrand } from '../machineBrand/machineBrand.entity';
import { EProductionStatus } from 'src/enum';
import { PrintSetting } from 'src/modules/common/entity/printSetting.entity';
import { MachineStatus } from 'src/modules/common/entity/machineStatus.entity';

@Injectable()
export class MachineService {
  constructor(
    @InjectRepository(Machine)
    private machineRepository: Repository<Machine>,

    @InjectRepository(MachineBrand)
    private machineBrandRepository: Repository<MachineBrand>,

    @InjectRepository(Client)
    private clientRepository: Repository<Client>,

    @InjectRepository(PrintSetting)
    private printSettingRepository: Repository<PrintSetting>,

    @InjectRepository(MachineStatus)
    private machineStatusRepository: Repository<MachineStatus>,

    private loggerService: SBLogger,
  ) {}

  async findAll(): Promise<Machine[]> {
    return this.logExecution('findAll', async () =>
      this.machineRepository.find(),
    );
  }

  async findByIds(ids: string[]): Promise<TMachineResponse[]> {
    return this.logExecution('findByIds', async () => {
      const machines = await this.machineRepository.findByIds(ids);
      return this.convertRef(machines);
    });
  }

  async findOneById(id: string): Promise<TMachineResponse> {
    return this.logExecution('findOneById', async () => {
      const machine = await this.machineRepository.findOneBy({ id });
      if (!machine) throw new NotFoundException('Machine not found');
      const [convertedMachine] = await this.convertRef([machine]);
      return convertedMachine;
    });
  }

  async create(
    createMachineInput: CreateMachineInput,
    user: User,
  ): Promise<TMachineResponse> {
    return this.logExecution('create', async () => {
      this.validateInput(createMachineInput);

      const machineBrand = await this.validateMachineBrand(
        createMachineInput.machineId,
      );

      const tmp = this.machineRepository.create({
        ...createMachineInput,
        ...this.generateMachineMetadata(user.id),
      });

      const result = await this.machineRepository.save(tmp);

      const machineStatus = this.machineStatusRepository.create({
        clientId: user.id,
        machine: result,
      });
      await this.machineStatusRepository.save(machineStatus);

      return { ...result, machineBrand };
    });
  }

  async update(
    updateMachineInput: UpdateMachineInput,
    user: User,
  ): Promise<TMachineResponse> {
    return this.logExecution('update', async () => {
      // Kiểm tra và xử lý machineBrand nếu có thay đổi
      let machineBrand = null;
      if (updateMachineInput.machineId) {
        machineBrand = await this.validateMachineBrand(updateMachineInput.machineId);
      }

      const updatedMachine = await this.machineRepository.preload({
        ...updateMachineInput,
        id: updateMachineInput.id,
        ...this.generateMachineMetadata(user.id, false),
      });

      if (!updatedMachine) {
        throw new NotFoundException('Machine not found');
      }

      this.validateInput(updatedMachine);

      const previousMachine = await this.machineRepository.findOneBy({
        id: updateMachineInput.id,
      });

      const result = await this.machineRepository.save(updatedMachine);

      await this.handleClientUpdates(updatedMachine, previousMachine);

      const [convertedMachine] = await this.convertRef([result]);

      // Nếu có thay đổi machineBrand, trả về kết quả với machineBrand mới
      if (machineBrand) {
        return { ...convertedMachine, machineBrand };
      }

      return convertedMachine;
    });
  }

  async delete(id: string): Promise<Machine> {
    return this.logExecution('delete', async () => {
      const machine = await this.machineRepository.findOneBy({ id });
      if (!machine) throw new NotFoundException('Machine not found');
      if (machine.orderId)
        throw new BadRequestException('Machine has an order');

      return this.machineRepository.remove(machine);
    });
  }

  async getMachines(input: GetMachineInput): Promise<TMachineResponse[]> {
    const { machineCode, havePayScreen, productionStatus, userId } = input;

    const clients = await this.machineRepository.find({
      where: {
        machineCode,
        havePayScreen,
        productionStatus,
        userId,
      },
      order: {
        createdAt: 'DESC',
      },
    });

    if (get(input, 'haveOrder') !== undefined) {
      const filteredClients = clients.filter(
        (el) => Boolean(el.orderId) === !!input.haveOrder,
      );
      return this.convertRef(filteredClients);
    }

    const convertedClients = await this.convertRef(clients);
    return convertedClients;
  }

  private async validateMachineBrand(machineId: string): Promise<MachineBrand> {
    const machineBrand = await this.machineBrandRepository.findOneBy({
      id: machineId,
    });
    if (!machineBrand) throw new BadRequestException('No machine brand');
    return machineBrand;
  }

  private validateInput(input: CreateMachineInput | UpdateMachineInput): void {
    if (
      input.productionStatus &&
      !Object.values(EProductionStatus).includes(input.productionStatus)
    ) {
      throw new BadRequestException(
        `Invalid status provided. Allowed values: ${Object.values(
          EProductionStatus,
        ).join(', ')}`,
      );
    }

    if (
      input.expiryDate &&
      (typeof input.expiryDate !== 'string' || !/^\d+$/.test(input.expiryDate))
    ) {
      throw new BadRequestException(
        'Invalid expiryDate. It must be a numeric string.',
      );
    }
  }

  private generateMachineMetadata(userId: string, isNew = true) {
    const timestamp = moment.utc().valueOf().toString();
    return {
      machineCode: isNew ? `SNAPBOX${timestamp}` : undefined,
      machinePin: isNew
        ? Math.floor(1000 + Math.random() * 9000).toString()
        : undefined,
      createdAt: isNew ? timestamp : undefined,
      createdBy: isNew ? userId : undefined,
      updatedAt: isNew ? undefined : timestamp,
      updatedBy: isNew ? undefined : userId,
    };
  }

  private async convertRef(machines: Machine[]): Promise<TMachineResponse[]> {
    const tmp = await this.mappingProp(
      machines,
      'machineId',
      'machineBrand',
      this.machineBrandRepository,
    );
    return this.mappingProp(tmp, 'userId', 'user', this.clientRepository);
  }

  private async mappingProp(
    entities: Machine[],
    prop: string,
    targetProp: string,
    repository: Repository<any>,
  ): Promise<TMachineResponse[]> {
    const ids = entities.map((el) => el[prop]);
    if (!ids.length) return entities;

    const mapping = {};
    (await repository.findByIds(ids)).forEach((el) => {
      mapping[el.id] = el;
    });

    return entities.map((el) => ({
      ...el,
      [targetProp]: mapping[el[prop]],
    }));
  }

  private async handleClientUpdates(
    updatedMachine: Machine,
    previousMachine: Machine,
  ): Promise<void> {
    const isDeliveredNow = updatedMachine.productionStatus === 'DELIVERED';
    const wasDeliveredBefore = previousMachine.productionStatus === 'DELIVERED';

    if (updatedMachine.userId && isDeliveredNow !== wasDeliveredBefore) {
      const client = await this.clientRepository.findOneBy({
        id: updatedMachine.userId,
      });

      if (client) {
        client.totalMachines = client.totalMachines || 0;

        if (isDeliveredNow) {
          client.totalMachines += 1;
        } else if (wasDeliveredBefore) {
          client.totalMachines = Math.max(client.totalMachines - 1, 0);
        }

        await this.clientRepository.save(client);
      }
    }
  }

  private async logExecution<T>(
    action: string,
    callback: () => Promise<T>,
  ): Promise<T> {
    this.loggerService.log(`START - machine - ${action}`);
    try {
      const result = await callback();
      this.loggerService.log(`END - machine - ${action}`, result);
      return result;
    } catch (error) {
      this.loggerService.log(`ERROR - machine - ${action}`, error.message);
      throw error;
    }
  }
}
