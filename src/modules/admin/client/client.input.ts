import { Field, InputType, ObjectType } from '@nestjs/graphql';

@InputType()
export class CreateClientInput {
  @Field()
  name: string;

  @Field({ nullable: true })
  ownerName?: string;

  @Field()
  email: string;

  @Field()
  password: string;

  @Field()
  phone: string;

  @Field()
  province: string;

  @Field()
  address: string;
}

@InputType()
export class UpdateClientInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field({ nullable: true })
  ownerName?: string;

  @Field({ nullable: true })
  password?: string;

  @Field({ nullable: true })
  phone?: string;

  @Field({ nullable: true })
  province?: string;

  @Field({ nullable: true })
  address?: string;

  @Field({ nullable: true })
  email?: string;
}

@InputType()
export class GetClientInput {
  @Field({ nullable: true })
  name: string;

  @Field({ nullable: true })
  ownerName?: string;

  @Field({ nullable: true })
  email?: string;

  @Field({ nullable: true })
  phone?: string;

  @Field({ nullable: true })
  province?: string;

  @Field({ nullable: true })
  address?: string;
}

@ObjectType()
export class AdminClientResponse {
  @Field()
  id: string;

  @Field()
  name: string;

  @Field()
  email: string;

  @Field({ nullable: true })
  ownerName?: string;

  @Field()
  phone: string;

  @Field()
  province: string;

  @Field()
  address: string;

  @Field()
  totalOrders: number;

  @Field()
  totalMachines: number;

  @Field()
  createdAt: string;

  @Field(() => Boolean, { nullable: true })
  isPayOn: boolean;
}

@ObjectType()
export class ClientGenerateFramesResponse {
  @Field(() => Boolean)
  isSuccess: boolean;
}
