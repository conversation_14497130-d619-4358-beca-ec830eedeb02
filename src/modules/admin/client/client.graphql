type AdminClientResponse {
  id: ID!
  name: String!
  ownerName: String
  email: String!
  phone: String!
  province: String!
  address: String!
  totalOrders: Int!
  totalMachines: Int!
  createdAt: String!
  isPayOn: Boolean
}

input CreateClientInput {
  name: String!
  ownerName: String
  email: String!
  phone: String!
  province: String!
  address: String!
  password: String!
}

input UpdateClientInput {
  id: ID
  name: String!
  ownerName: String
  phone: String!
  province: String!
  address: String!
  password: String!
}

input GetClientInput {
  name: String!
  ownerName: String
  email: String!
  phone: String!
  province: String!
  address: String!
}

type AdminClientGenerateFramesRes {
  isSuccess: Boolean!
}

type ClientLoginRes {
  id: ID!
  email: String!
  token: String!
}

type Query {
  adminClientById(id: ID!): AdminClientResponse
}

type Mutation {
  adminClients(input: GetClientInput): [AdminClientResponse]
  adminCreateClient(input: CreateClientInput): AdminClientResponse
  adminUpdateClient(input: UpdateClientInput): AdminClientResponse
  adminDeleteClient(id: ID!): AdminClientResponse
  adminClientGenerateFrames(clientId: ID!): AdminClientGenerateFramesRes!
  adminLoginClientById(id: ID!): ClientLoginRes
}
