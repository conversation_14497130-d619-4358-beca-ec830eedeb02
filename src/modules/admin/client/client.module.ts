import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ClientService } from './client.service';
import { ClientResolver } from './client.resolver';
import { Client } from '../../common/entity/client.entity';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { SettingSize } from 'src/modules/common/entity/settingSize.entity';
import { WaitingScreen } from 'src/modules/common/entity/waitingScreen.entity';
import { Layout } from 'src/modules/common/entity/layout.entity';
import { LayoutFormat } from 'src/modules/common/entity/layoutFormat.entity';
import { LayoutItem } from 'src/modules/common/entity/layoutItem.entity';
import { PaymentAccountSetting } from 'src/modules/common/entity/paymentAccountSetting.entity';
import { FrameGeneratorModule } from 'src/modules/common/module/frameGenerator/frameGenerator.module';
import { PrintSetting } from 'src/modules/common/entity/printSetting.entity';
import { AppearanceSetting } from 'src/modules/common/entity/appearanceSetting.entity';
import { ClientAccount } from 'src/modules/common/entity/clientAccount.entity';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      Client,
      WaitingScreen,
      SettingSize,
      Layout,
      LayoutFormat,
      LayoutItem,
      PaymentAccountSetting,
      PrintSetting,
      AppearanceSetting,
      ClientAccount,
    ]),
    FrameGeneratorModule,
  ],
  providers: [JwtService, ClientService, ClientResolver, SBLogger],
  exports: [ClientService],
})
export class ClientModule {}
