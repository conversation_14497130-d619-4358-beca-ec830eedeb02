import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import {
  CreateClientInput,
  GetClientInput,
  UpdateClientInput,
  AdminClientResponse,
  ClientGenerateFramesResponse,
} from './client.input';
import { ClientService } from './client.service';
import { Client } from '../../common/entity/client.entity';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { Auth } from 'src/modules/common/entity/auth.entity';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermissions,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';

@Resolver(() => Client)
@UseGuards(AuthGuard)
export class ClientResolver {
  constructor(private clientService: ClientService) {}

  @Mutation(() => [AdminClientResponse])
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENTS)
  async adminClients(
    @Args('input') input: GetClientInput,
  ): Promise<AdminClientResponse[]> {
    return this.clientService.getClients(input);
  }

  @Query(() => Client)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_DETAILS)
  async adminClientById(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<Client> {
    return this.clientService.findOneById(id);
  }

  @Mutation(() => Client)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminCreateClient(
    @Args('input') input: CreateClientInput,
    @Context() context: any,
  ): Promise<Client> {
    return this.clientService.create(input, context.req.user);
  }

  @Mutation(() => Client)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminUpdateClient(
    @Args('input') input: UpdateClientInput,
    @Context() context: any,
  ): Promise<Client> {
    return this.clientService.update(input, context.req.user);
  }

  @Mutation(() => Client)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminDeleteClient(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<Client> {
    return this.clientService.delete(id);
  }

  @Mutation(() => ClientGenerateFramesResponse)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminClientGenerateFrames(
    @Args('clientId', { type: () => ID }) clientId: string,
  ): Promise<ClientGenerateFramesResponse> {
    return this.clientService.generateFrames(clientId);
  }

  @Mutation(() => Auth)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminLoginClientById(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<Auth> {
    return this.clientService.loginById(id);
  }
}
