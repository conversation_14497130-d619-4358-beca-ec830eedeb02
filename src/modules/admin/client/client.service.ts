import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not } from 'typeorm';
import {
  CreateClientInput,
  GetClientInput,
  UpdateClientInput,
  AdminClientResponse,
} from './client.input';
import { Client } from '../../common/entity/client.entity';
import * as bcrypt from 'bcrypt';
import * as moment from 'moment';
import { SBLogger } from '../../logger/logger.service';
import { User } from '../user/user.entity';
import { WaitingScreen } from 'src/modules/common/entity/waitingScreen.entity';
import { SettingSize } from 'src/modules/common/entity/settingSize.entity';
import {
  ELayoutType,
  EPrintSettingType,
  ESettingSizeType,
  EWaitingScreenType,
} from 'src/enum';
import { Layout } from 'src/modules/common/entity/layout.entity';
import { LayoutFormat } from 'src/modules/common/entity/layoutFormat.entity';
import { LayoutItem } from 'src/modules/common/entity/layoutItem.entity';
import { PaymentAccountSetting } from '../../common/entity/paymentAccountSetting.entity';
import { FrameGeneratorService } from 'src/modules/common/module/frameGenerator/frameGenerator.service';
import { PrintSetting } from 'src/modules/common/entity/printSetting.entity';
import { DEFAULT_PRINT_SETTING } from 'src/constants';
import { AppearanceSetting } from 'src/modules/common/entity/appearanceSetting.entity';
import { Auth } from '../../common/entity/auth.entity';
import { JwtService } from '@nestjs/jwt';
import { jwtConstants } from 'src/constants';
import { ClientAccount } from 'src/modules/common/entity/clientAccount.entity';
@Injectable()
export class ClientService {
  constructor(
    @InjectRepository(Client)
    private clientRepository: Repository<Client>,
    @InjectRepository(ClientAccount)
    private clientAccountRepository: Repository<ClientAccount>,
    private loggerService: SBLogger,
    @InjectRepository(WaitingScreen)
    private waitingScreenRepository: Repository<WaitingScreen>,
    @InjectRepository(SettingSize)
    private settingSizeRepository: Repository<SettingSize>,
    @InjectRepository(Layout)
    private layoutRepository: Repository<Layout>,
    @InjectRepository(PaymentAccountSetting)
    private paymentAccountSettingRepository: Repository<PaymentAccountSetting>,
    @InjectRepository(PrintSetting)
    private printSettingRepository: Repository<PrintSetting>,
    @InjectRepository(AppearanceSetting)
    private appearanceSettingRepository: Repository<AppearanceSetting>,
    private frameGeneratorService: FrameGeneratorService,
    private jwtService: JwtService,
  ) {}

  async findAll(): Promise<Client[]> {
    const result = this.clientRepository.find();
    return result;
  }

  async findOneById(id: string): Promise<Client> {
    const res = this.clientRepository.findOneBy({ id });
    return res;
  }

  async loginById(id: string): Promise<Auth> {
    const client = await this.clientRepository.findOneBy({
      id,
    });
    if (!client) {
      this.loggerService.log('ERROR - client - update', 'Client not found');
      throw new NotFoundException('Client not found');
    }
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { password, ...result } = client;
    const token = await this.jwtService.signAsync(result, jwtConstants);
    return { ...result, token, role: 'ADMIN' };
  }

  async create(
    createClientInput: CreateClientInput,
    user: User,
  ): Promise<Client> {
    if (createClientInput.email) {
      const duplicateClients = await this.clientRepository.find({
        where: [
          {
            email: createClientInput.email,
          },
        ],
      });
      const duplicateClientAccounts = await this.clientAccountRepository.find({
        where: [
          {
            email: createClientInput.email,
          },
        ],
      });

      [...duplicateClients, ...duplicateClientAccounts].forEach((client) => {
        if (client.email === createClientInput.email) {
          this.loggerService.log(
            'ERROR - client - create',
            'Email already exists',
          );
          throw new BadRequestException('Email đã tồn tại trong hệ thống');
        }
      });
    }

    const hashPassword = await bcrypt.hash(createClientInput.password, 10);
    const tmp = await this.clientRepository.create({
      ...createClientInput,
      password: hashPassword,
      createdAt: moment.utc().valueOf().toString(),
      createdBy: user.id.toString(),
    });

    await this.clientRepository.save(tmp);
    await this.createDefaultWaitingScreen(tmp.id);
    await this.createDefaultSettingSize(tmp.id);
    await this.createDefaultLayout(tmp.id);
    await this.createDefaultPrintSetting(tmp.id);
    await this.createDefaultAppearanceSetting(tmp.id);
    this.frameGeneratorService.execute(tmp.id);
    return tmp;
  }

  async update(
    updateClientInput: UpdateClientInput,
    user: User,
  ): Promise<Client> {
    const tmp = await this.clientRepository.findOneBy({
      id: updateClientInput.id,
    });
    if (!tmp) {
      this.loggerService.log('ERROR - client - update', 'Client not found');
      throw new NotFoundException('Client not found');
    }

    if (updateClientInput.email) {
      const duplicateClients = await this.clientRepository.find({
        where: [
          {
            email: updateClientInput.email,
            id: Not(updateClientInput.id),
          },
        ],
      });
      const duplicateClientAccounts = await this.clientAccountRepository.find({
        where: [
          {
            email: updateClientInput.email,
          },
        ],
      });

      [...duplicateClients, ...duplicateClientAccounts].forEach((client) => {
        if (client.email === updateClientInput.email) {
          this.loggerService.log(
            'ERROR - client - update',
            'Email already exists',
          );
          throw new BadRequestException('Email đã tồn tại trong hệ thống');
        }
      });
    }

    const hashPassword = updateClientInput.password
      ? await bcrypt.hash(updateClientInput.password, 10)
      : tmp.password;

    const res = this.clientRepository.save({
      ...tmp,
      ...updateClientInput,
      password: hashPassword,
      updatedAt: moment.utc().valueOf().toString(),
      updatedBy: user.id.toString(),
    });

    return res;
  }

  async delete(id: string): Promise<Client> {
    const tmp = await this.clientRepository.findOneBy({ id });
    if (!tmp) {
      this.loggerService.log('ERROR - client - delete', 'Client not found');
      throw new NotFoundException('Client not found');
    }
    const res = await this.clientRepository.remove(tmp);
    return res;
  }

  async getClients(
    input: GetClientInput,
  ): Promise<Omit<AdminClientResponse, 'password'>[]> {
    const clients = await this.clientRepository.find({
      where: input,
      order: {
        createdAt: 'DESC',
      },
    });

    const res = await Promise.all(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      clients.map(async ({ password, ...client }) => {
        const hasActivePayment =
          await this.paymentAccountSettingRepository.findOne({
            where: { clientId: client.id, isActive: true },
          });

        return {
          ...client,
          isPayOn: !!hasActivePayment,
        };
      }),
    );

    return res;
  }

  async createDefaultWaitingScreen(clientId: string): Promise<void> {
    const waitingScreen = this.waitingScreenRepository.create({
      type: EWaitingScreenType.Default,
      clientId,
      createdAt: moment.utc().valueOf().toString(),
      updatedAt: moment.utc().valueOf().toString(),
    });
    await this.waitingScreenRepository.save(waitingScreen);
  }

  async createDefaultSettingSize(clientId: string): Promise<void> {
    const settingSize = this.settingSizeRepository.create({
      settingSizeType: ESettingSizeType.Default,
      smallSizePrice2: 70000,
      smallSizePrice4: 130000,
      smallSizePrice6: 180000,
      smallSizePrice8: 230000,
      smallSizePrice10: 280000,
      largeSizePrice2: 90000,
      largeSizePrice3: 140000,
      largeSizePrice4: 180000,
      largeSizePrice5: 220000,
      largeSizePrice6: 260000,
      clientId,
    });
    await this.settingSizeRepository.save(settingSize);
  }

  async createDefaultLayout(clientId: string): Promise<void> {
    const layout = this.layoutRepository.create({
      layoutType: ELayoutType.Default,
      clientId,
    });
    const formats: LayoutFormat[] = [3, 4, 5, 6].map((count) => {
      const format = new LayoutFormat();
      format.name = `${count} Images`;
      format.imageCount = count;
      format.layoutItems = Array.from({ length: count }).map((_, index) => {
        const item = new LayoutItem();
        item.position = index + 1;
        return item;
      });
      return format;
    });
    layout.formats = formats;
    await this.layoutRepository.save(layout);
  }

  async createDefaultPrintSetting(clientId: string): Promise<void> {
    const defaultPrintSetting = await this.printSettingRepository.findOneBy({
      type: EPrintSettingType.Default,
      clientId: clientId,
    });
    if (defaultPrintSetting) return;
    await this.printSettingRepository.insert({
      type: EPrintSettingType.Default,
      clientId: clientId,
      ...DEFAULT_PRINT_SETTING,
    });
  }

  async createDefaultAppearanceSetting(clientId: string): Promise<void> {
    const appearanceSetting = this.appearanceSettingRepository.create({
      primary_color: '#FFC5D3',
      secondary_color: '#535353',
      background_color: '#ffffff',
      primary_text_color: '#000000',
      secondary_text_color: '#797979',
      secondary_text_color_2: '#ffffff',
      clientId,
    });
    await this.appearanceSettingRepository.save(appearanceSetting);
  }

  async generateFrames(clientId: string): Promise<{ isSuccess: boolean }> {
    const client = await this.clientRepository.findOneBy({ id: clientId });
    if (!client) {
      this.loggerService.log(
        'ERROR - client - generateFrames',
        'Client not found',
      );
      throw new NotFoundException('Client not found');
    }
    this.frameGeneratorService.execute(clientId);
    return { isSuccess: true };
  }
}
