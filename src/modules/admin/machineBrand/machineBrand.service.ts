import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  CreateMachineBrandInput,
  UpdateMachineBrandInput,
  GetMachineBrandInput,
} from './machineBrand.input';
import { MachineBrand } from './machineBrand.entity';
import * as moment from 'moment';
import { User } from '../user/user.entity';
import { Machine } from '../../common/entity/machine.entity';
import { SBLogger } from 'src/modules/logger/logger.service';
@Injectable()
export class MachineBrandService {
  constructor(
    @InjectRepository(MachineBrand)
    private machineBrandRepository: Repository<MachineBrand>,

    @InjectRepository(Machine)
    private machineRepository: Repository<Machine>,

    private loggerService: SBLogger,
  ) {}

  async getMachineBrands(input: GetMachineBrandInput): Promise<MachineBrand[]> {
    const { supported, name } = input;
    const whereCondition: any = {};
    if (supported !== undefined) whereCondition.supported = supported;
    if (name) whereCondition.name = name;

    const clients = await this.machineBrandRepository.find({
      where: whereCondition,
      order: { createdAt: 'DESC' },
    });

    return clients;
  }

  async findOneById(id: string): Promise<MachineBrand> {
    const res = this.machineBrandRepository.findOneBy({ id });
    return res;
  }

  async create(
    createMachineBrandInput: CreateMachineBrandInput,
    user: User,
  ): Promise<MachineBrand> {
    const year = moment.utc().format('YYYY');
    const existingCount = await this.machineBrandRepository.count();
    const nextNumber = existingCount + 1;
    const brandCode = `M${year}${nextNumber.toString().padStart(3, '0')}`;

    const tmp = this.machineBrandRepository.create({
      ...createMachineBrandInput,
      brandCode,
      createdAt: moment.utc().valueOf().toString(),
      createdBy: user.id.toString(),
    });
    return this.machineBrandRepository.save(tmp);
  }

  async update(
    updateMachineBrandInput: UpdateMachineBrandInput,
    user: User,
  ): Promise<MachineBrand> {
    const tmp = await this.machineBrandRepository.findOneBy({
      id: updateMachineBrandInput.id,
    });
    if (!tmp) {
      this.loggerService.log(
        'ERROR - machineBrand - update',
        'MachineBrand not found',
      );
      throw new NotFoundException('MachineBrand not found');
    }

    const res = this.machineBrandRepository.save({
      ...tmp,
      ...updateMachineBrandInput,
      updatedAt: moment.utc().valueOf().toString(),
      updatedBy: user.id.toString(),
    });
    return res;
  }

  async delete(id: string): Promise<MachineBrand> {
    const tmp = await this.machineBrandRepository.findOneBy({ id });
    if (!tmp) {
      this.loggerService.log(
        'ERROR - machineBrand - delete',
        'MachineBrand not found',
      );
      throw new NotFoundException('MachineBrand not found');
    }
    const machine = await this.machineRepository.findBy({ machineId: id });
    if (machine.length > 0) {
      this.loggerService.log(
        'ERROR - machineBrand - delete',
        'Machine is using',
      );
      throw new NotFoundException('Machine is using');
    }

    return this.machineBrandRepository.remove(tmp);
  }
}
