import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MachineBrandService } from './machineBrand.service';
import { MachineBrandResolver } from './machineBrand.resolver';
import { MachineBrand } from './machineBrand.entity';
import { JwtService } from '@nestjs/jwt';
import { Machine } from '../../common/entity/machine.entity';
import { SBLogger } from 'src/modules/logger/logger.service';
@Module({
  imports: [TypeOrmModule.forFeature([Machine, MachineBrand])],
  providers: [JwtService, MachineBrandService, MachineBrandResolver, SBLogger],
  exports: [MachineBrandService],
})
export class MachineBrandModule {}
