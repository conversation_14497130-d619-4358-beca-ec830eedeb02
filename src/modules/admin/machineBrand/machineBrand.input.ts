import { Field, InputType } from '@nestjs/graphql';

@InputType()
export class CreateMachineBrandInput {
  @Field()
  name: string;

  @Field({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  supported: boolean;

  @Field({ nullable: true })
  code: string;

  @Field({ nullable: true })
  modelKey?: string;
}

@InputType()
export class UpdateMachineBrandInput {
  @Field()
  id: string;

  @Field({ nullable: true })
  name: string;

  @Field({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  supported: boolean;

  @Field({ nullable: true })
  code: string;

  @Field({ nullable: true })
  modelKey?: string;
}

@InputType()
export class GetMachineBrandInput {
  @Field({ nullable: true })
  name: string;

  @Field({ nullable: true })
  supported: boolean;
}
