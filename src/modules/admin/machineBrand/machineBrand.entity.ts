import { Field, ObjectType } from '@nestjs/graphql';
import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { Machine } from '../../common/entity/machine.entity';
@Entity()
@ObjectType()
export class MachineBrand {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column()
  @Field()
  brandCode: string;

  @Column()
  @Field()
  name: string;

  @Column()
  @Field()
  description?: string;

  @Column({ default: false })
  @Field()
  supported: boolean;

  @Column()
  @Field()
  code: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  modelKey?: string;

  @Column()
  @Field()
  createdAt: string;

  @Column()
  @Field()
  createdBy: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  updatedAt: string;

  @Column({ nullable: true })
  @Field({ nullable: true })
  updatedBy: string;

  @OneToMany(() => Machine, (machine) => machine.machineBrand)
  @Field(() => [Machine])
  machines: Machine[];
}
