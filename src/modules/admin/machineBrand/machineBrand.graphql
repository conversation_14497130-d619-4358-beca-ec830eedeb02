type MachineBrand {
  id: ID!
  brandCode: String!
  name: String!
  description: String!
  supported: Boolean!
  createdAt: String!
  code: String!
}

input CreateMachineBrandInput {
  brandCode: String!
  name: String!
  code: String!
  description: String!
  supported: Boolean!
  modelKey: String
}

input UpdateMachineBrandInput {
  id: ID!
  brandCode: String!
  name: String!
  code: String!
  description: String!
  supported: Boolean!
  modelKey: String
}

input GetMachineBrandInput {
  name: String!
  supported: Boolean!
}

type Query {
  adminGetMachineBrand(input: GetMachineBrandInput): [MachineBrand]
  adminMachineBrandById(id: ID!): Machine<PERSON>rand
}

type Mutation {
  adminCreateMachineBrand(input: CreateMachineBrandInput): Machine<PERSON>rand
  adminUpdateMachineBrand(input: UpdateMachineBrandInput): MachineBrand
  adminDeleteMachineBrand(id: ID!): MachineBrand
}
