import { Resolver, Query, Mutation, Args, ID, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { MachineBrand } from './machineBrand.entity';
import { MachineBrandService } from './machineBrand.service';
import {
  CreateMachineBrandInput,
  UpdateMachineBrandInput,
  GetMachineBrandInput,
} from './machineBrand.input';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermissions,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';

@Resolver(() => MachineBrand)
@UseGuards(AuthGuard)
export class MachineBrandResolver {
  constructor(private machineBrandService: MachineBrandService) {}

  @Query(() => [MachineBrand])
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_DETAILS)
  async adminMachineBrands(
    @Args('input') input: GetMachineBrandInput,
  ): Promise<MachineBrand[]> {
    return this.machineBrandService.getMachineBrands(input);
  }

  @Query(() => MachineBrand)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_DETAILS)
  async adminMachineBrandById(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<MachineBrand> {
    return this.machineBrandService.findOneById(id);
  }

  @Mutation(() => MachineBrand)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminCreateMachineBrand(
    @Args('input') input: CreateMachineBrandInput,
    @Context() context: any,
  ): Promise<MachineBrand> {
    return this.machineBrandService.create(input, context.req.user);
  }

  @Mutation(() => MachineBrand)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminUpdateMachineBrand(
    @Args('input') input: UpdateMachineBrandInput,
    @Context() context: any,
  ): Promise<MachineBrand> {
    return this.machineBrandService.update(input, context.req.user);
  }

  @Mutation(() => MachineBrand)
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.ADMIN_ONLY)
  async adminDeleteMachineBrand(
    @Args('id', { type: () => ID }) id: string,
  ): Promise<MachineBrand> {
    return this.machineBrandService.delete(id);
  }
}
