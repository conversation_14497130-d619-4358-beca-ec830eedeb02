import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MBank } from 'src/modules/common/entity/mBank.entity';
import { SBLogger } from 'src/modules/logger/logger.service';
@Injectable()
export class MBankService {
  constructor(
    @InjectRepository(MBank)
    private mBankRepository: Repository<MBank>,

    private loggerService: SBLogger,
  ) {}

  async getMBanks(): Promise<MBank[]> {
    const mBanks = await this.mBankRepository.find({
      order: { code: 'DESC' },
    });

    return mBanks;
  }
}
