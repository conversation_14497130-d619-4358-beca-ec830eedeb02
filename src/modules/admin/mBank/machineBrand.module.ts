import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MBankService } from './mBank.service';
import { MBankResolver } from './mBank.resolver';
import { MBank } from 'src/modules/common/entity/mBank.entity';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
@Module({
  imports: [TypeOrmModule.forFeature([MBank])],
  providers: [JwtService, MBankService, MBankResolver, SBLogger],
  exports: [MBankService],
})
export class MBankModule {}
