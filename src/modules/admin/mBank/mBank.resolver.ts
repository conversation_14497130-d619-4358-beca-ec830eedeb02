import { Resolver, Query } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AuthGuard } from 'src/guards/auth.guard';
import { MBank } from 'src/modules/common/entity/mBank.entity';
import { MBankService } from './mBank.service';
import { ModPermissionGuard } from 'src/guards/modPermission.guard';
import {
  ModPermissions,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';

@Resolver(() => MBank)
@UseGuards(AuthGuard)
export class MBankResolver {
  constructor(private mBankService: MBankService) {}

  @Query(() => [MBank])
  @UseGuards(ModPermissionGuard)
  @ModPermissions(ModPermission.VIEW_CLIENT_DETAILS)
  async adminMBanks(): Promise<MBank[]> {
    return this.mBankService.getMBanks();
  }
}
