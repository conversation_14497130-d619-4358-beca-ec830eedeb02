import { HttpException, Injectable } from '@nestjs/common';
import { PayosService as CommonPayosService } from '../common/module/payos/payos.service';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Machine } from '../common/entity/machine.entity';
import { Order } from '../clientApp/order/order.entity';
import { Promotion } from '../common/entity/promotion.entity';
import { PromotionCode } from '../common/entity/promotionCode.entity';
import { FirestoreService } from '../common/module/firestore/firestore.service';
import { EOrderAppStatus, EPaymentMethod } from '../../enum/index';
import { PromotionService } from '../common/module/promotion/promotion.service';
import { PaymentAccountSetting } from '../common/entity/paymentAccountSetting.entity';
import { decrypt } from '../common/module/cryptoUtil/cryptoUtil.service';

@Injectable()
export class PayosService {
  constructor(
    @InjectRepository(Machine)
    private readonly machineRepository: Repository<Machine>,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(PromotionCode)
    private readonly promotionCodeRepository: Repository<PromotionCode>,
    @InjectRepository(Promotion)
    private readonly promotionRepository: Repository<Promotion>,
    @InjectRepository(PaymentAccountSetting)
    private readonly paymentAccountSettingRepository: Repository<PaymentAccountSetting>,
    private readonly firestoreService: FirestoreService,
    private readonly promotionService: PromotionService,
  ) {}

  async callback(body: Record<string, any>) {
    try {
      const machineCode = this.extractMachineCode(body.data.description);
      if (!machineCode) {
        return { message: 'Success' };
      }
      const machine = await this.machineRepository.findOne({
        where: { machineCode },
      });
      if (!machine) {
        return { message: 'Success' };
      }

      const deliveredOrders =
        await this.firestoreService.getDocumentsByCondition(machine.userId, [
          {
            field: 'refCode',
            operator: '==',
            value: body.data.paymentLinkId,
          },
        ]);

      if (deliveredOrders.length) {
        await this.processDeliveredOrders(
          deliveredOrders,
          body.data,
          body.signature,
          machine.userId,
        );
      }
    } catch (error) {
      console.log('error', error);
    } finally {
      return { message: 'Success' };
    }
  }

  private extractMachineCode(description: string): string {
    const match = description.match(/SNAPBOX\d+/);
    if (match) {
      return match[0];
    }
    return;
  }

  private async processDeliveredOrders(
    deliveredOrders: any[],
    paymentData: any,
    paymentSignature: string,
    userId: string,
  ) {
    const order = deliveredOrders[0];
    if (!order) {
      return { message: 'Success' };
    }

    const paymentRecord = await this.paymentAccountSettingRepository.findOne({
      where: { clientId: userId, isActive: true },
    });

    if (!paymentRecord) {
      return { message: 'Success' };
    }

    const isValidSignature = CommonPayosService.isValidSignature(
      paymentData,
      paymentSignature,
      paymentRecord.checkSum,
    );

    if (!isValidSignature) {
      return { message: 'Success' };
    }

    const orderId = await this.createDatabaseOrder(order, paymentData);
    await this.updateFirestoreOrder(order, paymentData, userId, orderId);
  }

  private async updateFirestoreOrder(
    order: any,
    paymentData: any,
    userId: string,
    orderId: string,
  ) {
    const updatedOrder = {
      ...order,
      orderId,
      domain: `${process.env.MEMORY_DOMAIN}${orderId}`,
      status: EOrderAppStatus.DELIVERED,
      receivedAmount: paymentData.amount,
    };

    try {
      await this.firestoreService.updateDocument(
        userId,
        order.id,
        updatedOrder,
      );
    } catch (error) {
      console.error(`Failed to update order ${order.id}:`, error);
    }
  }

  private async createDatabaseOrder(order: any, paymentData: any) {
    const newOrder = this.orderRepository.create({
      orderCode: order.orderCode,
      amount: order.amount,
      receivedAmount: paymentData.amount,
      totalOrderNumber: order.totalPriceAfterDiscount,
      description: order.description,
      refCode: order.refCode,
      status: EOrderAppStatus.DELIVERED,
      paymentMethod: EPaymentMethod.ONLINE,
      machineId: order.machineId,
      clientId: order.clientId,
      settingSizeId: order.settingSizeId,
      frameId: order.frameId,
      topicId: order.topicId,
      promotionId: order.promotionId == 'not found' ? null : order.promotionId,
      promotionCodeId:
        order.promotionCodeId == 'not found' ? null : order.promotionCodeId,
      imageNumber: order.imageNumber,
    });

    try {
      const savedOrder = await this.orderRepository.save(newOrder);
      if (order.promotionCodeId && order.promotionCodeId !== 'not found') {
        const promotionCodeRecord = await this.promotionCodeRepository.findOne({
          where: { id: order.promotionCodeId },
        });

        const promotion = await this.promotionRepository.findOne({
          where: { id: promotionCodeRecord.promotionId },
        });

        await this.promotionService.updatePromotionCode(
          promotionCodeRecord.id,
          promotionCodeRecord,
          promotion,
        );
      }

      return savedOrder.id;
    } catch (error) {
      console.error('Failed to create new order in database:', error);
    }
  }

  async cancel(query: Record<string, any>) {
    return { message: 'Cancel callback function' };
  }
}
