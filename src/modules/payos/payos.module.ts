import { Module } from '@nestjs/common';
import { PayosService } from './payos.service';
import { PayosController } from './payos.controller';
import { Machine } from '../common/entity/machine.entity';
import { Order } from '../clientApp/order/order.entity';
import { Promotion } from '../common/entity/promotion.entity';
import { PromotionCode } from '../common/entity/promotionCode.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FirestoreService } from '../common/module/firestore/firestore.service';
import { PromotionModule } from '../common/module/promotion/promotion.module';
import { PaymentAccountSetting } from '../common/entity/paymentAccountSetting.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Machine,
      Order,
      Promotion,
      PromotionCode,
      PaymentAccountSetting,
    ]),
    PromotionModule,
  ],
  controllers: [PayosController],
  providers: [PayosService, FirestoreService],
})
export class PayosModule {}
