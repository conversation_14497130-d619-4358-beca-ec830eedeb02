import { Body, Controller, Get, Post, Query } from '@nestjs/common';
import { PayosService } from './payos.service';

@Controller('payos')
export class PayosController {
  constructor(private readonly payosService: PayosService) {}

  @Post('callback')
  callback(@Body() body: Record<string, any>) {
    return this.payosService.callback(body);
  }

  @Get('cancel')
  cancel(@Query() query: Record<string, any>) {
    return this.payosService.cancel(query);
  }
}
