import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppImage } from '../../clientApp/uploadImage/image.entity';
import { Order } from '../../clientApp/order/order.entity';
import { Client } from '../../common/entity/client.entity';
import { ImageRes } from './image.input';
import { AppearanceSetting } from '../../common/entity/appearanceSetting.entity';
import { MEMORY_EXPIRE_TIME } from 'src/constants';

@Injectable()
export class ImageService {
  constructor(
    @InjectRepository(AppImage)
    private readonly imageRepository: Repository<AppImage>,

    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,

    @InjectRepository(Client)
    private readonly clientRepository: Repository<Client>,

    @InjectRepository(AppearanceSetting)
    private readonly appearanceSettingRepository: Repository<AppearanceSetting>,
  ) {}

  async getImagesByOrderId(orderId: string): Promise<ImageRes[]> {
    try {
      const order = await this.orderRepository.findOne({
        where: { id: orderId },
        relations: ['machine'],
      });

      if (!order) {
        throw new BadRequestException(
          `Order with ID ${orderId} does not exist.`,
        );
      }

      // Kiểm tra xem đơn hàng có quá 3 ngày không
      const orderCreatedTime = order.createdAt.getTime();
      const currentTime = Date.now();
      const timeDiff = currentTime - orderCreatedTime;
      const daysDiff = timeDiff / (1000 * 60 * 60 * 24);

      // Nếu đơn hàng được tạo hơn 3 ngày, trả về mảng rỗng với thông báo
      if (
        daysDiff > 3 ||
        orderCreatedTime + MEMORY_EXPIRE_TIME * 1000 < currentTime
      ) {
        // Trả về một phần tử với thông báo thay vì mảng rỗng
        return [
          {
            id: '',
            fileUrl: '',
            fileName: '',
            orderId: orderId,
            orderCreatedAt: order.createdAt.toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            message: `Order with ID ${orderId} has expired. Images are only available for 3 days after order creation.`,
          },
        ];
      }

      let clientName: string | null = null;
      let appearanceSetting: AppearanceSetting | null = null;

      if (order.machine?.userId) {
        const [client, appearance] = await Promise.all([
          this.clientRepository.findOne({
            where: { id: order.machine.userId },
          }),
          this.appearanceSettingRepository.findOne({
            where: { clientId: order.machine.userId },
          }),
        ]);

        if (client) {
          clientName = client.name;
          appearanceSetting = appearance || null;
        }
      }

      // ✅ Lấy danh sách hình ảnh
      const images = await this.imageRepository.find({ where: { orderId } });

      // Nếu không có ảnh nào, trả về thông báo đang chờ upload ảnh
      if (images.length === 0) {
        return [
          {
            id: '',
            fileUrl: '',
            fileName: '',
            orderId: orderId,
            orderCreatedAt: order.createdAt.toISOString(),
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            message:
              'Waiting for image upload. No images available for this order yet.',
          },
        ];
      }

      // ✅ Trả về danh sách ảnh kèm clientName và appearanceSetting
      return images.map((image) => ({
        id: image.id,
        fileUrl: image.fileUrl,
        fileName: image.fileName,
        fileType: image.fileType,
        orderId: image.orderId,
        clientName,
        appearanceSetting,
        createdAt: image.createdAt.toISOString(),
        updatedAt: image.updatedAt.toISOString(),
        orderCreatedAt: order.createdAt.toISOString(),
      }));
    } catch (error) {
      // Nếu lỗi không phải BadRequestException, ném lại lỗi
      if (!(error instanceof BadRequestException)) {
        throw error;
      }

      // Nếu là lỗi BadRequestException, trả về thông báo lỗi
      return [
        {
          id: '',
          fileUrl: '',
          fileName: '',
          orderId: orderId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          message: error.message || 'An error occurred while fetching images',
        },
      ];
    }
  }
}
