import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppImage } from '../../clientApp/uploadImage/image.entity';
import { ImageResolver } from './image.resolver';
import { ImageService } from './image.service';
import { Order } from '../../clientApp/order/order.entity';
import { Client } from '../../common/entity/client.entity';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { AppearanceSetting } from '../../common/entity/appearanceSetting.entity';
@Module({
  imports: [
    TypeOrmModule.forFeature([AppImage, Order, Client, AppearanceSetting]),
  ],
  providers: [JwtService, SBLogger, ImageResolver, ImageService],
})
export class ImageModule {}
