import { Field, ObjectType, ID } from '@nestjs/graphql';
import { AppearanceSetting } from '../../common/entity/appearanceSetting.entity';

@ObjectType()
export class ImageRes {
  @Field(() => ID)
  id: string;

  @Field()
  fileUrl: string;

  @Field()
  fileName: string;

  @Field({ nullable: true })
  fileType?: string;

  @Field({ nullable: true })
  orderId?: string;

  @Field({ nullable: true })
  clientName?: string;

  @Field(() => AppearanceSetting, { nullable: true })
  appearanceSetting?: AppearanceSetting;

  @Field()
  createdAt: string;

  @Field()
  updatedAt: string;

  @Field({ nullable: true })
  orderCreatedAt?: string;

  @Field({ nullable: true })
  message?: string;
}
