enum EImageFileType {
  IMAGE
  IMAGE_FINAL
  VIDEO
}

type AppearanceSetting {
  id: ID!
  logo: String
  background: String
  primary_color: String!
  secondary_color: String!
  background_color: String!
  primary_text_color: String!
  secondary_text_color: String!
  secondary_text_color_2: String!
  clientId: ID!
  createdAt: String!
  updatedAt: String!
}

type Image {
  id: ID!
  fileUrl: String!
  fileName: String!
  fileType: EImageFileType
  orderId: ID
  clientName: String!
  appearanceSetting: AppearanceSetting
  createdAt: String!
  updatedAt: String!
}

type Query {
  clientMemoryGetImages(orderId: String!): [Image!]
}