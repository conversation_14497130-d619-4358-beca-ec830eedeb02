import { Resolver, Query, Args } from '@nestjs/graphql';
import { ImageService } from './image.service';
import { ImageRes } from './image.input';

@Resolver()
export class ImageResolver {
  constructor(private readonly imageService: ImageService) {}

  @Query(() => [ImageRes])
  async clientMemoryGetImages(
    @Args('orderId') orderId: string,
  ): Promise<ImageRes[]> {
    try {
      return await this.imageService.getImagesByOrderId(orderId);
    } catch (error) {
      // Xử lý lỗi một cách nhẹ nhàng, trả về thông báo lỗi thay vì ném lỗi
      console.error(
        `Error fetching images for order ${orderId}:`,
        error.message,
      );

      // Trả về một phần tử với thông báo lỗi
      return [
        {
          id: '',
          fileUrl: '',
          fileName: '',
          orderId: orderId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          message: error.message || 'An error occurred while fetching images',
        },
      ];
    }
  }
}
