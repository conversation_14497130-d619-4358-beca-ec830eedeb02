import { Injectable, Logger, Scope } from '@nestjs/common';
import { getConsoleLogger, maskSensitiveData } from '../../utils/consoleLogger';
import {
  getCurrentRequestId,
  setCurrentRequestId,
} from '../../middlewares/request-id.middleware';

@Injectable({ scope: Scope.TRANSIENT })
export class SBLogger {
  private readonly logger = new Logger('SnapBox-API', { timestamp: true });
  private readonly jsonLogger = getConsoleLogger('jsonLogging');
  private readonly sqlLogger = new Logger('SQL');
  private requestId: string = 'no-request-id';
  private userId: string = 'anonymous';

  /**
   * Set the request ID for this logger instance
   */
  setRequestId(requestId: string): void {
    this.requestId = requestId;
    this.jsonLogger.addContext('requestId', requestId);

    // Also set the global request ID for error handling
    setCurrentRequestId(requestId);
  }

  /**
   * Set the user ID for this logger instance
   */
  setUserId(userId: string): void {
    this.userId = userId;
    this.jsonLogger.addContext('userId', userId);
  }

  /**
   * Write a 'log' level log with enhanced context
   */
  log(type: string, payload?: any, context?: any): void {
    // Get the current request ID from the global context
    const requestId = getCurrentRequestId();

    // If payload contains a requestId, use that instead
    const payloadRequestId = payload?.requestId;
    const finalRequestId = payloadRequestId || requestId;

    // Update the instance requestId if needed
    if (finalRequestId !== this.requestId) {
      this.setRequestId(finalRequestId);
    }

    const maskedPayload = payload ? maskSensitiveData(payload) : null;
    const logMessage = `[reqId:${finalRequestId}] ${type}: ${maskedPayload ? JSON.stringify(maskedPayload) : ''}`;
    this.logger.log(logMessage);

    // Also log in structured JSON format - manually stringify the JSON
    this.jsonLogger.info(
      JSON.stringify({
        timestamp: new Date().toISOString(),
        level: 'info',
        type,
        requestId: finalRequestId,
        userId: this.userId,
        payload: maskedPayload,
        context: context ? maskSensitiveData(context) : null,
      }),
    );
  }

  /**
   * Write an 'error' level log with enhanced context
   */
  error(type: string, error: any, context?: any): void {
    // Get the current request ID from the global context
    const requestId = getCurrentRequestId();

    let errorMessage = error;
    let errorStack = null;

    if (error instanceof Error) {
      errorMessage = error.message;
      errorStack = error.stack;
    }

    // If context contains a requestId, use that instead
    const contextRequestId = context?.requestId;
    const finalRequestId = contextRequestId || requestId;

    // Update the instance requestId if needed
    if (finalRequestId !== this.requestId) {
      this.setRequestId(finalRequestId);
    }

    const logMessage = `[reqId:${finalRequestId}] ${type}: ${errorMessage}`;
    this.logger.error(logMessage);

    // Also log in structured JSON format - manually stringify the JSON
    this.jsonLogger.error(
      JSON.stringify({
        timestamp: new Date().toISOString(),
        level: 'error',
        type,
        requestId: finalRequestId,
        userId: this.userId,
        error: errorMessage,
        stack: errorStack,
        context: context ? maskSensitiveData(context) : null,
      }),
    );
  }

  /**
   * Write a 'warn' level log with enhanced context
   */
  warn(type: string, payload?: any, context?: any): void {
    // Get the current request ID from the global context
    const requestId = getCurrentRequestId();

    // If payload contains a requestId, use that instead
    const payloadRequestId = payload?.requestId;
    const finalRequestId = payloadRequestId || requestId;

    // Update the instance requestId if needed
    if (finalRequestId !== this.requestId) {
      this.setRequestId(finalRequestId);
    }

    const maskedPayload = payload ? maskSensitiveData(payload) : null;
    const logMessage = `[reqId:${finalRequestId}] ${type}: ${maskedPayload ? JSON.stringify(maskedPayload) : ''}`;
    this.logger.warn(logMessage);

    // Also log in structured JSON format - manually stringify the JSON
    this.jsonLogger.warn(
      JSON.stringify({
        timestamp: new Date().toISOString(),
        level: 'warn',
        type,
        requestId: finalRequestId,
        userId: this.userId,
        payload: maskedPayload,
        context: context ? maskSensitiveData(context) : null,
      }),
    );
  }

  /**
   * Write a 'debug' level log with enhanced context
   */
  debug(type: string, payload?: any, context?: any): void {
    // Get the current request ID from the global context
    const requestId = getCurrentRequestId();

    // If payload contains a requestId, use that instead
    const payloadRequestId = payload?.requestId;
    const finalRequestId = payloadRequestId || requestId;

    // Update the instance requestId if needed
    if (finalRequestId !== this.requestId) {
      this.setRequestId(finalRequestId);
    }

    const maskedPayload = payload ? maskSensitiveData(payload) : null;
    const logMessage = `[reqId:${finalRequestId}] ${type}: ${maskedPayload ? JSON.stringify(maskedPayload) : ''}`;
    this.logger.debug(logMessage);

    // Also log in structured JSON format - manually stringify the JSON
    this.jsonLogger.debug(
      JSON.stringify({
        timestamp: new Date().toISOString(),
        level: 'debug',
        type,
        requestId: finalRequestId,
        userId: this.userId,
        payload: maskedPayload,
        context: context ? maskSensitiveData(context) : null,
      }),
    );
  }

  /**
   * Log SQL queries with enhanced context
   * This method can be used for manual SQL logging if needed
   */
  sql(query: string, parameters?: any[], executionTime?: number): void {
    // Get the current request ID from the global context
    const requestId = getCurrentRequestId();

    // ANSI color codes for terminal output
    const cyan = '\x1b[36m';
    const reset = '\x1b[0m';

    let message = `[reqId:${requestId}] ${cyan}${query}${reset}`;

    // Add parameters if available
    if (parameters && parameters.length) {
      message += ` -- Parameters: ${JSON.stringify(parameters)}`;
    }

    // Add execution time if available
    if (executionTime !== undefined) {
      message += ` -- Execution time: ${executionTime}ms`;
    }

    this.sqlLogger.log(message);

    // Also log in structured JSON format
    this.jsonLogger.info(
      JSON.stringify({
        timestamp: new Date().toISOString(),
        level: 'info',
        type: 'SQL',
        requestId,
        userId: this.userId,
        query,
        parameters,
        executionTime,
      }),
    );
  }
}
