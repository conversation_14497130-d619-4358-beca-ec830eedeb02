import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';

@Injectable()
export class HealthService {
  constructor(
    @InjectDataSource() private dataSource: DataSource,
  ) {}

  async check() {
    try {
      // Ki<PERSON>m tra kết nối database
      const dbStatus = await this.checkDatabase();
      
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        checks: {
          database: dbStatus ? 'up' : 'down',
        },
        version: process.env.npm_package_version || '1.0.0',
        uptime: process.uptime(),
      };
    } catch (error) {
      return {
        status: 'error',
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  private async checkDatabase(): Promise<boolean> {
    try {
      await this.dataSource.query('SELECT 1');
      return true;
    } catch {
      return false;
    }
  }
}