import { Resolver, Args, Context, Mutation } from '@nestjs/graphql';
import { AuthService } from './auth.service';
import { Auth } from '../../common/entity/auth.entity';
import { SignInByCodeInput, AppUser } from './auth.input';
import { ExecutionContext } from '@nestjs/common';

@Resolver(() => AppUser)
export class AuthResolver {
  constructor(private authService: AuthService) {}

  @Mutation(() => Auth)
  async clientAppSignOut(@Context() ctx: ExecutionContext): Promise<Auth> {
    const token = await this.authService.getToken(ctx);
    return this.authService.signOut(token);
  }

  @Mutation(() => AppUser)
  async clientAppSignInByCode(
    @Args('input') input: SignInByCodeInput,
  ): Promise<AppUser> {
    return this.authService.signInByCode(input);
  }
}
