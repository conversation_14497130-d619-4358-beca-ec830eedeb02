import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { Auth } from '../../common/entity/auth.entity';
import { SignInByCodeInput, AppUser } from './auth.input';
import * as bcrypt from 'bcrypt';
import { jwtConstants } from 'src/constants';
import { SBLogger } from '../../logger/logger.service';
import { Client } from 'src/modules/common/entity/client.entity';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { MachineBrand } from 'src/modules/admin/machineBrand/machineBrand.entity';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(Auth)
    private authRepository: Repository<Auth>,
    @InjectRepository(Client)
    private clientsService: Repository<Client>,
    @InjectRepository(Machine)
    private machinesService: Repository<Machine>,
    @InjectRepository(MachineBrand)
    private machineBrandService: Repository<MachineBrand>,
    private jwtService: JwtService,
    private loggerService: SBLogger,
  ) {}

  getToken(ctx: any): string {
    let token = '';
    const authHeader = ctx.req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
    }
    return token;
  }

  async getCurrentUser(token: string): Promise<Client> {
    try {
      const decoded = await this.jwtService.verifyAsync(token, jwtConstants);
      this.loggerService.log('getCurrentUser', decoded);
      return decoded;
    } catch (err) {
      this.loggerService.log('getCurrentUser - error', err);
      throw new UnauthorizedException();
    }
  }

  async comparePassword(plainTextPassword, hashedPassword) {
    const isMatch = await bcrypt.compare(plainTextPassword, hashedPassword);
    return isMatch;
  }

  async signOut(token: string): Promise<Auth> {
    const auth = await this.authRepository.findOneBy({ token });
    if (!auth) {
      this.loggerService.log('ERROR - signOut', auth);
      throw new NotFoundException();
    }
    const res = await this.authRepository.remove(auth);
    return res;
  }

  async signInByCode(request: SignInByCodeInput): Promise<AppUser> {
    try {
      if (!request.machineCode || !request.machinePin) {
        throw new BadRequestException();
      }
      const machine = await this.machinesService.findOne({
        where: { machineCode: request.machineCode },
        relations: ['machineBrand'],
      });
      if (!machine) throw new NotFoundException();

      const isValidPin = request.machinePin === machine.machinePin;
      if (!isValidPin) throw new UnauthorizedException();

      const client = await this.clientsService.findOneBy({
        id: machine.userId,
      });

      if (!client) throw new NotFoundException();
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const code = machine.machineBrand?.code || '';

      const result = {
        ...client,
        machineId: machine.id,
        machineCode: machine.machineCode,
      };
      const token = await this.jwtService.signAsync(result, jwtConstants);

      return {
        id: result.id,
        machineId: result.machineId,
        token: token,
        code: code,
      };
    } catch (error) {
      this.loggerService.log('ERROR - signInByCode', error);
      throw new UnauthorizedException(error.message);
    }
  }
}
