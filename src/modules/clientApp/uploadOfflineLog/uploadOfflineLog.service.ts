import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SBLogger } from '../../logger/logger.service';
import { FileUpload } from 'graphql-upload';
import * as AdmZip from 'adm-zip';
import * as csvParser from 'csv-parser';
import { Readable } from 'stream';
import { AppOrderService } from '../../common/module/appOrder/appOrder.service';
import { Order } from '../order/order.entity';
import { AppImage } from '../uploadImage/image.entity';
import {
  EOrderAppStatus,
  EPaymentMethod,
  EImageFileType,
} from '../../../enum/index';
import { FileService } from '../../common/module/file/file.service';
import * as path from 'path';

@Injectable()
export class UploadOfflineLogService {
  constructor(
    private loggerService: SBLogger,
    private readonly fileService: FileService,
    private readonly appOrderService: AppOrderService,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(AppImage)
    private readonly imageRepository: Repository<AppImage>,
  ) {}

  async perform(
    userId: string,
    machineId: string,
    file: FileUpload,
  ): Promise<string> {
    this.validateFileType(file.mimetype);

    const zipEntries = this.getZipEntries(
      await this.readStreamToBuffer(file.createReadStream()),
    );
    await this.processCsvEntries(zipEntries, userId, machineId);

    return 'ZIP file processed successfully';
  }

  private validateFileType(mimetype: string): void {
    if (mimetype !== 'application/zip') {
      throw new BadRequestException(
        'Invalid file type. Only ZIP files are allowed.',
      );
    }
  }

  private async readStreamToBuffer(stream: Readable): Promise<Buffer> {
    const chunks: Buffer[] = [];
    for await (const chunk of stream) chunks.push(chunk);
    return Buffer.concat(chunks);
  }

  private getZipEntries(buffer: Buffer): AdmZip.IZipEntry[] {
    return new AdmZip(buffer).getEntries();
  }

  private async processCsvEntries(
    zipEntries: AdmZip.IZipEntry[],
    userId: string,
    machineId: string,
  ): Promise<void> {
    const csvEntries = zipEntries.filter((entry) =>
      entry.entryName.endsWith('.csv'),
    );
    for (const entry of csvEntries) {
      const csvStream = Readable.from(entry.getData());
      await this.processCsvStream(csvStream, userId, machineId, zipEntries);
    }
  }

  private async processCsvStream(
    stream: Readable,
    userId: string,
    machineId: string,
    zipEntries: AdmZip.IZipEntry[],
  ): Promise<void> {
    const orders = await this.parseCsvStream(stream);
    await this.saveOrders(orders, userId, machineId, zipEntries);
  }

  private async parseCsvStream(stream: Readable): Promise<any[]> {
    const orders: any[] = [];
    return new Promise((resolve, reject) => {
      stream
        .pipe(csvParser())
        .on('data', (row) => orders.push(this.mapRowToOrder(row)))
        .on('end', () => resolve(orders))
        .on('error', reject);
    });
  }

  private mapRowToOrder(row: any): any {
    return {
      settingSizeId: row.settingSizeId || '',
      settingSizeKey: row.settingSizeKey || '',
      topicId: row.topicId || '',
      frameId: row.frameId || '',
      receivedAmount: row.receivedAmount || null,
      captureMode: row.captureMode || '',
    };
  }

  private async saveOrders(
    orders: any[],
    userId: string,
    machineId: string,
    zipEntries: AdmZip.IZipEntry[],
  ): Promise<void> {
    const directoriesWithFiles = this.extractDirectoriesWithFiles(zipEntries);

    for (const [index, order] of orders.entries()) {
      const savedOrder = await this.createOrder(order, userId, machineId);

      if (savedOrder) {
        const directoryName = `data_log/data-${index}/`;
        const correspondingDirectory = directoriesWithFiles.find(
          (dir) => dir.name === directoryName,
        );

        if (correspondingDirectory) {
          await this.uploadMediaFiles(
            savedOrder.id,
            correspondingDirectory,
            zipEntries,
          );
        }
      }
    }
  }

  private async createOrder(
    order: any,
    userId: string,
    machineId: string,
  ): Promise<Order> {
    const { totalPrice, settingSize, topic, frame } =
      await this.appOrderService.prepareOrderDependencies(
        order,
        userId,
        machineId,
        true, // Enable validation for offline payments
      );

    const orderCode = await this.appOrderService.generateQRCode(
      machineId,
      userId,
    );

    const newOrder = this.orderRepository.create({
      orderCode,
      refCode: '',
      amount: totalPrice,
      receivedAmount: order.receivedAmount,
      description: '',
      status: EOrderAppStatus.DELIVERED,
      paymentMethod: EPaymentMethod.OFFLINE,
      machineId,
      clientId: userId,
      settingSizeId: settingSize?.id,
      frameId: frame?.id,
      topicId: topic?.id,
      imageNumber: this.appOrderService.extractNumbersFromString(
        order.settingSizeKey,
      ),
      captureMode: order.captureMode,
      totalOrderNumber: totalPrice,
    });

    return this.orderRepository.save(newOrder);
  }

  private async uploadMediaFiles(
    orderId: string,
    directory: { name: string; files: string[] },
    zipEntries: AdmZip.IZipEntry[],
  ): Promise<void> {
    if (!directory.files.length) return;

    const uploadedImages: AppImage[] = await Promise.all(
      directory.files.map(async (fileName) => {
        const fileEntry = zipEntries.find(
          (entry) => entry.entryName === fileName,
        );
        if (!fileEntry) return null;

        return this.processFileEntry(fileEntry, orderId);
      }),
    );

    const validImages = uploadedImages.filter(
      (img) => img !== null,
    ) as AppImage[];

    if (validImages.length > 0) {
      await this.imageRepository.save(validImages);
    }
  }

  private extractDirectoriesWithFiles(
    entries: AdmZip.IZipEntry[],
  ): Array<{ name: string; files: string[] }> {
    return entries
      .filter((entry) => entry.isDirectory)
      .map((entry) => ({
        name: entry.entryName,
        files: entries
          .filter(
            (fileEntry) =>
              !fileEntry.isDirectory &&
              fileEntry.entryName.startsWith(entry.entryName) &&
              (fileEntry.entryName.endsWith('.jpg') ||
                fileEntry.entryName.endsWith('.png') ||
                fileEntry.entryName.endsWith('.webm')),
          )
          .map((fileEntry) => fileEntry.entryName),
      }))
      .filter((dir) => dir.files.length > 0);
  }

  private async processFileEntry(
    fileEntry: AdmZip.IZipEntry,
    orderId: string,
  ): Promise<AppImage | null> {
    const fileBuffer = fileEntry.getData();
    const fileName = fileEntry.entryName;
    const fileType = this.detectTypeImage(fileName);

    if (!fileType) return null;

    if (fileType === EImageFileType.VIDEO && fileName.endsWith('.mp4')) {
      const uploadedUrl = await this.fileService.singleUpload(
        fileBuffer,
        fileName,
        `appImage/${orderId}`,
      );
      return this.imageRepository.create({
        fileName: fileName,
        fileUrl: uploadedUrl,
        fileType,
        orderId,
      });
    }

    const uploadedUrl = await this.fileService.singleUpload(
      fileBuffer,
      fileName,
      `appImage/${orderId}`,
    );

    return this.imageRepository.create({
      fileName,
      fileUrl: uploadedUrl,
      fileType,
      orderId,
    });
  }

  private detectTypeImage(fileName: string): EImageFileType | null {
    const baseName = path.basename(fileName).trim();
    if (baseName.startsWith('flipped_')) return EImageFileType.IMAGE;
    if (baseName.includes('merged-image-')) return EImageFileType.IMAGE_FINAL;
    if (baseName.startsWith('timelapse_')) return EImageFileType.VIDEO;
    return null;
  }
}
