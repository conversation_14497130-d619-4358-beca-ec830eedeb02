import { Resolver, Mutation, Args, Context } from '@nestjs/graphql';
import { UploadOfflineLogService } from './uploadOfflineLog.service';
import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { UseGuards } from '@nestjs/common';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { UploadResponse } from './uploadOfflineLog.input';

@Resolver()
@UseGuards(ClientAppAuthGuard)
export class UploadOfflineLogResolver {
  constructor(
    private readonly uploadOfflineLogService: UploadOfflineLogService,
  ) {}

  @Mutation(() => UploadResponse)
  async clientAppUploadOfflineLog(
    @Args({ name: 'file', type: () => GraphQLUpload }) file: FileUpload,
    @Context() context: any,
  ): Promise<UploadResponse> {
    const { id: userId, machineId } = context.req.user;
    const message = await this.uploadOfflineLogService.perform(
      userId,
      machineId,
      file,
    );
    return { message };
  }
}
