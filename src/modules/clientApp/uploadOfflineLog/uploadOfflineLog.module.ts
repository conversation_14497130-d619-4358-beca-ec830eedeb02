import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppImage } from '../uploadImage/image.entity';
import { UploadOfflineLogResolver } from './uploadOfflineLog.resolver';
import { UploadOfflineLogService } from './uploadOfflineLog.service';
import { FileService } from 'src/modules/common/module/file/file.service';
import { Order } from '../order/order.entity';
import { SBLogger } from 'src/modules/logger/logger.service';
import { JwtService } from '@nestjs/jwt';
import { AppOrderModule } from '../../common/module/appOrder/appOrder.module';

@Module({
  imports: [TypeOrmModule.forFeature([AppImage, Order]), AppOrderModule],
  providers: [
    JwtService,
    SBLogger,
    UploadOfflineLogResolver,
    UploadOfflineLogService,
    FileService,
  ],
})
export class UploadOfflineLogModule {}
