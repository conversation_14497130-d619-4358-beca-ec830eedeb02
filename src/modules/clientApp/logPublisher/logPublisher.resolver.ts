import { Resolver, Mutation, Args, Context } from '@nestjs/graphql';
import { LogPublisherService } from './logPublisher.service';
import { LogPublisherInput, LogResponse } from './logPublisher.input';
import { UseGuards } from '@nestjs/common';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';

@Resolver()
export class LogPublisherResolver {
  constructor(private readonly logPublisherService: LogPublisherService) {}

  @Mutation(() => LogResponse, { name: 'clientAppLogPublisher' })
  @UseGuards(ClientAppAuthGuard)
  async clientAppLogPublisher(
    @Args('input') logPublisherInput: LogPublisherInput,
    @Context() context: any,
  ): Promise<LogResponse> {
    const { id: userId, machineId, machineCode } = context.req.user;

    await this.logPublisherService.createLog(
      logPublisherInput,
      userId,
      machineId,
      machineCode,
    );

    return {
      status: 'success',
      message: 'Log published with authentication',
    };
  }

  @Mutation(() => LogResponse, { name: 'clientApplogWithoutAuth' })
  async clientApplogWithoutAuth(
    @Args('input') logPublisherInput: LogPublisherInput,
  ): Promise<LogResponse> {
    await this.logPublisherService.createLog(
      logPublisherInput,
      'anonymous',
      null,
      null,
    );

    return {
      status: 'success',
      message: 'Log published without authentication',
    };
  }
}
