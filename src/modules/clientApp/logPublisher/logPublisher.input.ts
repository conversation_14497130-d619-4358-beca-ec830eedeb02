import { InputType, Field, ObjectType } from '@nestjs/graphql';

@InputType()
export class LogPublisherInput {
  @Field(() => String, { description: 'The log message to be recorded' })
  message: string;

  @Field(() => String, {
    nullable: true,
    description: 'The backtrace or stack trace related to the log',
  })
  backtrace?: string;
}

@ObjectType()
export class LogResponse {
  @Field(() => String, { description: 'Status of the log operation' })
  status: string;

  @Field(() => String, { description: 'Message about the log operation' })
  message: string;
}
