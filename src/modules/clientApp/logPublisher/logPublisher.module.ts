import { Modu<PERSON> } from '@nestjs/common';
import { LogPublisherResolver } from './logPublisher.resolver';
import { LogPublisherService } from './logPublisher.service';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { JwtService } from '@nestjs/jwt';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { SBLogger } from 'src/modules/logger/logger.service';

@Module({
  providers: [
    JwtService,
    LogPublisherResolver,
    LogPublisherService,
    FirestoreService,
    ClientAppAuthGuard,
    SBLogger,
  ],
})
export class LogPublisherModule {}
