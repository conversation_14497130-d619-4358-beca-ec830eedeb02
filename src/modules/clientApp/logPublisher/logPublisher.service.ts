import { Injectable } from '@nestjs/common';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { LogPublisherInput } from './logPublisher.input';

@Injectable()
export class LogPublisherService {
  constructor(private readonly firestoreService: FirestoreService) {}

  async createLog(
    logPublisherInput: LogPublisherInput,
    userId: string,
    machineId: string | null,
    machineCode: string | null,
  ): Promise<void> {
    const { message, backtrace } = logPublisherInput;

    const logData = {
      message,
      backtrace: backtrace || null,
      userId,
      machineId,
      machineCode,
      timestamp: new Date().toISOString(),
    };

    const documentId = `${Date.now()}`;
    await this.firestoreService.pushData('logs', documentId, logData, false);
  }
}
