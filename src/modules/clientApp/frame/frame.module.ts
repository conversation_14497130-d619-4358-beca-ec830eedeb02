import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Frame } from '../../common/entity/frame.entity';
import { Client } from '../../common/entity/client.entity';
import { FileService } from 'src/modules/common/module/file/file.service';
import { FrameService } from './frame.service';
import { FrameResolver } from './frame.resolver';
import { Layout } from '../../common/entity/layout.entity';
import { LayoutItem } from '../../common/entity/layoutItem.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Frame, Client, Layout, LayoutItem])],
  providers: [FrameService, FrameResolver, JwtService, SBLogger, FileService],
  exports: [FrameService],
})
export class FrameModule {}
