import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { SBLogger } from '../../logger/logger.service';
import { Frame } from '../../common/entity/frame.entity';
import { Layout } from '../..//common/entity/layout.entity';
import { ELayoutFormatStatus, ELayoutStatus, ELayoutType } from 'src/enum';

@Injectable()
export class FrameService {
  constructor(
    @InjectRepository(Frame)
    private frameRepository: Repository<Frame>,
    @InjectRepository(Layout)
    private layoutRepository: Repository<Layout>,
    private loggerService: SBLogger,
  ) {}

  async findAll(clientId: string, machineId?: string): Promise<Frame[]> {
    const topicIds = await this.getListTopicIds(clientId, machineId);

    const whereCondition: any = { clientId };
    if (topicIds.length > 0) {
      whereCondition.topicId = In(topicIds);
    }
    const result = await this.frameRepository.find({
      where: whereCondition,
      relations: ['frameItems'],
    });
    return result;
  }

  async findOne(id: string, clientId: string): Promise<Frame> {
    const result = await this.frameRepository.findOne({
      where: { id, clientId },
    });
    return result;
  }

  async getListTopicIds(
    clientId: string,
    machineId: string,
  ): Promise<string[]> {
    const customLayout = await this.layoutRepository
      .createQueryBuilder('layout')
      .leftJoinAndSelect('layout.formats', 'layoutFormat')
      .leftJoinAndSelect('layoutFormat.layoutItems', 'layoutItem')
      .where('layout.clientId = :clientId', { clientId })
      .andWhere('layout.layoutType = :layoutType', {
        layoutType: ELayoutType.Custom,
      })
      .andWhere('layout.status = :layoutStatus', {
        layoutStatus: ELayoutStatus.ACTIVE,
      })
      .andWhere('layoutFormat.status = :formatStatus', {
        formatStatus: ELayoutStatus.ACTIVE,
      })
      .andWhere('layout.machineIds = :machineId', { machineId })
      .getOne();

    if (customLayout) {
      const activeFormat = customLayout.formats.find(
        (format) => format.status === ELayoutFormatStatus.ACTIVE,
      );

      const resolvedLayoutItems = await activeFormat.layoutItems;
      if (activeFormat) {
        return resolvedLayoutItems.map((item) => item.topicId);
      }
    }

    const defaultLayout = await this.layoutRepository
      .createQueryBuilder('layout')
      .leftJoinAndSelect('layout.formats', 'layoutFormat')
      .leftJoinAndSelect('layoutFormat.layoutItems', 'layoutItem')
      .where('layout.clientId = :clientId', { clientId })
      .andWhere('layout.layoutType = :layoutType', {
        layoutType: ELayoutType.Default,
      })
      .andWhere('layout.status = :layoutStatus', {
        layoutStatus: ELayoutStatus.ACTIVE,
      })
      .andWhere('layoutFormat.status = :formatStatus', {
        formatStatus: ELayoutStatus.ACTIVE,
      })
      .getOne();

    if (defaultLayout) {
      const activeFormat = defaultLayout.formats.find(
        (format) => format.status === ELayoutFormatStatus.ACTIVE,
      );
      if (activeFormat) {
        return activeFormat.layoutItems.map((item) => item.topicId);
      }
    }
    return [];
  }
}
