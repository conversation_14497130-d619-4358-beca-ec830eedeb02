import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { FrameService } from './frame.service';
import { Frame } from '../../common/entity/frame.entity';
import { UseGuards } from '@nestjs/common';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';

@Resolver(() => Frame)
@UseGuards(ClientAppAuthGuard)
export class FrameResolver {
  constructor(private readonly frameService: FrameService) {}

  @Query(() => [Frame], { nullable: true })
  async clientAppGetFrames(@Context() context: any): Promise<Frame[]> {
    const clientId = context.req.user.id;
    return this.frameService.findAll(clientId, context.req.user.machineId);
  }

  @Query(() => Frame, { nullable: true })
  async clientAppGetFrame(
    @Context() context: any,
    @Args('id') id: string,
  ): Promise<Frame> {
    const clientId = context.req.user.id;
    return this.frameService.findOne(id, clientId);
  }
}
