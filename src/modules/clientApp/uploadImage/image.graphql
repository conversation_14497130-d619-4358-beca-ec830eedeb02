scalar Upload

enum ECaptureMode {
  AUTO
  MANUAL
}

input CreateImageInput {
  orderId: String!
  captureMode: ECaptureMode
}

type UploadResponse {
  message: String!
  captureMode: ECaptureMode!
  domain: String!
}

type CreateTimeLapseResponse {
  isSuccess: Boolean!
}

type Mutation {
  clientAppCreateImage(input: CreateImageInput!, file: Upload): UploadResponse!
  clientAppCreateTimeLapse(
    input: CreateImageInput!
    file: Upload
  ): CreateTimeLapseResponse!
}
