import { Field, ID, ObjectType } from '@nestjs/graphql';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
} from 'typeorm';
import { Order } from '../order/order.entity';
import { EImageFileType } from '../../../enum/index';

@ObjectType()
@Entity('app_image')
export class AppImage {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field()
  @Column({ type: 'varchar', length: 255 })
  fileUrl: string;

  @Field()
  @Column({ type: 'varchar', length: 255 })
  fileName: string;

  @Column({
    type: 'enum',
    enum: EImageFileType,
    default: EImageFileType.IMAGE,
  })
  @Field({ nullable: true })
  fileType: EImageFileType;

  @Field(() => ID, { nullable: true })
  @Column({ type: 'uuid', nullable: true })
  orderId: string;

  @Field()
  @CreateDateColumn()
  createdAt: Date;

  @Field()
  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => Order, (order) => order.images, { onDelete: 'CASCADE' })
  order: Order;
}
