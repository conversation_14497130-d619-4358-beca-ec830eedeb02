import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { FileJob } from './file-job.entity';
import { AppImage } from './image.entity';
import { FileProcessorProcessor } from './file-processor.processor';
import { ImageQueueService } from './image.service.queue';
import { JobStatusResolver } from './job-status.resolver';
import { FileService } from '../../common/module/file/file.service';
import { FirestoreService } from '../../common/module/firestore/firestore.service';
import { FilePool } from '../../common/utils/FilePool';
import { LockManager } from '../../common/utils/LockManager';

const redisConfig = {
  host: process.env.REDIS_HOST,
  port: Number(process.env.REDIS_PORT),
  username: process.env.REDIS_USERNAME,
  password: process.env.REDIS_PASSWORD,
};

@Module({
  imports: [
    TypeOrmModule.forFeature([FileJob, AppImage]),
    BullModule.registerQueue({
      name: 'file-processing',
      redis: {
        ...redisConfig,
        connectTimeout: 30000, // Tăng timeout kết nối
        maxRetriesPerRequest: 10, // Tăng số lần thử lại
        enableReadyCheck: false,
        enableOfflineQueue: true, // Cho phép hàng đợi ngoại tuyến
        retryStrategy: (times) => {
          console.log(`Redis retry attempt: ${times}`);
          return Math.min(times * 2000, 30000); // Tăng thời gian giữa các lần thử lại
        },
        reconnectOnError: (err) => {
          console.log(`Redis connection error: ${err.message}`);
          // Thử kết nối lại cho tất cả các lỗi
          return true;
        },
      },
      defaultJobOptions: {
        attempts: 5, // Số lần thử lại
        backoff: {
          type: 'exponential', // Tăng thời gian giữa các lần thử lại theo cấp số nhân
          delay: 5000, // Thời gian chờ ban đầu (5 giây)
        },
        removeOnComplete: true, // Xóa job khi hoàn thành
        removeOnFail: false, // Giữ lại job khi thất bại để kiểm tra
      },
    }),
  ],
  providers: [
    FileProcessorProcessor,
    ImageQueueService,
    JobStatusResolver,
    FileService,
    FirestoreService,
    FilePool,
    LockManager,
  ],
  exports: [ImageQueueService],
})
export class UploadQueueModule {}
