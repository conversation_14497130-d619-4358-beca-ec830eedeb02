import { Process, Processor } from '@nestjs/bull';
import { <PERSON><PERSON>, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import { Job } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AppImage } from './image.entity';
import { FileService } from '../../common/module/file/file.service';
import { FirestoreService } from '../../common/module/firestore/firestore.service';
import * as AdmZip from 'adm-zip';
import * as fs from 'fs';
import { EImageFileType } from '../../../enum';
import { FileJob, FileJobStatus } from './file-job.entity';
import { FilePool } from '../../common/utils/FilePool';
import { LockManager } from '../../common/utils/LockManager';

@Processor('file-processing')
export class FileProcessorProcessor implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(FileProcessorProcessor.name);
  private readonly filePool: FilePool;
  private readonly lockManager: LockManager;

  constructor(
    @InjectRepository(AppImage)
    private readonly imageRepository: Repository<AppImage>,
    @InjectRepository(FileJob)
    private readonly fileJobRepository: Repository<FileJob>,
    private readonly fileService: FileService,
    private readonly firestoreService: FirestoreService,
  ) {
    this.filePool = new FilePool();
    this.lockManager = new LockManager();
    this.logger.log('FileProcessorProcessor initialized');
  }

  onModuleInit() {
    // Any initialization if needed
  }

  onModuleDestroy() {
    this.filePool.destroy();
  }

  @Process('process-zip-file')
  async processZipFile(
    job: Job<{
      tempFilePath: string;
      orderId: string;
      jobId: string;
    }>,
  ) {
    const { tempFilePath, orderId } = job.data;
    this.logger.log(
      `[Queue] Starting background processing of ZIP file: ${tempFilePath} for order ${orderId}`,
    );

    try {
      // Kiểm tra xem file tạm có tồn tại không
      if (!fs.existsSync(tempFilePath)) {
        throw new Error(`Temporary file not found: ${tempFilePath}`);
      }

      // Lấy kích thước file
      const stats = fs.statSync(tempFilePath);
      this.logger.log(
        `[Queue] Processing ZIP file from disk (${(stats.size / 1024 / 1024).toFixed(2)}MB)`,
      );

      // Xử lý file ZIP từ đĩa
      const zip = new AdmZip(tempFilePath);
      const entries = zip
        .getEntries()
        .filter((entry: AdmZip.IZipEntry) => !entry.isDirectory);

      this.logger.log(`[Queue] Found ${entries.length} files in ZIP`);

      // Cập nhật tiến trình
      await job.progress(10);

      // Lấy hình ảnh hiện có
      const existingImages = await this.imageRepository.find({
        where: { orderId },
        select: ['fileName', 'fileType'],
      });

      const existingImageMap = new Map<string, boolean>();
      existingImages.forEach((img) => {
        const key = `${img.fileName}_${img.fileType}`;
        existingImageMap.set(key, true);
      });

      // Xử lý theo batch nhỏ hơn
      const batchSize = 3;
      let totalSaved = 0;

      for (let i = 0; i < entries.length; i += batchSize) {
        const batchStartTime = Date.now();
        const batch = entries.slice(i, i + batchSize);

        this.logger.log(
          `[Queue] Processing batch ${i / batchSize + 1}/${Math.ceil(entries.length / batchSize)}`,
        );

        const processedImages: AppImage[] = [];
        for (const entry of batch) {
          const result = await this.processZipEntryWithLookup(
            entry,
            orderId,
            existingImageMap,
          );
          if (result) processedImages.push(result);
        }

        // Lưu mỗi batch vào database ngay lập tức để giảm sử dụng bộ nhớ
        if (processedImages.length > 0) {
          const saved = await this.imageRepository.save(processedImages);
          totalSaved += saved.length;
          this.logger.log(
            `[Queue] Saved batch of ${saved.length} images in ${Date.now() - batchStartTime}ms`,
          );
        }

        // Cập nhật tiến trình
        await job.progress(10 + (i / entries.length) * 80);

        // Thêm độ trễ giữa các batch
        if (i + batchSize < entries.length) {
          await new Promise((resolve) => setTimeout(resolve, 200));
        }
      }

      // Cập nhật trạng thái trong Firestore
      await this.updateFirestoreStatus(orderId);

      // Dọn dẹp file tạm
      try {
        fs.unlinkSync(tempFilePath);
        this.logger.log(`[Queue] Cleaned up temporary file: ${tempFilePath}`);
      } catch (err) {
        this.logger.error(
          `[Queue] Failed to delete temporary file: ${tempFilePath}`,
          err,
        );
      }

      // Hoàn thành job
      await job.progress(100);
      this.logger.log(
        `[Queue] Completed processing ZIP file for order ${orderId}. Saved ${totalSaved} images.`,
      );

      return {
        success: true,
        message: `Processed and saved ${totalSaved} files from ZIP.`,
        savedCount: totalSaved,
      };
    } catch (error) {
      this.logger.error(`[Queue] Error processing ZIP file: ${error.message}`, {
        orderId,
        tempFilePath,
        error: error.message,
        stack: error.stack,
      });

      // Dọn dẹp file tạm nếu có lỗi
      try {
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
          this.logger.log(
            `[Queue] Cleaned up temporary file after error: ${tempFilePath}`,
          );
        }
      } catch (err) {
        this.logger.error(
          `[Queue] Failed to delete temporary file: ${tempFilePath}`,
          err,
        );
      }

      throw error;
    }
  }

  /**
   * Xử lý một entry trong file ZIP với map tra cứu để tối ưu hiệu năng
   */
  private async processZipEntryWithLookup(
    entry: AdmZip.IZipEntry,
    orderId: string,
    existingImageMap: Map<string, boolean>,
  ): Promise<AppImage | null> {
    if (entry.isDirectory) return null;

    try {
      const fileName = entry.entryName;
      const fileType = this.detectTypeImage(fileName);

      if (!fileType) {
        this.logger.warn(`[Queue] Unsupported file: ${fileName}`);
        return null;
      }

      // Kiểm tra nếu hình ảnh đã tồn tại bằng map tra cứu
      const key = `${fileName}_${fileType}`;
      if (existingImageMap.get(key)) return null;

      // Thêm vào map để tránh xử lý trùng lặp trong batch tiếp theo
      existingImageMap.set(key, true);

      const fileBuffer = entry.getData();
      const uploadedUrl = await this.fileService.singleUpload(
        fileBuffer,
        fileName,
        `appImage/${orderId}`,
      );

      return this.imageRepository.create({
        fileName,
        fileUrl: uploadedUrl,
        fileType: fileType,
        orderId: orderId,
      });
    } catch (err) {
      this.logger.error(
        `[Queue] Failed to process file: ${entry.entryName}`,
        err,
      );
      return null;
    }
  }

  /**
   * Update the status in Firestore to false after successful image upload
   * This indicates that the reprint job has been completed
   */
  private async updateFirestoreStatus(orderId: string): Promise<void> {
    try {
      // Truy vấn trực tiếp các document phù hợp với orderId và status=true
      const matchingDocs = await this.firestoreService.getDocumentsByCondition(
        'ImageReup',
        [
          { field: 'orderId', operator: '==', value: orderId },
          { field: 'status', operator: '==', value: true },
        ],
      );

      if (matchingDocs.length > 0) {
        const doc = matchingDocs[0]; // Lấy document đầu tiên phù hợp
        await this.firestoreService.pushData(
          'ImageReup',
          doc.id,
          {
            ...doc,
            status: false,
          },
          true, // overwrite
        );

        this.logger.log(
          `[Queue] Updated reprint job status to false for order ${orderId} on machine ${doc.id}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `[Queue] Failed to update Firestore status for order ${orderId}:`,
        error,
      );
      // Không ném lỗi ở đây, vì chúng ta không muốn làm hỏng quá trình upload
    }
  }

  /**
   * Process timelapse file
   */
  @Process('process-timelapse')
  async processTimelapse(
    job: Job<{
      tempFilePath: string;
      orderId: string;
      jobId: string;
      filename: string;
    }>,
  ) {
    const { tempFilePath, orderId, filename } = job.data;
    const fileKey = `${orderId}_${filename}`;

    return this.lockManager.withLock(fileKey, async () => {
      this.logger.log(
        `[Queue] Starting background processing of timelapse file: ${filename} for order ${orderId}`,
      );

      try {
        // Check if file exists in pool first
        let pooledFilePath = await this.filePool.getFile(fileKey);

        if (!pooledFilePath) {
          // If not in pool, check if file exists on disk
          if (!fs.existsSync(tempFilePath)) {
            throw new Error(`Temporary file not found: ${tempFilePath}`);
          }

          // Add to pool
          await this.filePool.addFile(fileKey, tempFilePath);
          pooledFilePath = tempFilePath;
        }

        // Get file stats
        const stats = fs.statSync(pooledFilePath);
        this.logger.log(
          `[Queue] Processing timelapse file from disk (${(stats.size / 1024 / 1024).toFixed(2)}MB)`,
        );

        // Update progress
        await job.progress(20);

        // Check if file already exists in database
        const fileType = this.detectTypeImage(filename);
        if (!fileType) {
          throw new Error(`Unsupported file: ${filename}`);
        }

        const existedImage = await this.imageRepository.findOne({
          where: {
            fileName: filename,
            fileType,
            orderId: orderId,
          },
        });

        if (existedImage) {
          this.logger.log(
            `[Queue] File already exists, skipping upload: ${filename}`,
          );

          // Release the file from pool
          await this.filePool.releaseFile(fileKey);

          return {
            success: true,
            message: `File already exists, skipped upload.`,
            fileUrl: existedImage.fileUrl,
          };
        }

        // Read file from disk
        const fileBuffer = fs.readFileSync(pooledFilePath);

        // Update progress
        await job.progress(50);

        // Upload file to storage
        const uploadStartTime = Date.now();
        const uploadedUrl = await this.fileService.singleUpload(
          fileBuffer,
          filename,
          `appImage/${orderId}`,
        );
        this.logger.log(
          `[Queue] File uploaded in ${Date.now() - uploadStartTime}ms`,
        );

        // Update progress
        await job.progress(80);

        // Save to database
        const file = this.imageRepository.create({
          fileName: filename,
          fileUrl: uploadedUrl,
          fileType: fileType,
          orderId: orderId,
        });

        const dbStartTime = Date.now();
        await this.imageRepository.save(file);
        this.logger.log(
          `[Queue] Database save completed in ${Date.now() - dbStartTime}ms`,
        );

        // Update Firestore status
        await this.updateFirestoreStatus(orderId);

        // Release and cleanup the file
        await this.filePool.releaseFile(fileKey);

        // Complete job
        await job.progress(100);
        this.logger.log(
          `[Queue] Completed processing timelapse file for order ${orderId}.`,
        );

        // Update job status
        await this.fileJobRepository.update(
          { jobId: job.data.jobId },
          {
            status: FileJobStatus.COMPLETED,
            progress: 100,
            result: JSON.stringify({
              success: true,
              message: `Processed and saved timelapse file.`,
              fileUrl: uploadedUrl,
            }),
          },
        );

        return {
          success: true,
          message: `Processed and saved timelapse file.`,
          fileUrl: uploadedUrl,
        };
      } catch (error) {
        this.logger.error(
          `[Queue] Error processing timelapse file: ${error.message}`,
          {
            orderId,
            tempFilePath,
            error: error.message,
            stack: error.stack,
          },
        );

        // Update job status
        await this.fileJobRepository.update(
          { jobId: job.data.jobId },
          {
            status: FileJobStatus.FAILED,
            error: error.message,
          },
        );

        // Release the file from pool in case of error
        await this.filePool.releaseFile(fileKey);

        throw error;
      }
    });
  }

  /**
   * Phát hiện loại file hình ảnh
   */
  private detectTypeImage(fileName: string): EImageFileType | null {
    const baseName = fileName.split('.')[0].split('/').pop()?.split('.')[0];

    if (!baseName) {
      return null;
    }

    let result: EImageFileType | null = null;

    if (baseName.startsWith('flipped_')) {
      result = EImageFileType.IMAGE;
    } else if (baseName.includes('merged-image-')) {
      result = EImageFileType.IMAGE_FINAL;
    } else if (baseName.startsWith('timelapse_')) {
      result = EImageFileType.VIDEO;
    }

    return result;
  }
}
