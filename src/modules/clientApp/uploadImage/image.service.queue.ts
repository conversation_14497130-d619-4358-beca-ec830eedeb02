import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan } from 'typeorm';
import { FileJob, FileJobStatus } from './file-job.entity';
import { v4 as uuidv4 } from 'uuid';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';

/**
 * Service để xử lý các tác vụ liên quan đến queue
 */
@Injectable()
export class ImageQueueService {
  private readonly logger = new Logger(ImageQueueService.name);
  private isProcessing = false;
  private lastRunTime: Date | null = null;

  constructor(
    @InjectRepository(FileJob)
    private readonly fileJobRepository: Repository<FileJob>,
    @InjectQueue('file-processing')
    private readonly fileProcessingQueue: Queue,
  ) {
    this.logger.log('ImageQueueService initialized');

    // <PERSON><PERSON>m tra kết nối đến Redis
    this.checkRedisConnection();

    // Lắng nghe các sự kiện từ queue
    this.setupQueueEventListeners();

    // Thiết lập một listener duy nhất cho tất cả các job
    this.setupGlobalProgressListener();
  }

  private setupQueueEventListeners() {
    this.fileProcessingQueue.on('error', (error) => {
      this.logger.error(`Queue error: ${error.message}`, error.stack);
    });

    this.fileProcessingQueue.on('waiting', (jobId) => {
      this.logger.log(`Job ${jobId} is waiting`);
    });

    this.fileProcessingQueue.on('active', (job) => {
      this.logger.log(`Job ${job.id} has started`);
    });

    this.fileProcessingQueue.on('completed', (job, result) => {
      this.logger.log(
        `Job ${job.id} has completed with result: ${JSON.stringify(result)}`,
      );
    });

    this.fileProcessingQueue.on('failed', (job, error) => {
      this.logger.error(
        `Job ${job.id} has failed with error: ${error.message}`,
      );
    });

    this.fileProcessingQueue.on('stalled', (job) => {
      this.logger.warn(`Job ${job.id} has stalled`);
    });
  }

  private setupGlobalProgressListener() {
    // Xử lý progress
    this.fileProcessingQueue.on('progress', async (job, progress) => {
      try {
        const jobId = job.data.jobId;
        const fileJob = await this.fileJobRepository.findOne({
          where: { jobId },
        });

        if (fileJob) {
          await this.fileJobRepository.update(fileJob.id, {
            progress,
            status: FileJobStatus.PROCESSING,
          });
          this.logger.log(
            `[Queue] Updated progress for job ${jobId}: ${progress}%`,
          );
        }
      } catch (error) {
        this.logger.error(`Error updating job progress: ${error.message}`);
      }
    });

    // Xử lý completed
    this.fileProcessingQueue.on('completed', async (job, result) => {
      try {
        const jobId = job.data.jobId;
        const fileJob = await this.fileJobRepository.findOne({
          where: { jobId },
        });

        if (fileJob) {
          await this.fileJobRepository.update(fileJob.id, {
            status: FileJobStatus.COMPLETED,
            progress: 100,
            result: JSON.stringify(result),
          });
          this.logger.log(`[Queue] Job ${jobId} completed successfully`);
        }
      } catch (error) {
        this.logger.error(`Error updating completed job: ${error.message}`);
      }
    });

    // Xử lý failed
    this.fileProcessingQueue.on('failed', async (job, error) => {
      try {
        const jobId = job.data.jobId;
        const fileJob = await this.fileJobRepository.findOne({
          where: { jobId },
        });

        if (fileJob) {
          await this.fileJobRepository.update(fileJob.id, {
            status: FileJobStatus.FAILED,
            error: error.message,
          });
          this.logger.error(`[Queue] Job ${jobId} failed: ${error.message}`);
        }
      } catch (err) {
        this.logger.error(`Error updating failed job: ${err.message}`);
      }
    });

    // Xử lý stalled
    this.fileProcessingQueue.on('stalled', async (job) => {
      try {
        const jobId = job.data.jobId;
        const fileJob = await this.fileJobRepository.findOne({
          where: { jobId },
        });

        if (fileJob) {
          await this.fileJobRepository.update(fileJob.id, {
            status: FileJobStatus.FAILED,
            error: 'Job stalled - processing took too long',
          });
          this.logger.warn(`[Queue] Job ${jobId} stalled`);
        }
      } catch (err) {
        this.logger.error(`Error updating stalled job: ${err.message}`);
      }
    });
  }

  /**
   * Tạo một job trống để trả về jobId ngay lập tức
   */
  async createEmptyJob(
    orderId: string,
    tempFilePath: string,
  ): Promise<{ jobId: string; fileJobId: string }> {
    // Tạo một job ID duy nhất
    const jobId = uuidv4();

    // Tạo bản ghi job với trạng thái PENDING
    const fileJob = await this.fileJobRepository.save({
      id: uuidv4(),
      orderId,
      jobId,
      status: FileJobStatus.PENDING,
      progress: 0,
      filePath: tempFilePath,
    });

    // Không cần thiết lập listener riêng nữa
    this.logger.log(
      `[Queue] Created empty job with ID: ${jobId} for order: ${orderId}`,
    );

    return { jobId, fileJobId: fileJob.id };
  }

  /**
   * Cập nhật job và thêm vào queue
   */
  async updateJobAndAddToQueue(
    orderId: string,
    tempFilePath: string,
    jobId: string,
    fileJobId: string,
  ): Promise<void> {
    try {
      this.logger.log(`[Queue] Adding job ${jobId} to queue for processing`);

      // Thêm job vào queue
      this.logger.log(`[Queue] About to add job ${jobId} to queue with data:`, {
        tempFilePath,
        orderId,
        jobId,
      });

      const job = await this.fileProcessingQueue.add(
        'process-zip-file',
        {
          tempFilePath,
          orderId,
          jobId,
        },
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
          removeOnComplete: true,
          removeOnFail: false,
        },
      );

      this.logger.log(
        `[Queue] Job added with ID: ${job.id}, data: ${JSON.stringify(job.data)}`,
      );

      // Kiểm tra số lượng job trong queue
      const jobCounts = await this.fileProcessingQueue.getJobCounts();
      this.logger.log(
        `[Queue] Current job counts: ${JSON.stringify(jobCounts)}`,
      );

      this.logger.log(`[Queue] Job ${jobId} added to queue successfully`);
    } catch (error) {
      this.logger.error(`[Queue] Error adding job to queue: ${error.message}`, {
        orderId,
        jobId,
        fileJobId,
        error: error.message,
        stack: error.stack,
      });

      // Cập nhật trạng thái job thành FAILED
      await this.fileJobRepository.update(fileJobId, {
        status: FileJobStatus.FAILED,
        error: error.message,
      });

      throw error;
    }
  }

  /**
   * Tạo một job mới để xử lý file ZIP
   */
  async createFileProcessingJob(
    orderId: string,
    tempFilePath: string,
  ): Promise<{ jobId: string; fileJobId: string }> {
    // Tạo một job ID duy nhất
    const jobId = uuidv4();

    // Tạo bản ghi job
    const fileJob = await this.fileJobRepository.save({
      id: uuidv4(),
      orderId,
      jobId,
      status: FileJobStatus.PENDING,
      progress: 0,
      filePath: tempFilePath,
    });

    // Thêm job vào queue
    await this.fileProcessingQueue.add(
      'process-zip-file',
      {
        tempFilePath,
        orderId,
        jobId,
      },
      {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 5000,
        },
        removeOnComplete: true,
        removeOnFail: false,
      },
    );

    this.logger.log(
      `[Queue] Created file processing job with ID: ${jobId} for order: ${orderId}`,
    );

    return { jobId, fileJobId: fileJob.id };
  }

  /**
   * Tạo một job mới để xử lý file timelapse
   */
  async createTimelapseProcessingJob(
    orderId: string,
    tempFilePath: string,
    filename: string,
  ): Promise<{ jobId: string; fileJobId: string }> {
    // Tạo một job ID duy nhất
    const jobId = uuidv4();

    // Tạo bản ghi job
    const fileJob = await this.fileJobRepository.save({
      id: uuidv4(),
      orderId,
      jobId,
      status: FileJobStatus.PENDING,
      progress: 0,
      filePath: tempFilePath,
      fileName: filename,
    });

    this.logger.log(
      `[Queue] Created timelapse job with ID: ${jobId} for order: ${orderId}`,
    );

    return { jobId, fileJobId: fileJob.id };
  }

  /**
   * Cập nhật job và thêm vào queue cho timelapse
   */
  async updateTimelapseJobAndAddToQueue(
    orderId: string,
    tempFilePath: string,
    jobId: string,
    fileJobId: string,
    filename: string,
  ): Promise<void> {
    try {
      this.logger.log(
        `[Queue] Adding timelapse job ${jobId} to queue for processing`,
      );

      // Thêm job vào queue
      const job = await this.fileProcessingQueue.add(
        'process-timelapse',
        {
          tempFilePath,
          orderId,
          jobId,
          filename,
        },
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
          removeOnComplete: true,
          removeOnFail: false,
        },
      );

      this.logger.log(
        `[Queue] Timelapse job added with ID: ${job.id}, data: ${JSON.stringify(job.data)}`,
      );

      // Kiểm tra số lượng job trong queue
      const jobCounts = await this.fileProcessingQueue.getJobCounts();
      this.logger.log(
        `[Queue] Current job counts: ${JSON.stringify(jobCounts)}`,
      );

      this.logger.log(
        `[Queue] Timelapse job ${jobId} added to queue successfully`,
      );
    } catch (error) {
      this.logger.error(
        `[Queue] Error adding timelapse job to queue: ${error.message}`,
        {
          orderId,
          jobId,
          fileJobId,
          error: error.message,
          stack: error.stack,
        },
      );

      // Cập nhật trạng thái job thành FAILED
      await this.fileJobRepository.update(fileJobId, {
        status: FileJobStatus.FAILED,
        error: error.message,
      });

      throw error;
    }
  }

  /**
   * Lưu file tạm vào đĩa
   * @param stream Stream dữ liệu file
   * @param orderId ID của đơn hàng
   * @param fileType Loại file (mặc định là 'zip')
   * @returns Đường dẫn đến file tạm
   */
  async saveTemporaryFile(
    stream: NodeJS.ReadableStream,
    orderId: string,
    fileType: string = 'zip',
  ): Promise<string> {
    // Xác định phần mở rộng file dựa trên loại file
    const fileExtension = this.getFileExtension(fileType);

    // Tạo đường dẫn file tạm với phần mở rộng phù hợp
    const tempFilePath = path.join(
      os.tmpdir(),
      `upload-${orderId}-${Date.now()}.${fileExtension}`,
    );
    let writeStream: fs.WriteStream = null;

    try {
      writeStream = fs.createWriteStream(tempFilePath);
      this.logger.log(`[Queue] Creating temporary file: ${tempFilePath}`);

      // Pipe stream upload vào file tạm với xử lý lỗi cải thiện
      await new Promise<void>((resolve, reject) => {
        stream.pipe(writeStream);

        writeStream.on('finish', () => {
          this.logger.log(
            `[Queue] Temporary file write completed: ${tempFilePath}`,
          );
          resolve();
        });

        writeStream.on('error', (err) => {
          this.logger.error(
            `[Queue] Error writing to temporary file: ${err.message}`,
            {
              orderId,
              tempFilePath,
              error: err.message,
            },
          );
          reject(err);
        });

        // Xử lý khi client ngắt kết nối
        stream.on('close', () => {
          this.logger.warn(`[Queue] Upload stream closed unexpectedly`, {
            orderId,
            tempFilePath,
          });

          // Nếu writeStream vẫn đang mở, chúng ta sẽ kết thúc nó
          if (writeStream && !writeStream.destroyed) {
            writeStream.end();
          }
        });
      });

      // Kiểm tra kích thước file tạm để đảm bảo nó đã được ghi đầy đủ
      const stats = fs.statSync(tempFilePath);
      if (stats.size === 0) {
        throw new Error('Upload failed: Empty file received');
      }

      this.logger.log(
        `[Queue] Temporary file created: ${tempFilePath} (${(stats.size / 1024 / 1024).toFixed(2)}MB)`,
      );

      return tempFilePath;
    } catch (error) {
      // Ghi log lỗi chi tiết
      this.logger.error(
        `[Queue] Error saving temporary file: ${error.message}`,
        {
          orderId,
          tempFilePath,
          error: error.message,
          stack: error.stack,
        },
      );

      // Dọn dẹp file tạm nếu có lỗi
      try {
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
          this.logger.log(
            `[Queue] Cleaned up temporary file after error: ${tempFilePath}`,
          );
        }
      } catch (err) {
        this.logger.error(
          `[Queue] Failed to delete temporary file: ${tempFilePath}`,
          err,
        );
      }

      // Ném lại lỗi để xử lý ở cấp cao hơn
      throw error;
    } finally {
      // Đảm bảo writeStream được đóng nếu vẫn đang mở
      if (writeStream && !writeStream.destroyed) {
        try {
          writeStream.end();
        } catch (err) {
          this.logger.error(
            `[Queue] Error closing write stream: ${err.message}`,
          );
        }
      }
    }
  }

  /**
   * Lấy thông tin về job
   */
  async getJobStatus(jobId: string): Promise<FileJob> {
    const job = await this.fileJobRepository.findOne({ where: { jobId } });

    if (job) {
      // Kiểm tra trạng thái của job trong queue
      try {
        const queueJobs = await this.fileProcessingQueue.getJobs([
          'waiting',
          'active',
          'completed',
          'failed',
        ]);
        const queueJob = queueJobs.find((j) => j.data.jobId === jobId);

        if (queueJob) {
          this.logger.log(
            `[Queue] Found job ${jobId} in queue with state: ${await queueJob.getState()}`,
          );
        } else {
          this.logger.warn(`[Queue] Job ${jobId} not found in queue`);
        }
      } catch (error) {
        this.logger.error(
          `[Queue] Error checking job in queue: ${error.message}`,
        );
      }
    }

    return job;
  }

  /**
   * Kiểm tra kết nối đến Redis
   */
  private async checkRedisConnection(): Promise<void> {
    try {
      // Get Redis connection details for logging
      const redisClient = this.fileProcessingQueue.client;
      const redisOptions = redisClient.options as any;

      // Log Redis connection details
      this.logger.log(
        `Redis connection details: Host: ${redisOptions.host || 'localhost'}, Port: ${redisOptions.port || 6379}`,
      );

      // Thêm các event listener để theo dõi trạng thái kết nối Redis
      redisClient.on('connect', () => {
        this.logger.log('Redis client connected');
      });

      redisClient.on('ready', () => {
        this.logger.log('Redis client ready');
      });

      redisClient.on('error', (err) => {
        this.logger.error(`Redis client error: ${err.message}`);
      });

      redisClient.on('reconnecting', () => {
        this.logger.log('Redis client reconnecting');
      });

      // Attempt to ping Redis
      await redisClient.ping();
      this.logger.log('Connected to Redis successfully');

      // Thiết lập kiểm tra kết nối định kỳ
      this.setupPeriodicConnectionCheck();
    } catch (error) {
      this.logger.error(`Failed to connect to Redis: ${error.message}`);
      this.logger.error(`Error stack: ${error.stack}`);

      // Try to get more information about the Redis connection
      try {
        // Thử kết nối trực tiếp đến Redis với thông tin cố định
        this.logger.log(
          'Attempting to connect to Redis with fixed configuration...',
        );

        // Hiển thị thông tin kết nối cố định
        this.logger.log(
          'Fixed Redis connection details: Host: valkey-14679c2c-modi.k.aivencloud.com, Port: 26646',
        );

        const redisClient = this.fileProcessingQueue.client;
        const redisOptions = redisClient.options as any;

        this.logger.error(
          `Redis connection details: ${
            redisOptions.path
              ? `Socket: ${redisOptions.path}`
              : `Host: ${redisOptions.host || 'localhost'}, Port: ${redisOptions.port || 6379}`
          }`,
        );
      } catch (innerError) {
        this.logger.error(
          `Could not get Redis connection details: ${innerError.message}`,
        );
      }
    }
  }

  /**
   * Thiết lập kiểm tra kết nối Redis định kỳ
   */
  private setupPeriodicConnectionCheck(): void {
    // Kiểm tra kết nối mỗi 30 giây
    setInterval(async () => {
      try {
        await this.fileProcessingQueue.client.ping();
        this.logger.log('Periodic Redis connection check: Connected');
      } catch (error) {
        this.logger.error(
          `Periodic Redis connection check failed: ${error.message}`,
        );

        // Thử kết nối lại
        try {
          this.logger.log('Attempting to reconnect to Redis...');
          // Không cần làm gì đặc biệt vì Bull sẽ tự động thử kết nối lại
        } catch (reconnectError) {
          this.logger.error(
            `Failed to reconnect to Redis: ${reconnectError.message}`,
          );
        }
      }
    }, 30000); // 30 giây
  }

  /**
   * Lấy phần mở rộng file dựa trên loại file
   * @param fileType Loại file
   * @returns Phần mở rộng file
   */
  private getFileExtension(fileType: string): string {
    // Chuyển đổi fileType thành chữ thường để dễ so sánh
    const type = fileType.toLowerCase();

    // Xác định phần mở rộng dựa trên loại file
    switch (type) {
      case 'zip':
        return 'zip';
      case 'timelapse':
      case 'video':
        return 'mp4';
      case 'image':
        return 'jpg';
      case 'gif':
        return 'gif';
      case 'png':
        return 'png';
      default:
        // Mặc định trả về zip nếu không xác định được loại file
        this.logger.warn(
          `Unknown file type: ${fileType}, using default extension 'zip'`,
        );
        return 'zip';
    }
  }

  /**
   * Kiểm tra trạng thái của queue
   */
  async checkQueueStatus(): Promise<any> {
    try {
      // Kiểm tra kết nối đến Redis
      await this.fileProcessingQueue.client.ping();

      // Lấy số lượng job trong queue
      const jobCounts = await this.fileProcessingQueue.getJobCounts();

      // Lấy danh sách job đang chờ
      const waitingJobs = await this.fileProcessingQueue.getJobs(['waiting']);

      // Lấy danh sách job đang xử lý
      const activeJobs = await this.fileProcessingQueue.getJobs(['active']);

      // Lấy danh sách job đã hoàn thành
      const completedJobs = await this.fileProcessingQueue.getJobs([
        'completed',
      ]);

      // Lấy danh sách job bị lỗi
      const failedJobs = await this.fileProcessingQueue.getJobs(['failed']);

      return {
        connected: true,
        jobCounts,
        waitingJobs: waitingJobs.map((job) => ({
          id: job.id,
          data: job.data,
        })),
        activeJobs: activeJobs.map((job) => ({
          id: job.id,
          data: job.data,
        })),
        completedJobs: completedJobs.map((job) => ({
          id: job.id,
          data: job.data,
        })),
        failedJobs: failedJobs.map((job) => ({
          id: job.id,
          data: job.data,
          failedReason: job.failedReason,
        })),
      };
    } catch (error) {
      this.logger.error(
        `[Queue] Error checking queue status: ${error.message}`,
      );
      return {
        connected: false,
        error: error.message,
      };
    }
  }

  /**
   * Cron job để xử lý các job bị treo
   */
  @Cron('0 */10 * * * *')
  async handleStaleJobs() {
    const currentTime = new Date();
    this.logger.log(
      `Starting stale jobs check at ${currentTime.toISOString()}`,
    );

    if (this.lastRunTime) {
      const timeSinceLastRun =
        (currentTime.getTime() - this.lastRunTime.getTime()) / 1000;
      this.logger.log(`Time since last run: ${timeSinceLastRun} seconds`);
    }

    if (this.isProcessing) {
      this.logger.warn('Another stale jobs process is running, skipping...');
      return;
    }

    try {
      this.isProcessing = true;
      const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);

      // Log queue stats before processing
      await this.logQueueStats();

      const staleJobs = await this.fileJobRepository.find({
        select: ['id', 'jobId', 'orderId', 'status', 'filePath', 'createdAt'],
        where: {
          status: FileJobStatus.PENDING,
          createdAt: LessThan(tenMinutesAgo),
        },
        take: 100,
        order: {
          createdAt: 'ASC',
        },
      });

      this.logger.log(`Found ${staleJobs.length} stale jobs to check`);
      if (staleJobs.length === 0) {
        return;
      }

      // Get only active, waiting, and completed jobs from Bull queue
      const activeJobs = await this.fileProcessingQueue.getJobs(['active']);
      const waitingJobs = await this.fileProcessingQueue.getJobs(['waiting']);
      const completedJobs = await this.fileProcessingQueue.getJobs([
        'completed',
      ]);

      // Combine all active jobs
      const existingBullJobs = [
        ...activeJobs,
        ...waitingJobs,
        ...completedJobs,
      ];

      // Create a Set of jobIds from Bull queue data
      const existingJobIds = new Set(
        existingBullJobs.map((job) => job.data.jobId),
      );

      this.logger.log(
        `Found ${existingBullJobs.length} active jobs in Bull queue (${activeJobs.length} active, ${waitingJobs.length} waiting, ${completedJobs.length} completed)`,
      );

      // Get failed jobs for logging purposes
      const failedJobs = await this.fileProcessingQueue.getJobs(['failed']);
      this.logger.log(`Found ${failedJobs.length} failed jobs in Bull queue`);

      // Filter jobs that are not in active Bull queue
      const jobsToRequeue = staleJobs.filter(
        (job) => !existingJobIds.has(job.jobId),
      );

      this.logger.log(
        `Found ${jobsToRequeue.length} jobs that need to be requeued`,
      );

      if (jobsToRequeue.length > 0) {
        this.logger.log(`Requeueing ${jobsToRequeue.length} jobs`);

        const batchSize = 10;
        for (let i = 0; i < jobsToRequeue.length; i += batchSize) {
          const batch = jobsToRequeue.slice(i, i + batchSize);
          await Promise.all(
            batch.map(async (job) => {
              try {
                const isTimelapse = job.filePath?.includes('timelapse');
                const processorName = isTimelapse
                  ? 'process-timelapse'
                  : 'process-zip-file';

                // Check if file exists and has content
                const stats = fs.statSync(job.filePath);
                if (stats.size === 0) {
                  this.logger.error(`Temporary file is empty: ${job.filePath}`);

                  await this.fileJobRepository.update(
                    { jobId: job.jobId },
                    {
                      status: FileJobStatus.FAILED,
                      error: `Temporary file is empty: ${job.filePath}`,
                    },
                  );
                  return;
                }

                this.logger.log(
                  `Processing job ${job.jobId} - ${job.filePath} - ${processorName}`,
                );

                // Remove failed job with the same jobId if it exists
                const failedJob = failedJobs.find(
                  (j) => j.data.jobId === job.jobId,
                );

                if (failedJob) {
                  this.logger.log(
                    `Removing failed job ${failedJob.id} for jobId ${job.jobId}`,
                  );
                  await failedJob.remove();
                }

                const filename = path.basename(job.filePath);
                // Add job to queue with the same jobId
                const queueJob = await this.fileProcessingQueue.add(
                  processorName,
                  {
                    jobId: job.jobId,
                    orderId: job.orderId,
                    tempFilePath: job.filePath,
                    ...(isTimelapse && { filename }),
                  },
                  {
                    jobId: job.jobId, // Use the same jobId
                    attempts: 3,
                    removeOnComplete: true,
                    removeOnFail: false,
                    backoff: {
                      type: 'exponential',
                      delay: 1000,
                    },
                  },
                );

                this.logger.log(
                  `Added job to queue with ID ${queueJob.id} for jobId ${job.jobId}`,
                );
              } catch (error) {
                this.logger.error(
                  `Failed to requeue job ${job.jobId}: ${error.message}`,
                  error.stack,
                );

                // Update job status to failed
                await this.fileJobRepository.update(
                  { jobId: job.jobId },
                  {
                    status: FileJobStatus.FAILED,
                    error: error.message,
                  },
                );
              }
            }),
          );

          this.logger.log(
            `Requeued batch ${i / batchSize + 1}/${Math.ceil(jobsToRequeue.length / batchSize)}`,
          );
        }

        // Log queue stats after processing
        await this.logQueueStats();
      } else {
        this.logger.log('No jobs need to be requeued');
      }
    } catch (error) {
      this.logger.error(`Error handling stale jobs: ${error.message}`);
      this.logger.error(`Error stack: ${error.stack}`);
    } finally {
      this.lastRunTime = currentTime;
      this.isProcessing = false;
      this.logger.log(
        `Finished stale jobs check at ${new Date().toISOString()}`,
      );
    }
  }

  private async logQueueStats() {
    try {
      const jobCounts = await this.fileProcessingQueue.getJobCounts();
      this.logger.log(`Current queue stats:
        Waiting: ${jobCounts.waiting}
        Active: ${jobCounts.active}
        Completed: ${jobCounts.completed}
        Failed: ${jobCounts.failed}
        Delayed: ${jobCounts.delayed}
      `);
    } catch (error) {
      this.logger.error(`Failed to get queue stats: ${error.message}`);
    }
  }
}
