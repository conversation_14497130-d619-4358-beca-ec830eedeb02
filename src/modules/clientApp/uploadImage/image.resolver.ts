import { Resolver, Mutation, Args } from '@nestjs/graphql';
import { ImageService } from './image.service';
import {
  CreateImageInput,
  CreateImageResponse,
  CreateTimeLapseResponse,
} from './image.input';
import { FileUpload, GraphQLUpload } from 'graphql-upload';
import { UseGuards } from '@nestjs/common';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';

@Resolver()
@UseGuards(ClientAppAuthGuard)
export class ImageResolver {
  constructor(private readonly imageService: ImageService) {}

  @Mutation(() => CreateImageResponse)
  async clientAppCreateImage(
    @Args('input') input: CreateImageInput,
    @Args({ name: 'file', type: () => GraphQLUpload }) file: FileUpload,
  ): Promise<CreateImageResponse> {
    return this.imageService.create(input, file);
  }

  @Mutation(() => CreateTimeLapseResponse)
  async clientAppCreateTimeLapse(
    @Args('input') input: CreateImageInput,
    @Args({ name: 'file', type: () => GraphQLUpload }) file: FileUpload,
  ): Promise<CreateTimeLapseResponse> {
    return this.imageService.createTimeLapse(input, file);
  }
}
