import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ObjectType, Field, Float } from '@nestjs/graphql';

export enum FileJobStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
}

@ObjectType()
@Entity('file_job')
export class FileJob {
  @Field()
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Field()
  @Column()
  orderId: string;

  @Field()
  @Column()
  jobId: string;

  @Field()
  @Column({
    type: 'enum',
    enum: FileJobStatus,
    default: FileJobStatus.PENDING,
  })
  status: FileJobStatus;

  @Field(() => Float, { nullable: true })
  @Column({ nullable: true })
  progress: number;

  @Field({ nullable: true })
  @Column({ nullable: true, type: 'text' })
  result: string;

  @Field({ nullable: true })
  @Column({ nullable: true, type: 'text' })
  error: string;

  @Field({ nullable: true })
  @Column({ nullable: true })
  filePath: string;

  @Field()
  @CreateDateColumn()
  createdAt: Date;

  @Field()
  @UpdateDateColumn()
  updatedAt: Date;
}
