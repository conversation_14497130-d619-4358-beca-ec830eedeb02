import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppImage } from './image.entity';
import { ImageResolver } from './image.resolver';
import { ImageService } from './image.service';
import { FileService } from 'src/modules/common/module/file/file.service';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { Order } from '../order/order.entity';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { UploadQueueModule } from './upload-queue.module';

@Module({
  imports: [TypeOrmModule.forFeature([AppImage, Order]), UploadQueueModule],
  providers: [
    JwtService,
    SBLogger,
    ImageResolver,
    ImageService,
    FileService,
    FirestoreService,
  ],
})
export class ImageModule {}
