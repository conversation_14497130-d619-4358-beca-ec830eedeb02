import {
  Injectable,
  BadRequestException,
  ConflictException,
  Logger,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Order } from '../order/order.entity';
import { CreateImageInput } from './image.input';
import { FileUpload } from 'graphql-upload';
import { ECaptureMode } from '../../../enum';
import { ImageQueueService } from './image.service.queue';

interface UploadResult {
  message: string;
  captureMode: ECaptureMode;
  domain: string;
  jobId?: string;
}

@Injectable()
export class ImageService {
  private readonly logger = new Logger(ImageService.name);

  constructor(
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    private readonly imageQueueService: ImageQueueService,
  ) {}

  async create(
    input: CreateImageInput,
    file?: FileUpload,
  ): Promise<UploadResult> {
    const startTime = Date.now();

    try {
      this.logger.log(
        `[Performance] Starting upload for order ${input.orderId}`,
      );
      await this.validateInput(input, file);
      const orderRecord = await this.getOrderRecord(input.orderId);

      const { createReadStream, mimetype, filename } = await (file as any);
      this.validateFileType(mimetype);

      // Sử dụng queue để xử lý file lớn
      this.logger.log(
        `[Performance] Using queue for processing ZIP file: ${filename || 'unknown'}`,
      );

      try {
        this.logger.log(
          `[Performance] Creating empty job for immediate response`,
        );

        // Lưu file tạm vào đĩa trước khi trả về response
        this.logger.log(
          `[Performance] Saving temporary file to disk before returning response`,
        );
        const tempFilePath = await this.imageQueueService.saveTemporaryFile(
          createReadStream(),
          orderRecord.id,
          'zip',
        );
        this.logger.log(`[Performance] Temporary file saved: ${tempFilePath}`);

        // Tạo job trống trước để có jobId ngay lập tức
        const { jobId, fileJobId } =
          await this.imageQueueService.createEmptyJob(
            orderRecord.id,
            tempFilePath,
          );

        this.logger.log(`[Performance] Empty job created with ID: ${jobId}`);

        // Chuẩn bị response để trả về ngay lập tức
        const response: UploadResult = {
          message: `File upload started. Processing in background. Check status with jobId: ${jobId}`,
          captureMode: input.captureMode,
          domain: `${process.env.MEMORY_DOMAIN}${input.orderId}`,
          jobId,
        };

        // Xử lý file trong background (không đợi hoàn thành)
        // Sử dụng setTimeout để đảm bảo response được trả về trước
        setTimeout(() => {
          // Không sử dụng createReadStream() trong setTimeout
          this.logger.log(
            `[Background] Starting background processing for order: ${orderRecord.id}, job: ${jobId}`,
          );

          // Cập nhật job và đưa vào queue
          this.imageQueueService
            .updateJobAndAddToQueue(
              orderRecord.id,
              tempFilePath,
              jobId,
              fileJobId,
            )
            .catch((err) => {
              this.logger.error(
                `[Performance] Error in background processing: ${err.message}`,
                {
                  orderId: orderRecord.id,
                  jobId,
                  error: err.message,
                  stack: err.stack,
                },
              );
            });
        }, 10);

        this.logger.log(
          `[Performance] Returning immediate response with jobId: ${jobId}`,
        );

        // Trả về kết quả ngay lập tức cho client
        return response;
      } catch (uploadError) {
        // Xử lý lỗi cụ thể từ quá trình upload
        if (
          uploadError.message &&
          uploadError.message.includes('disconnected')
        ) {
          this.logger.warn(
            `[Performance] Client disconnected during upload for order ${input.orderId}`,
            {
              orderId: input.orderId,
              error: uploadError.message,
            },
          );

          // Ném lỗi BadRequest với mã 499 (Client Closed Request)
          const clientDisconnectError = new BadRequestException(
            'Client disconnected during file upload',
          );
          clientDisconnectError['statusCode'] = 499;
          throw clientDisconnectError;
        }

        // Ném lại các lỗi khác
        throw uploadError;
      }
    } catch (error) {
      const elapsedTime = Date.now() - startTime;

      // Phân loại lỗi để log phù hợp
      if (error.statusCode === 499) {
        // Lỗi client ngắt kết nối - log ở mức warning thay vì error
        this.logger.warn('[Performance] Client disconnected during upload', {
          orderId: input?.orderId,
          elapsedTime: `${elapsedTime}ms`,
          error: error.message,
        });
      } else {
        // Các lỗi khác - log ở mức error
        this.logger.error('[Performance] Upload failed', {
          orderId: input?.orderId,
          elapsedTime: `${elapsedTime}ms`,
          error: error.message,
          stack: error.stack,
        });
      }

      this.handleError(error);
    }
  }

  async createTimeLapse(
    input: CreateImageInput,
    uploadedFile?: FileUpload,
  ): Promise<{ isSuccess: boolean; jobId?: string }> {
    const startTime = Date.now();

    try {
      this.logger.log(
        `[Performance] Starting timelapse upload for order ${input.orderId}`,
      );
      await this.validateInput(input, uploadedFile);
      const { createReadStream, filename } = await (uploadedFile as any);

      // Sử dụng queue để xử lý file timelapse
      this.logger.log(
        `[Performance] Using queue for processing timelapse file: ${filename || 'unknown'}`,
      );

      try {
        this.logger.log(
          `[Performance] Creating empty job for immediate response`,
        );

        // Lưu file tạm vào đĩa trước khi trả về response
        this.logger.log(
          `[Performance] Saving temporary file to disk before returning response`,
        );
        const tempFilePath = await this.imageQueueService.saveTemporaryFile(
          createReadStream(),
          input.orderId,
        );
        this.logger.log(`[Performance] Temporary file saved: ${tempFilePath}`);

        // Tạo job trống trước để có jobId ngay lập tức
        const { jobId, fileJobId } =
          await this.imageQueueService.createEmptyJob(
            input.orderId,
            tempFilePath,
          );

        this.logger.log(`[Performance] Empty job created with ID: ${jobId}`);

        // Chuẩn bị response để trả về ngay lập tức
        const response = {
          isSuccess: true,
          jobId, // Thêm jobId vào response để client có thể kiểm tra trạng thái
        };

        // Xử lý file trong background (không đợi hoàn thành)
        // Sử dụng setTimeout để đảm bảo response được trả về trước
        setTimeout(() => {
          try {
            this.logger.log(
              `[Background] Starting background processing for timelapse: ${filename}`,
            );

            // Tạo job xử lý bất đồng bộ
            this.imageQueueService
              .updateTimelapseJobAndAddToQueue(
                input.orderId,
                tempFilePath,
                jobId,
                fileJobId,
                filename,
              )
              .then(() => {
                this.logger.log(
                  `[Background] Timelapse job added to queue successfully: ${jobId}`,
                );
              })
              .catch((err) => {
                this.logger.error(
                  `[Background] Error in background processing: ${err.message}`,
                  {
                    orderId: input.orderId,
                    filename,
                    jobId,
                    error: err.message,
                    stack: err.stack,
                  },
                );
              });
          } catch (err) {
            this.logger.error(
              `[Background] Error in background processing: ${err.message}`,
              {
                orderId: input.orderId,
                filename,
                jobId,
                error: err.message,
                stack: err.stack,
              },
            );
          }
        }, 10);

        this.logger.log(
          `[Performance] Returning immediate response with jobId: ${jobId}`,
        );

        // Trả về kết quả ngay lập tức cho client
        return response;
      } catch (uploadError) {
        // Xử lý lỗi cụ thể từ quá trình upload
        if (
          uploadError.message &&
          uploadError.message.includes('disconnected')
        ) {
          this.logger.warn(
            `[Performance] Client disconnected during timelapse upload for order ${input.orderId}`,
            {
              orderId: input.orderId,
              filename,
              error: uploadError.message,
            },
          );

          // Ném lỗi BadRequest với mã 499 (Client Closed Request)
          const clientDisconnectError = new BadRequestException(
            'Client disconnected during file upload',
          );
          clientDisconnectError['statusCode'] = 499;
          throw clientDisconnectError;
        }

        // Ném lại các lỗi khác
        throw uploadError;
      }
    } catch (error) {
      const elapsedTime = Date.now() - startTime;

      // Phân loại lỗi để log phù hợp
      if (error.statusCode === 499) {
        // Lỗi client ngắt kết nối - log ở mức warning thay vì error
        this.logger.warn(
          '[Performance] Client disconnected during timelapse upload',
          {
            orderId: input?.orderId,
            filename: uploadedFile?.filename,
            elapsedTime: `${elapsedTime}ms`,
            error: error.message,
          },
        );
      } else {
        // Các lỗi khác - log ở mức error
        this.logger.error('[Performance] Timelapse upload failed', {
          orderId: input?.orderId,
          filename: uploadedFile?.filename,
          elapsedTime: `${elapsedTime}ms`,
          error: error.message,
          stack: error.stack,
        });
      }

      this.handleError(error);
    }
  }

  private async validateInput(
    input: CreateImageInput,
    file?: FileUpload,
  ): Promise<void> {
    if (!input?.orderId) {
      throw new BadRequestException('Order ID is required.');
    }
    if (!file) {
      throw new BadRequestException('File is required');
    }
  }

  private handleError(error: any): never {
    // Xử lý lỗi trùng lặp từ database
    if (error.code === 'ER_DUP_ENTRY') {
      throw new ConflictException('Duplicate entry for image and order.');
    }

    // Xử lý lỗi client ngắt kết nối
    if (
      error.statusCode === 499 ||
      (error.message &&
        (error.message.includes('disconnected') ||
          error.message.includes('closed') ||
          error.message.includes('Request disconnected')))
    ) {
      // Đảm bảo lỗi có mã 499 (Client Closed Request)
      const clientDisconnectError = new BadRequestException(
        error.message || 'Client disconnected during file upload',
      );
      clientDisconnectError['statusCode'] = 499;
      throw clientDisconnectError;
    }

    // Ném lại các lỗi khác
    throw error;
  }

  private async getOrderRecord(orderID: string): Promise<Order> {
    const orderRecord = await this.orderRepository.findOne({
      where: { id: orderID },
      relations: ['images'],
    });

    if (!orderRecord) {
      throw new BadRequestException('Order record not found.');
    }

    return orderRecord;
  }

  private validateFileType(mimetype: string): void {
    if (mimetype !== 'application/zip') {
      throw new BadRequestException(
        'Invalid file type. Only ZIP files are allowed.',
      );
    }
  }
}
