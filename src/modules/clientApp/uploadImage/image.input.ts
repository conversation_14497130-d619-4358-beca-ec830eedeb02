import { Field, InputType, ObjectType } from '@nestjs/graphql';
import { ECaptureMode } from '../../../enum/index';

@InputType()
export class CreateImageInput {
  @Field({ nullable: true })
  orderId?: string;

  @Field({ nullable: true })
  captureMode: ECaptureMode;
}

@ObjectType()
export class CreateImageResponse {
  @Field()
  message: string;

  @Field()
  captureMode: ECaptureMode;

  @Field()
  domain: string;
}

@ObjectType()
export class CreateTimeLapseResponse {
  @Field()
  isSuccess: boolean;
}
