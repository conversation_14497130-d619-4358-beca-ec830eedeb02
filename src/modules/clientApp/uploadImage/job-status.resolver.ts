import {
  Resolver,
  Query,
  Args,
  ObjectType,
  Field,
  Float,
} from '@nestjs/graphql';
import { ImageQueueService } from './image.service.queue';
import { FileJobStatus } from './file-job.entity';
import { Logger } from '@nestjs/common';

@Resolver()
export class JobStatusResolver {
  private readonly logger = new Logger(JobStatusResolver.name);

  constructor(private readonly imageQueueService: ImageQueueService) {}

  @Query(() => JobStatusResponse)
  async getJobStatus(@Args('jobId') jobId: string): Promise<JobStatusResponse> {
    this.logger.log(`Checking status for job: ${jobId}`);
    const job = await this.imageQueueService.getJobStatus(jobId);

    if (!job) {
      this.logger.warn(`Job not found: ${jobId}`);
      return {
        jobId,
        status: 'not_found',
        message: 'Job not found',
        progress: 0,
      };
    }

    this.logger.log(
      `Found job: ${jobId}, status: ${job.status}, progress: ${job.progress}`,
    );
    return {
      jobId: job.jobId,
      status: job.status,
      message: this.getStatusMessage(job.status),
      progress: job.progress || 0,
      result: job.result,
      error: job.error,
    };
  }

  @Query(() => String)
  async checkQueueStatus(): Promise<string> {
    this.logger.log('Checking queue status');
    try {
      const status = await this.imageQueueService.checkQueueStatus();
      return JSON.stringify(status, null, 2);
    } catch (error) {
      this.logger.error(`Error checking queue status: ${error.message}`);
      return JSON.stringify({ error: error.message }, null, 2);
    }
  }

  private getStatusMessage(status: FileJobStatus): string {
    switch (status) {
      case FileJobStatus.PENDING:
        return 'Job is pending';
      case FileJobStatus.PROCESSING:
        return 'Job is being processed';
      case FileJobStatus.COMPLETED:
        return 'Job has been completed successfully';
      case FileJobStatus.FAILED:
        return 'Job has failed';
      default:
        return 'Unknown status';
    }
  }
}

// Định nghĩa response type cho GraphQL
@ObjectType()
export class JobStatusResponse {
  @Field()
  jobId: string;

  @Field()
  status: string;

  @Field()
  message: string;

  @Field(() => Float)
  progress: number;

  @Field({ nullable: true })
  result?: string;

  @Field({ nullable: true })
  error?: string;
}
