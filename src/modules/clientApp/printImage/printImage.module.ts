import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PrintImageService } from './printImage.service';
import { PrintImageResolver } from './printImage.resolver';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { SBLogger } from 'src/modules/logger/logger.service';
import { JwtService } from '@nestjs/jwt';

@Module({
  imports: [TypeOrmModule.forFeature([])],
  providers: [
    JwtService,
    PrintImageService,
    PrintImageResolver,
    FirestoreService,
    SBLogger,
  ],
  exports: [PrintImageService],
})
export class PrintImageModule {}
