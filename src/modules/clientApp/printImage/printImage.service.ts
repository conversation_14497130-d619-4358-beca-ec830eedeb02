import { Injectable, BadRequestException } from '@nestjs/common';
import { SBLogger } from '../../logger/logger.service';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';

@Injectable()
export class PrintImageService {
  constructor(
    private readonly loggerService: SBLogger,
    private readonly firestoreService: FirestoreService,
  ) {}

  async deleteDocumentOnFirestore(
    machineId: string,
  ): Promise<{ message: string }> {
    try {
      const documentId = machineId;

      const existingData = await this.firestoreService.getData(
        'ImagePrint',
        documentId,
      );

      if (!existingData) {
        return { message: 'No print job found to delete.' };
      }

      await this.firestoreService.deleteDocument('ImagePrint', documentId);
      return { message: 'Print job deleted successfully from Firestore.' };
    } catch (error) {
      this.loggerService.log(
        `Error deleting print job from Firestore: ${error.message}`,
      );
      throw new BadRequestException('Failed to delete print job.');
    }
  }
}
