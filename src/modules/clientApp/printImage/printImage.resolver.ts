import { Resolver, Mutation, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { PrintImageService } from './printImage.service';
import { ClientAppPrintImageResponse } from './printImage.input';

@Resolver()
@UseGuards(ClientAppAuthGuard)
export class PrintImageResolver {
  constructor(private readonly printImageService: PrintImageService) {}

  @Mutation(() => ClientAppPrintImageResponse)
  async clientAppPrintImage(
    @Context() context: any,
  ): Promise<ClientAppPrintImageResponse> {
    return await this.printImageService.deleteDocumentOnFirestore(
      context.req.user.machineId,
    );
  }
}
