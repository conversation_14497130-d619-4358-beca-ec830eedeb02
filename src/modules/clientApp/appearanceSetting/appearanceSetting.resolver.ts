import { Resolver, Query, Context } from '@nestjs/graphql';
import { AppearanceSettingResponse } from './appearanceSetting.input';
import { UseGuards } from '@nestjs/common';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { AppearanceSettingService } from './appearanceSetting.service';

@Resolver()
@UseGuards(ClientAppAuthGuard)
export class AppearanceSettingResolver {
  constructor(
    private readonly appearanceSettingService: AppearanceSettingService,
  ) {}

  @Query(() => AppearanceSettingResponse, { nullable: true })
  async clientAppGetAppearanceSetting(
    @Context() context: any,
  ): Promise<AppearanceSettingResponse | null> {
    return this.appearanceSettingService.findOne(
      context.req.user.id,
      context.req.user.machineId,
    );
  }
}
