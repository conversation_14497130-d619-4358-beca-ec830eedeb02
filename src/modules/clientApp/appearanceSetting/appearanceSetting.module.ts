import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { AppearanceSetting } from '../../common/entity/appearanceSetting.entity';
import { AppearanceSettingService } from './appearanceSetting.service';
import { AppearanceSettingResolver } from './appearanceSetting.resolver';
import { Client } from '../../common/entity/client.entity';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { FileService } from 'src/modules/common/module/file/file.service';

@Module({
  imports: [TypeOrmModule.forFeature([AppearanceSetting, Client, Machine])],
  providers: [
    AppearanceSettingService,
    AppearanceSettingResolver,
    JwtService,
    SBLogger,
    FileService,
  ],
  exports: [AppearanceSettingService],
})
export class AppearanceSettingModule {}
