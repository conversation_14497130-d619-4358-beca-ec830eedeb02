import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SBLogger } from '../../logger/logger.service';
import { AppearanceSetting } from '../../common/entity/appearanceSetting.entity';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { FileService } from 'src/modules/common/module/file/file.service';
import { AppearanceSettingResponse } from './appearanceSetting.input';

@Injectable()
export class AppearanceSettingService {
  constructor(
    @InjectRepository(AppearanceSetting)
    private appearanceSettingRepository: Repository<AppearanceSetting>,
    @InjectRepository(Machine)
    private machineRepository: Repository<Machine>,
    private loggerService: SBLogger,
    private fileService: FileService,
  ) {}

  async findOne(
    clientId: string,
    machineId: string,
  ): Promise<AppearanceSettingResponse> {
    const result = await this.appearanceSettingRepository.findOne({
      where: { clientId },
    });

    const machine = await this.machineRepository.findOne({
      where: { id: machineId, userId: clientId },
    });
    return {
      ...result,
      havePayScreen: machine?.havePayScreen ?? null,
    };
  }
}
