import { Field, ID, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class AppearanceSettingResponse {
  @Field(() => ID)
  id: string;

  @Field({ nullable: true })
  primary_color?: string;

  @Field({ nullable: true })
  secondary_color?: string;

  @Field({ nullable: true })
  background_color?: string;

  @Field({ nullable: true })
  primary_text_color?: string;

  @Field({ nullable: true })
  secondary_text_color?: string;

  @Field({ nullable: true })
  secondary_text_color_2?: string;

  @Field({ nullable: true })
  logo?: string;

  @Field({ nullable: true })
  background?: string;

  @Field({ nullable: true })
  havePayScreen?: boolean;

  @Field({ nullable: true })
  clientId: string;

  @Field({ nullable: true })
  createdAt: Date;

  @Field({ nullable: true })
  updatedAt: Date;
}
