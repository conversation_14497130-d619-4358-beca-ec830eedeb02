import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { TopicService } from './topic.service';
import { Topic } from '../../common/entity/topic.entity';
import { UseGuards } from '@nestjs/common';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
@Resolver(() => Topic)
@UseGuards(ClientAppAuthGuard)
export class TopicResolver {
  constructor(private readonly topicService: TopicService) {}

  @Query(() => [Topic])
  async clientAppGetTopics(@Context() context: any): Promise<Topic[]> {
    return this.topicService.findAll(context.req.user.id);
  }

  @Query(() => Topic)
  async clientAppGetTopic(
    @Args('id') id: string,
    @Context() context: any,
  ): Promise<Topic> {
    return this.topicService.findOne(id, context.req.user.id);
  }
}
