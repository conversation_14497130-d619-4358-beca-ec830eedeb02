import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TopicService } from './topic.service';
import { TopicResolver } from './topic.resolver';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Topic } from '../../common/entity/topic.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Topic])],
  providers: [TopicService, TopicResolver, JwtService, SBLogger],
  exports: [TopicService],
})
export class TopicModule {}
