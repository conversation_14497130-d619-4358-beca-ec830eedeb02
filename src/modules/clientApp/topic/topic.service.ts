import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Topic } from '../../common/entity/topic.entity';

@Injectable()
export class TopicService {
  constructor(
    @InjectRepository(Topic)
    private readonly topicRepository: Repository<Topic>,
  ) {}

  async findAll(clientId: string): Promise<Topic[]> {
    return this.topicRepository.find({ where: { clientId } });
  }

  async findOne(id: string, clientId: string): Promise<Topic> {
    return this.topicRepository.findOne({ where: { id, clientId } });
  }
}
