interface AppOrderBase {
  promotionCode: String
  settingSizeId: String!
  settingSizeKey: String!
  frameId: String!
  topicId: String!
}

type AppOrderOnlInput implements AppOrderBase {}
type AppOrderOnlCashInput implements AppOrderBase {
  receivedAmount: Float!
}

type AttemptPaymentResponse {
  qrCode: String
  skipPayment: Boolean
  refCode: String
}

type AttemptPaymentCashResponse {
  orderId: String!
  domain: String!
}

type Mutation {
  clientAppCreateOrderPayOnline(input: AppOrderOnlInput): AttemptPaymentResponse
  clientAppCreateOrderPayWithCash(input: AppOrderOnlCashInput): AttemptPaymentCashResponse
}
