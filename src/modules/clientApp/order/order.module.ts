import { Module } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OrderService } from './order.service';
import { OrderResolver } from './order.resolver';
import { SBLogger } from 'src/modules/logger/logger.service';
import { PayosService } from 'src/modules/common/module/payos/payos.service';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { PromotionModule } from '../../common/module/promotion/promotion.module';
import { AppOrderModule } from '../../common/module/appOrder/appOrder.module';
import { Order } from './order.entity';
import { PaymentAccountSetting } from '../../common/entity/paymentAccountSetting.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Order, PaymentAccountSetting]),
    PromotionModule,
    AppOrderModule,
  ],
  providers: [
    JwtService,
    SBLogger,
    PayosService,
    FirestoreService,
    OrderResolver,
    OrderService,
  ],
  exports: [OrderService],
})
export class OrderModule {}
