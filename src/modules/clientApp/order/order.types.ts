import { registerEnumType } from '@nestjs/graphql';
import {
  EOrderAppStatus,
  EPaymentMethod,
  ECaptureMode,
  EPaymentProviderType,
} from '../../../enum/index';

// Register GraphQL enums for Order entity
registerEnumType(EOrderAppStatus, {
  name: 'EOrderAppStatus',
});

registerEnumType(EPaymentMethod, {
  name: 'EPaymentMethod',
});

registerEnumType(ECaptureMode, {
  name: 'ECaptureMode',
});

registerEnumType(EPaymentProviderType, {
  name: 'EPaymentProviderType',
});
