import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { PayosService } from 'src/modules/common/module/payos/payos.service';
import { Repository } from 'typeorm';
import {
  EOrderAppStatus,
  EPaymentMethod,
  EPaymentProviderType,
} from '../../../enum/index';
import { PaymentAccountSetting } from '../../common/entity/paymentAccountSetting.entity';
import { MBank } from '../../common/entity/mBank.entity';
import { AppOrderService } from '../../common/module/appOrder/appOrder.service';
import { PromotionService } from '../../common/module/promotion/promotion.service';
import { Order } from './order.entity';
import {
  AppOrderOnlineCashInput,
  AppOrderOnlineInput,
  AttemptPaymentCashResponse,
  AttemptPaymentResponse,
} from './order.input';
import { EPaymentAccountSettingType } from 'src/modules/admin/paymentAccountSetting/paymentAccountSetting.input';
import { AutobankService } from '../../common/module/autobank/autobank.service';
@Injectable()
export class OrderService {
  constructor(
    private firestoreService: FirestoreService,
    private readonly promotionService: PromotionService,
    private readonly appOrderService: AppOrderService,
    @InjectRepository(Order)
    private readonly orderRepository: Repository<Order>,
    @InjectRepository(PaymentAccountSetting)
    private readonly paymentAccountSettingRepository: Repository<PaymentAccountSetting>,
  ) {}

  async createOrderPayOnline(
    input: AppOrderOnlineInput,
    userId: string,
    machineId: string,
    machineCode: string,
  ): Promise<AttemptPaymentResponse> {
    const {
      totalPrice,
      totalPriceAfterDiscount,
      promotionCodeRecord,
      promotion,
      settingSize,
      topic,
      frame,
    } = await this.appOrderService.prepareOrderDependencies(
      input,
      userId,
      machineId,
      false, // No need to validate received amount for online payment
    );
    let refCode = '';
    let qrCode;
    let qrCodeUrl;
    let skipPayment = true;
    let paymentProviderType = EPaymentProviderType.OTHER;

    if (totalPriceAfterDiscount >= 500) {
      const paySetting = await this.getActivePaymentSetting(userId);
      if (paySetting.type === EPaymentAccountSettingType.PAYOS) {
        try {
          const payOsSession = new PayosService();
          const payOsResp = await payOsSession.makePayment(
            machineId,
            machineCode,
            totalPriceAfterDiscount,
            paySetting.apiId,
            paySetting.apiKey,
            paySetting.checkSum,
          );
          skipPayment = false;
          refCode = payOsResp.refCode;
          qrCode = payOsResp.qrCode;
          paymentProviderType = EPaymentProviderType.PAYOS;
        } catch (error) {
          throw error;
        }
      }

      if (paySetting.type === EPaymentAccountSettingType.BANK) {
        const autobankService = new AutobankService();
        const autobankResp = await autobankService.makePayment(
          machineId,
          totalPriceAfterDiscount,
          userId,
          paySetting.bankAccountNumber,
          paySetting.bankOwnerName,
          paySetting.mBank.code,
        );
        skipPayment = false;
        refCode = autobankResp.refCode;
        qrCodeUrl = autobankResp.qrCodeUrl;
        paymentProviderType = EPaymentProviderType.AUTOBANK;
      }
    }

    const orderData = this.prepareOrderData(
      qrCode || qrCodeUrl,
      totalPrice,
      totalPriceAfterDiscount,
      machineId,
      userId,
      promotionCodeRecord?.id,
      refCode,
      frame?.id,
      topic?.id,
      settingSize?.id,
      promotion?.id,
      input.settingSizeKey,
      paymentProviderType,
    );
    await this.pushOrderToFirestore(userId, machineId, orderData);
    return { qrCode, skipPayment, refCode, qrCodeUrl };
  }

  async createOrderPayOnlineWithCash(
    input: AppOrderOnlineCashInput,
    userId: string,
    machineId: string,
  ): Promise<AttemptPaymentCashResponse> {
    const {
      totalPrice,
      totalPriceAfterDiscount,
      promotionCodeRecord,
      promotion,
      settingSize,
      topic,
      frame,
    } = await this.appOrderService.prepareOrderDependencies(
      input,
      userId,
      machineId,
      true, // Enable validation of received amount
    );

    const orderCode = await this.appOrderService.generateQRCode(
      machineId,
      userId,
    );

    const newOrder = this.orderRepository.create({
      orderCode: orderCode,
      refCode: '',
      amount: totalPrice,
      receivedAmount: input.receivedAmount,
      totalOrderNumber: totalPriceAfterDiscount,
      description: '',
      status: EOrderAppStatus.DELIVERED,
      paymentMethod: EPaymentMethod.OFFLINE,
      paymentProviderType: EPaymentProviderType.OTHER,
      machineId: machineId,
      clientId: userId,
      settingSizeId: settingSize?.id,
      frameId: frame?.id,
      topicId: topic?.id,
      promotionId: promotion?.id,
      promotionCodeId: promotionCodeRecord?.id,
      imageNumber: this.appOrderService.extractNumbersFromString(
        input.settingSizeKey,
      ),
      denominations: input.denominations,
    });

    try {
      const savedOrder = await this.orderRepository.save(newOrder);
      if (input.promotionCode && promotionCodeRecord) {
        await this.promotionService.updatePromotionCode(
          promotionCodeRecord.id,
          promotionCodeRecord,
          promotion,
        );
      }

      return {
        orderId: savedOrder.id,
        domain: `${process.env.MEMORY_DOMAIN}${savedOrder.id}`,
      };
    } catch (error) {
      console.error('Failed to create new order in database:', error);
      throw error;
    }
  }

  private prepareOrderData(
    qrCode: string,
    totalPrice: number,
    totalPriceAfterDiscount: number,
    machineId: string,
    clientId: string,
    promotionCodeId: string | null,
    refCode: string,
    frameId: string | null,
    topicId: string | null,
    settingSizeId: string | null,
    promotionId: string | null,
    settingSizeKey: string | null,
    paymentProviderType: EPaymentProviderType,
    denominations?: string,
  ): Record<string, any> {
    return {
      orderCode: qrCode,
      amount: totalPrice,
      totalPriceAfterDiscount: totalPriceAfterDiscount,
      receivedAmount: 0,
      status: EOrderAppStatus.PENDING,
      paymentMethod: EPaymentMethod.ONLINE,
      paymentProviderType: paymentProviderType,
      machineId,
      clientId,
      promotionCodeId: promotionCodeId || 'not found',
      refCode,
      settingSizeId: settingSizeId || 'not found',
      frameId: frameId || 'not found',
      topicId: topicId || 'not found',
      promotionId: promotionId || 'not found',
      imageNumber:
        this.appOrderService.extractNumbersFromString(settingSizeKey),
      denominations: denominations || null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  private async pushOrderToFirestore(
    userId: string,
    machineId: string,
    orderData: Record<string, any>,
  ): Promise<void> {
    try {
      await this.firestoreService.pushData(userId, machineId, orderData, true);
    } catch (error) {
      console.error('Failed to push order data to Firestore:', error.message);
      throw new BadRequestException('Could not save order data to Firestore.');
    }
  }

  private async getActivePaymentSetting(
    userId: string,
  ): Promise<PaymentAccountSetting> {
    const paySetting = await this.paymentAccountSettingRepository.findOne({
      where: {
        clientId: userId,
        isActive: true,
      },
      relations: ['mBank'],
    });

    if (!paySetting) {
      throw new Error('No active payment setting found for this user.');
    }

    return paySetting;
  }
}
