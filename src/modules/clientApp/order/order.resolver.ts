import { Resolver, Mutation, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { OrderService } from './order.service';
import {
  AttemptPaymentResponse,
  AttemptPaymentCashResponse,
} from './order.input';
import { AppOrderOnlineInput, AppOrderOnlineCashInput } from './order.input';
import { Order } from 'src/modules/admin/order/order.entity';

@Resolver(() => Order)
@UseGuards(ClientAppAuthGuard)
export class OrderResolver {
  constructor(private readonly orderService: OrderService) {}

  @Mutation(() => AttemptPaymentResponse, {
    name: 'clientAppCreateOrderPayOnline',
  })
  async clientAppCreateOrderPayOnline(
    @Args('input') input: AppOrderOnlineInput,
    @Context() context: any,
  ): Promise<AttemptPaymentResponse> {
    const { id: userId, machineId, machineCode } = context.req.user;
    return await this.orderService.createOrderPayOnline(
      input,
      userId,
      machineId,
      machineCode,
    );
  }

  @Mutation(() => AttemptPaymentCashResponse, {
    name: 'clientAppCreateOrderPayWithCash',
  })
  async clientAppCreateOrderPayWithCash(
    @Args('input') input: AppOrderOnlineCashInput,
    @Context() context: any,
  ): Promise<AttemptPaymentCashResponse> {
    const { id: userId, machineId } = context.req.user;
    return await this.orderService.createOrderPayOnlineWithCash(
      input,
      userId,
      machineId,
    );
  }
}
