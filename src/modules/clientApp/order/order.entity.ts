import {
  En<PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
} from 'typeorm';
import {
  EOrderAppStatus,
  EPaymentMethod,
  ECaptureMode,
  EPaymentProviderType,
} from '../../../enum/index';
import { PromotionCode } from '../../common/entity/promotionCode.entity';
import { Field, ObjectType } from '@nestjs/graphql';
import { Frame } from '../../common/entity/frame.entity';
import './order.types'; // Import để register enum
import { Topic } from '../../common/entity/topic.entity';
import { SettingSize } from '../../common/entity/settingSize.entity';
import { Machine } from '../../common/entity/machine.entity';
import { Client } from '../../common/entity/client.entity';
import { AppImage } from '../uploadImage/image.entity';

@Entity('app_order')
@ObjectType('app_order')
export class Order {
  @PrimaryGeneratedColumn('uuid')
  @Field()
  id: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  @Field({ nullable: true })
  orderCode: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  @Field({ nullable: true })
  amount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  @Field({ nullable: true })
  totalOrderNumber: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  @Field({ nullable: true })
  receivedAmount: number;

  @Column({ type: 'text', nullable: true })
  @Field({ nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 255 })
  @Field({ nullable: true })
  refCode: string;

  @Column({
    type: 'int',
    default: 0,
    nullable: false,
  })
  @Field({ nullable: true })
  imageNumber: number;

  @Column({
    type: 'enum',
    enum: EOrderAppStatus,
    default: EOrderAppStatus.PENDING,
  })
  @Field({ nullable: true })
  status: EOrderAppStatus;

  @Column({
    type: 'enum',
    enum: EPaymentMethod,
    default: EPaymentMethod.OFFLINE,
  })
  @Field({ nullable: true })
  paymentMethod: EPaymentMethod;

  @Column({
    type: 'enum',
    enum: EPaymentProviderType,
    nullable: true,
  })
  @Field({ nullable: true })
  paymentProviderType: EPaymentProviderType;

  @Column({
    type: 'enum',
    enum: ECaptureMode,
    default: ECaptureMode.AUTO,
  })
  @Field({ nullable: true })
  captureMode: ECaptureMode;

  @Column({
    type: 'uuid',
    nullable: false,
    default: '',
  })
  @Field({ nullable: true })
  machineId: string;

  @Column({
    type: 'uuid',
    nullable: false,
    default: '',
  })
  @Field({ nullable: true })
  clientId: string;

  @Column({
    type: 'uuid',
    nullable: false,
    default: '',
  })
  @Field({ nullable: true })
  settingSizeId: string;

  @Column({ type: 'uuid', nullable: true })
  @Field({ nullable: true })
  promotionId: string | null;

  @Column({ type: 'uuid', nullable: true })
  @Field({ nullable: true })
  promotionCodeId: string | null;

  @Column({ type: 'uuid', nullable: true })
  @Field({ nullable: true })
  topicId: string | null;

  @Column({
    type: 'uuid',
    nullable: false,
    default: '',
  })
  @Field({ nullable: true })
  frameId: string;

  @Column({
    type: 'json',
    nullable: true,
  })
  @Field({ nullable: true })
  denominations?: string;

  @CreateDateColumn()
  @Field({ nullable: true })
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => PromotionCode, (promotionCode) => promotionCode.orders, {
    onDelete: 'CASCADE',
  })
  @Field(() => PromotionCode, { nullable: true })
  promotionCode: PromotionCode;

  @ManyToOne(() => Frame, (frame) => frame.orders)
  @Field(() => Frame, { nullable: true })
  frame: Frame;

  @ManyToOne(() => Topic, (topic) => topic.orders)
  @Field(() => Topic, { nullable: true })
  topic: Topic;

  @ManyToOne(() => SettingSize, (settingSize) => settingSize.orders)
  @Field(() => SettingSize, { nullable: true })
  settingSize: SettingSize;

  @ManyToOne(() => Machine, (machine) => machine.orders)
  @Field(() => Machine, { nullable: true })
  machine: Machine;

  @ManyToOne(() => Client, { onDelete: 'CASCADE' })
  @Field(() => Client, { nullable: true })
  client: Client;

  @OneToMany(() => AppImage, (appImage) => appImage.order, { cascade: true })
  @Field(() => [AppImage], { nullable: true })
  images: AppImage[];
}
