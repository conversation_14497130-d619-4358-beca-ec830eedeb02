import { Field, InputType, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class AttemptPaymentResponse {
  @Field({ nullable: true })
  qrCode?: string;

  @Field({ nullable: true })
  refCode: string;

  @Field({ nullable: true })
  skipPayment: boolean;

  @Field({ nullable: true })
  qrCodeUrl?: string;
}

@ObjectType()
export class AttemptPaymentCashResponse {
  @Field()
  orderId: string;

  @Field()
  domain: string;
}

@InputType()
export class AppOrderOnlineInput {
  @Field({ nullable: true })
  promotionCode?: string;

  @Field({ nullable: false })
  settingSizeId: string;

  @Field({ nullable: false })
  settingSizeKey: string;

  @Field({ nullable: false })
  frameId: string;

  @Field({ nullable: false })
  topicId: string;
}

@InputType()
export class AppOrderOnlineCashInput extends AppOrderOnlineInput {
  @Field({ nullable: true })
  receivedAmount?: number;

  @Field({ nullable: true })
  denominations?: string;
}
