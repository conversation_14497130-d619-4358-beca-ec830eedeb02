import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { MachineStatus } from 'src/modules/common/entity/machineStatus.entity';
import { MachineStatusService } from './machineStatus.service';
import { MachineStatusResolver } from './machineStatus.resolver';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';

@Module({
  imports: [TypeOrmModule.forFeature([MachineStatus])],
  providers: [MachineStatusService, JwtService, SBLogger, MachineStatusResolver, FirestoreService],
  exports: [],
})
export class MachineStatusModule {}