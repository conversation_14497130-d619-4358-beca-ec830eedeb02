import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MachineStatus } from 'src/modules/common/entity/machineStatus.entity';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import * as moment from 'moment';

@Injectable()
export class MachineStatusService {
  constructor(
    @InjectRepository(MachineStatus)
    private readonly machineStatusRepository: Repository<MachineStatus>,
    private firestoreService: FirestoreService,
  ) {}

  async updateMachineStatus(machineId, status, clientId): Promise<boolean> {
    try {
      let machineStatus = await this.machineStatusRepository.findOne({
        where: {
          clientId: clientId,
          machineId: machineId,
        },
      });

      if (!machineStatus) {
        machineStatus = this.machineStatusRepository.create({
          clientId: clientId,
          machineId: machineId,
        });
        await this.machineStatusRepository.save(machineStatus);
      }

      machineStatus.status = status;
      machineStatus.syncDate = moment.utc().valueOf().toString();
      machineStatus.isUpdated = false;

      await this.machineStatusRepository.save(machineStatus);
      await this.pushFirestoreSync(machineId);

      return true;
    } catch (error) {
      console.error('Error in updateMachineStatus:', error);
      throw new BadRequestException(error.message);
    }
  }

  private async pushFirestoreSync(machineId: string): Promise<any> {
    try {
      const data = {
        isUpdated: false,
      };
      await this.firestoreService.updateDocument(
        `SyncMachines`,
        machineId,
        data,
      );
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
