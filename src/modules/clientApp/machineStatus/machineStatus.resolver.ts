import { Resolver, Args, Context, Mutation } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { MachineStatusService } from './machineStatus.service';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';

@Resolver()
@UseGuards(ClientAppAuthGuard)
export class MachineStatusResolver {
  constructor(private readonly machineStatusService: MachineStatusService) {}

  @Mutation(() => Boolean)
  clientAppUpdateMachineStatus(
    @Context() context: any,
    @Args('status') status: string,
  ): Promise<boolean> {
    return this.machineStatusService.updateMachineStatus(
      context.req.user.machineId,
      status,
      context.req.user.id,
    );
  }
}
