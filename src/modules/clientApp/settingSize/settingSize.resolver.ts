import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { SettingSize } from '../../common/entity/settingSize.entity';
import { UseGuards } from '@nestjs/common';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { SettingSizeService } from './settingSize.service';
@Resolver(() => SettingSize)
@UseGuards(ClientAppAuthGuard)
export class SettingSizeResolver {
  constructor(private readonly settingSizeService: SettingSizeService) {}

  @Query(() => [SettingSize])
  async clientAppGetSettingSizes(
    @Context() context: any,
  ): Promise<SettingSize[]> {
    return this.settingSizeService.findAll(
      context.req.user.id,
      context.req.user.machineId,
    );
  }

  @Query(() => SettingSize)
  async clientAppGetSettingSize(
    @Args('id') id: string,
    @Context() context: any,
  ): Promise<SettingSize> {
    return this.settingSizeService.findOne(
      id,
      context.req.user.id,
      context.req.user.machineId,
    );
  }
}
