type SettingSize {
  id: ID!
  name: String!
  settingSizeType: String!
  machineIds: String
  clientId: String
  smallSizePrice2: Int
  smallSizePrice4: Int
  smallSizePrice6: Int
  smallSizePrice8: Int
  smallSizePrice10: Int
  largeSizePrice2: Int
  largeSizePrice3: Int
  largeSizePrice4: Int
  largeSizePrice5: Int
  largeSizePrice6: Int
  createdAt: String
  updatedAt: String
}

type Query {
  clientAppGetSettingSizes: [SettingSize!]!
  clientAppGetSettingSize(id: ID!): SettingSize!
}

type Mutation {
}
