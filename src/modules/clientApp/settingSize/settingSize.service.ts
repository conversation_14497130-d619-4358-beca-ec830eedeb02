import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SettingSize } from 'src/modules/common/entity/settingSize.entity';
import { ESettingSizeType } from 'src/enum';

@Injectable()
export class SettingSizeService {
  constructor(
    @InjectRepository(SettingSize)
    private readonly settingSizeRepository: Repository<SettingSize>,
  ) {}

  async findAll(clientId: string, machineId: string): Promise<SettingSize[]> {
    const customSettingSizes = await this.settingSizeRepository
      .createQueryBuilder('settingSize')
      .where('settingSize.clientId = :clientId', { clientId })
      .andWhere('settingSize.settingSizeType = :settingSizeType', {
        settingSizeType: ESettingSizeType.Custom,
      })
      .andWhere('FIND_IN_SET(:machineId, settingSize.machineIds) > 0', {
        machineId,
      })
      .getMany();

    if (customSettingSizes.length > 0) {
      return customSettingSizes;
    }

    const defaultSettingSizes = await this.settingSizeRepository.find({
      where: {
        clientId,
        settingSizeType: ESettingSizeType.Default,
      },
    });

    return defaultSettingSizes;
  }

  async findOne(
    id: string,
    clientId: string,
    machineId: string,
  ): Promise<SettingSize> {
    const customSettingSize = await this.settingSizeRepository
      .createQueryBuilder('settingSize')
      .where('settingSize.id = :id', { id })
      .andWhere('settingSize.clientId = :clientId', { clientId })
      .andWhere('settingSize.settingSizeType = :settingSizeType', {
        settingSizeType: ESettingSizeType.Custom,
      })
      .andWhere('FIND_IN_SET(:machineId, settingSize.machineIds) > 0', {
        machineId,
      })
      .getOne();

    if (customSettingSize) {
      return customSettingSize;
    }

    const defaultSettingSize = await this.settingSizeRepository.findOne({
      where: {
        id,
        clientId,
        settingSizeType: ESettingSizeType.Default,
      },
    });

    return defaultSettingSize;
  }
}
