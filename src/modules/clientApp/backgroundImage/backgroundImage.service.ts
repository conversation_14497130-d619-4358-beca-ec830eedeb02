import { Injectable, Logger } from '@nestjs/common';
import { CommonBackgroundImageService } from '../../common/service/backgroundImage.service';
import {
  ClientAppBackgroundImageListInput,
  ClientAppBackgroundImageListResponse,
} from './backgroundImage.input';

@Injectable()
export class ClientAppBackgroundImageService {
  private readonly logger = new Logger(ClientAppBackgroundImageService.name);

  constructor(
    private commonBackgroundImageService: CommonBackgroundImageService,
  ) {}

  async getBackgroundImages(
    input: ClientAppBackgroundImageListInput,
    clientId: string,
  ): Promise<ClientAppBackgroundImageListResponse> {
    try {
      this.logger.log(
        `Getting all background images for clientApp: ${clientId}`,
      );

      // Get all images without pagination - ignore input._dummy
      const result =
        await this.commonBackgroundImageService.getBackgroundImages({
          clientId,
          limit: 999999, // Get all images
          offset: 0,
          orderBy: 'createdAt',
          orderDirection: 'DESC',
        });

      return {
        images: result.images.map((image) => ({
          id: image.id,
          fileName: image.fileName,
          fileUrl: image.fileUrl,
          description: image.description,
          createdAt: image.createdAt,
          updatedAt: image.updatedAt,
        })),
        total: result.total,
        message:
          result.images.length > 0
            ? `Retrieved all ${result.images.length} background images successfully`
            : 'No background images found',
      };
    } catch (error) {
      this.logger.error(
        `Failed to get background images for clientApp: ${error.message}`,
      );
      throw error;
    }
  }

  async getBackgroundImagesCount(clientId: string): Promise<number> {
    try {
      return await this.commonBackgroundImageService.getBackgroundImagesCount(
        clientId,
      );
    } catch (error) {
      this.logger.error(
        `Failed to get background images count: ${error.message}`,
      );
      return 0;
    }
  }
}
