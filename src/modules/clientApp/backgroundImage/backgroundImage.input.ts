import { Field, InputType, ObjectType, Int } from '@nestjs/graphql';

@InputType()
export class ClientAppBackgroundImageListInput {
  @Field({ nullable: true })
  _dummy?: string; // Required by GraphQL - not used in logic
}

@ObjectType()
export class ClientAppBackgroundImageDto {
  @Field()
  id: string;

  @Field()
  fileName: string;

  @Field()
  fileUrl: string;

  @Field({ nullable: true })
  description?: string;

  @Field()
  createdAt: Date;

  @Field()
  updatedAt: Date;
}

@ObjectType()
export class ClientAppBackgroundImageListResponse {
  @Field(() => [ClientAppBackgroundImageDto])
  images: ClientAppBackgroundImageDto[];

  @Field(() => Int)
  total: number;

  @Field()
  message: string;
}
