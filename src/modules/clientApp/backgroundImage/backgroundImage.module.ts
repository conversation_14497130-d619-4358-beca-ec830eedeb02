import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { BackgroundImage } from '../../common/entity/backgroundImage.entity';
import { ClientAppBackgroundImageResolver } from './backgroundImage.resolver';
import { ClientAppBackgroundImageService } from './backgroundImage.service';
import { CommonBackgroundImageService } from '../../common/service/backgroundImage.service';
import { SBLogger } from '../../logger/logger.service';

@Module({
  imports: [TypeOrmModule.forFeature([BackgroundImage])],
  providers: [
    ClientAppBackgroundImageResolver,
    ClientAppBackgroundImageService,
    CommonBackgroundImageService,
    JwtService,
    SBLogger,
  ],
  exports: [ClientAppBackgroundImageService],
})
export class ClientAppBackgroundImageModule {}
