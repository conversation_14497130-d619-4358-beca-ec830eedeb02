input ClientAppBackgroundImageListInput {
  _dummy: String # Required by GraphQL - not used in logic
}

type ClientAppBackgroundImageDto {
  id: String!
  fileName: String!
  fileUrl: String!
  description: String
  createdAt: DateTime!
  updatedAt: DateTime!
}

type ClientAppBackgroundImageListResponse {
  images: [ClientAppBackgroundImageDto!]!
  total: Int!
  message: String!
}

type Query {
  clientAppGetBackgroundImages(input: ClientAppBackgroundImageListInput): ClientAppBackgroundImageListResponse!
}
