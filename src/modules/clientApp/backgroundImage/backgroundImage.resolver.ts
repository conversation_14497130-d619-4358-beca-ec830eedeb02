import { Resolver, Query, Args, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { ClientAppAuthGuard } from '../../../guards/clientAppAuth.guard';
import { ClientAppBackgroundImageService } from './backgroundImage.service';
import {
  ClientAppBackgroundImageListInput,
  ClientAppBackgroundImageListResponse,
} from './backgroundImage.input';

@Resolver()
@UseGuards(ClientAppAuthGuard)
export class ClientAppBackgroundImageResolver {
  constructor(
    private readonly clientAppBackgroundImageService: ClientAppBackgroundImageService,
  ) {}

  @Query(() => ClientAppBackgroundImageListResponse)
  async clientAppGetBackgroundImages(
    @Args('input', { nullable: true })
    input: ClientAppBackgroundImageListInput = {},
    @Context() context: any,
  ): Promise<ClientAppBackgroundImageListResponse> {
    const clientId = context.req.user.id;
    return this.clientAppBackgroundImageService.getBackgroundImages(
      input,
      clientId,
    );
  }
}
