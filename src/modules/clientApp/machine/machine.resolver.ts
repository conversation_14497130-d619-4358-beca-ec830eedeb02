import { Resolver, Context, Mutation, Args } from '@nestjs/graphql';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { UseGuards } from '@nestjs/common';
import { MachineService } from 'src/modules/clientApp/machine/machine.service';
import { Machine } from 'src/modules/common/entity/machine.entity';
import {
  ClientAppUpdateMachineInput,
  ClientAppUpdateMachineResponse,
} from './machine.input';

@Resolver(() => Machine)
@UseGuards(ClientAppAuthGuard)
export class MachineResolver {
  constructor(private readonly machineService: MachineService) {}

  @Mutation(() => Boolean)
  async clientAppUpdateLastPing(@Context() context: any): Promise<boolean> {
    return this.machineService.updateLastPing(context.req.user.machineId);
  }

  @Mutation(() => ClientAppUpdateMachineResponse)
  async clientAppUpdateMachine(
    @Args('input') input: ClientAppUpdateMachineInput,
    @Context() context: any,
  ): Promise<ClientAppUpdateMachineResponse> {
    return this.machineService.updateMachine(context.req.user.machineId, input);
  }
}
