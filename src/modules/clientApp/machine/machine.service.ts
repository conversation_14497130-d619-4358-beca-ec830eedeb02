import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Machine } from '../../common/entity/machine.entity';
import {
  ClientAppUpdateMachineInput,
  ClientAppUpdateMachineResponse,
} from './machine.input';

@Injectable()
export class MachineService {
  constructor(
    @InjectRepository(Machine)
    private machineRepository: Repository<Machine>,
  ) {}

  async updateLastPing(id: string): Promise<boolean> {
    const machine = await this.machineRepository.findOne({ where: { id: id } });
    const now = Date.now().toString();
    if (!machine) {
      throw new NotFoundException('Machine not found');
    }
    await this.machineRepository.update(id, { lastPingAt: now });
    return true;
  }

  async updateMachine(
    id: string,
    input: ClientAppUpdateMachineInput,
  ): Promise<ClientAppUpdateMachineResponse> {
    const machine = await this.machineRepository.findOne({ where: { id } });

    if (!machine) {
      throw new NotFoundException('Machine not found');
    }

    await this.machineRepository.update(id, {
      pendingPrints: input.pendingPrints,
      remainingMedia: input.remainingMedia,
      remainingInk: input.remainingInk,
      state: input.state,
    });

    return { success: true };
  }
}
