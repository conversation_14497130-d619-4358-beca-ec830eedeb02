import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { MachineResolver } from './machine.resolver';
import { MachineService } from './machine.service';

@Module({
  imports: [TypeOrmModule.forFeature([Machine])],
  providers: [MachineService, JwtService, SBLogger, MachineResolver],
  exports: [MachineService],
})
export class MachineModule {}
