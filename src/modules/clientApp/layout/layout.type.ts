import { Layout } from 'src/modules/common/entity/layout.entity';
import { LayoutFormat } from 'src/modules/common/entity/layoutFormat.entity';
import { LayoutItem } from 'src/modules/common/entity/layoutItem.entity';
import { Topic } from 'src/modules/common/entity/topic.entity';

export type TLayoutResponse = Layout & { formats: TLayoutFormatResponse[] };
export type TLayoutFormatResponse = LayoutFormat & {
  layoutItems: TLayoutItemResponse[];
};
export type TLayoutItemResponse = LayoutItem & { topic: Topic };
