import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Layout } from '../../common/entity/layout.entity';
import { LayoutService } from './layout.service';
import { LayoutResolver } from './layout.resolver';
import { LayoutFormat } from 'src/modules/common/entity/layoutFormat.entity';
import { LayoutItem } from 'src/modules/common/entity/layoutItem.entity';
import { Topic } from 'src/modules/common/entity/topic.entity';
@Module({
  imports: [
    TypeOrmModule.forFeature([Layout, LayoutFormat, LayoutItem, Topic]),
  ],
  providers: [LayoutService, LayoutResolver, JwtService, SBLogger],
  exports: [LayoutService],
})
export class LayoutModule {}
