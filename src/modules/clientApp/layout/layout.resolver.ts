import { Resolver, Query, Context } from '@nestjs/graphql';
import { Layout } from '../../common/entity/layout.entity';
import { UseGuards } from '@nestjs/common';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { LayoutService } from './layout.service';
@Resolver(() => Layout)
@UseGuards(ClientAppAuthGuard)
export class LayoutResolver {
  constructor(private readonly layoutService: LayoutService) {}

  @Query(() => [Layout])
  async clientAppGetLayouts(@Context() context: any): Promise<Layout[]> {
    return this.layoutService.findAll(
      context.req.user.id,
      context.req.user.machineId,
    );
  }
}
