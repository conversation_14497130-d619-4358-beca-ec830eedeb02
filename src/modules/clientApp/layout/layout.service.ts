import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Layout } from 'src/modules/common/entity/layout.entity';
import { TLayoutResponse } from './layout.type';
import { LayoutFormat } from 'src/modules/common/entity/layoutFormat.entity';
import { ELayoutType } from 'src/enum';
import { ELayoutStatus } from '../../../enum/index';
@Injectable()
export class LayoutService {
  constructor(
    @InjectRepository(Layout)
    private readonly layoutRepository: Repository<Layout>,

    @InjectRepository(LayoutFormat)
    private readonly layoutFormatRepository: Repository<LayoutFormat>,
  ) {}

  async findAll(clientId: string, machineId: string): Promise<Layout[]> {
    const customLayouts = await this.layoutRepository
      .createQueryBuilder('layout')
      .leftJoinAndSelect(
        'layout.formats',
        'format',
        'format.status = :status',
        {
          status: ELayoutStatus.ACTIVE,
        },
      )
      .leftJoinAndSelect('format.layoutItems', 'layoutItem')
      .leftJoinAndSelect('layoutItem.topic', 'topic')
      .where('layout.clientId = :clientId', { clientId })
      .andWhere('layout.layoutType = :layoutType', {
        layoutType: ELayoutType.Custom,
      })
      .andWhere('layout.machineIds = :machineId', { machineId })
      .andWhere('layout.status = :layoutStatus', {
        layoutStatus: ELayoutStatus.ACTIVE,
      })
      .getMany();

    if (customLayouts.length > 0) {
      return customLayouts;
    }

    const defaultLayouts = await this.layoutRepository
      .createQueryBuilder('layout')
      .leftJoinAndSelect(
        'layout.formats',
        'format',
        'format.status = :status',
        {
          status: ELayoutStatus.ACTIVE,
        },
      )
      .leftJoinAndSelect('format.layoutItems', 'layoutItem')
      .leftJoinAndSelect('layoutItem.topic', 'topic')
      .where('layout.clientId = :clientId', { clientId })
      .andWhere('layout.layoutType = :layoutType', {
        layoutType: ELayoutType.Default,
      })
      .getMany();

    return defaultLayouts;
  }
}
