type Layout {
  id: ID!
  name: String
  machineIds: String
  clientId: String
  status: String
  layoutType: String
  createdAt: String
  updatedAt: String
  formats: [LayoutFormat]
}

type LayoutFormat {
  id: ID!
  name: String
  imageCount: Int
  layoutItems: [LayoutItem]
}

type LayoutItem {
  id: ID!
  layoutFormatId: ID
  imageUrl: String
  topicId: ID
  position: Int
  topic: Topic
}

type Query {
  clientAppGetLayouts: [Layout!]!
}

type Mutation {
}
