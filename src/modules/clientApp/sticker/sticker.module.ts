import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Sticker } from 'src/modules/common/entity/sticker.entity';
import { StickerService } from './sticker.service';
import { FileService } from 'src/modules/common/module/file/file.service';
import { StickerResolver } from './sticker.resolver';
@Module({
  imports: [TypeOrmModule.forFeature([Sticker])],
  providers: [
    StickerService,
    JwtService,
    SBLogger,
    FileService,
    StickerResolver,
  ],
  exports: [StickerService],
})
export class StickerModule {}
