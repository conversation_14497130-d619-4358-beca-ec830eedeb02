import { Resolver, Query, Context } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { Sticker } from 'src/modules/common/entity/sticker.entity';
import { StickerService } from './sticker.service';

@Resolver(() => Sticker)
@UseGuards(ClientAppAuthGuard)
export class StickerResolver {
  constructor(private readonly stickerService: StickerService) {}

  @Query(() => [Sticker])
  async clientAppGetStickers(@Context() context: any): Promise<Sticker[]> {
    return this.stickerService.findAll(context.req.user.id);
  }
}
