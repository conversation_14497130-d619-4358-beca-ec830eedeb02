import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FileService } from 'src/modules/common/module/file/file.service';
import { Sticker } from 'src/modules/common/entity/sticker.entity';
@Injectable()
export class StickerService {
  constructor(
    @InjectRepository(Sticker)
    private readonly stickerRepository: Repository<Sticker>,
    private fileService: FileService,
  ) {}

  async findAll(clientId: string): Promise<Sticker[]> {
    return this.stickerRepository.find({ where: { clientId } });
  }
}
