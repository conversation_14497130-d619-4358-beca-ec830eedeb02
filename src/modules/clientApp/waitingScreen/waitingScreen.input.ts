import { Field } from '@nestjs/graphql';

import { ObjectType } from '@nestjs/graphql';
import { EWaitingScreenType } from 'src/enum';
import { WaitingScreenImage } from 'src/modules/common/entity/waitingScreenImage.entity';

@ObjectType()
export class AppWaitingScreenResponse {
  @Field()
  id: string;

  @Field({ nullable: true })
  name?: string;

  @Field(() => String, { nullable: true })
  machineIds?: string;

  @Field({ nullable: true })
  type?: EWaitingScreenType;

  @Field(() => [AppImageResponse], { nullable: true })
  images?: WaitingScreenImage[];
}

@ObjectType()
class AppImageResponse {
  @Field()
  id: string;

  @Field()
  url: string;
}
