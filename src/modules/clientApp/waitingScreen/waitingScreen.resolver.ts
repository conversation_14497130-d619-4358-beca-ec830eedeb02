import { Resolver, Query, Context } from '@nestjs/graphql';
import { WaitingScreenService } from './waitingScreen.service';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { UseGuards } from '@nestjs/common';
import { AppWaitingScreenResponse } from './waitingScreen.input';

@Resolver(() => AppWaitingScreenResponse)
@UseGuards(ClientAppAuthGuard)
export class WaitingScreenResolver {
  constructor(private readonly waitingScreenService: WaitingScreenService) {}

  @Query(() => AppWaitingScreenResponse, { nullable: true })
  async clientAppWaitingScreen(
    @Context() context: any,
  ): Promise<AppWaitingScreenResponse | null> {
    return await this.waitingScreenService.findAll(
      context.req.user.machineId,
      context.req.user.id,
    );
  }
}
