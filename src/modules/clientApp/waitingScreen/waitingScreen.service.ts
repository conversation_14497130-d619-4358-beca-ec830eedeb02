import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WaitingScreen } from 'src/modules/common/entity/waitingScreen.entity';
import { EWaitingScreenType } from 'src/enum';
import { AppWaitingScreenResponse } from './waitingScreen.input';

@Injectable()
export class WaitingScreenService {
  constructor(
    @InjectRepository(WaitingScreen)
    private waitingScreenRepository: Repository<WaitingScreen>,
  ) {}

  async findAll(
    machineId: string,
    clientId: string,
  ): Promise<AppWaitingScreenResponse | null> {
    try {
      const customWaitingScreen = await this.waitingScreenRepository
        .createQueryBuilder('waitingScreen')
        .leftJoinAndSelect('waitingScreen.images', 'images')
        .where('waitingScreen.type = :waitingType', {
          waitingType: EWaitingScreenType.Custom,
        })
        .andWhere('waitingScreen.clientId = :clientId', { clientId })
        .andWhere('FIND_IN_SET(:machineId, waitingScreen.machineIds) > 0', {
          machineId,
        })
        .getOne();

      if (customWaitingScreen) {
        return customWaitingScreen;
      }

      const defaultWaitingScreen = await this.waitingScreenRepository
        .createQueryBuilder('waitingScreen')
        .leftJoinAndSelect('waitingScreen.images', 'images')
        .where('waitingScreen.clientId = :clientId', { clientId })
        .andWhere('waitingScreen.type = :waitingType', {
          waitingType: EWaitingScreenType.Default,
        })
        .getOne();
      return defaultWaitingScreen;
    } catch (error) {
      console.log(error);
      throw new Error('Error finding waiting screen');
    }
  }
}
