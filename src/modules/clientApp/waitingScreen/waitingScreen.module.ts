import { Module } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Client } from 'src/modules/common/entity/client.entity';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { WaitingScreen } from 'src/modules/common/entity/waitingScreen.entity';
import { WaitingScreenImage } from 'src/modules/common/entity/waitingScreenImage.entity';
import { FileService } from 'src/modules/common/module/file/file.service';
import { SBLogger } from 'src/modules/logger/logger.service';
import { WaitingScreenResolver } from './waitingScreen.resolver';
import { WaitingScreenService } from './waitingScreen.service';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      WaitingScreen,
      Client,
      Machine,
      WaitingScreenImage,
    ]),
  ],
  providers: [
    WaitingScreenService,
    JwtService,
    SBLogger,
    FileService,
    WaitingScreenResolver,
  ],
  exports: [WaitingScreenService],
})
export class WaitingScreenModule {}
