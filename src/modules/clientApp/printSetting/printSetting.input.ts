import { Field, InputType, ObjectType } from '@nestjs/graphql';
import { EPrintSettingType } from 'src/enum';

@ObjectType()
export class AppPrintSettingResponse {
  @Field()
  id: string;

  @Field({ nullable: true })
  machineIds?: string;

  @Field({ nullable: true })
  type?: EPrintSettingType;
  
  @Field({ nullable: true })
  adjustColor?: number;

  @Field({ nullable: true })
  printRetry?: number;

  @Field({ nullable: true })
  border?: number;

  @Field({ nullable: true })
  sharpness?: number;

  @Field({ nullable: true })
  overcoatFinish?: number;

  @Field({ nullable: true })
  printerQuality?: number;

  @Field({ nullable: true })
  yResolution?: number;

  @Field({ nullable: true })
  adjGammaR?: number;

  @Field({ nullable: true })
  adjGammaG?: number;

  @Field({ nullable: true })
  adjGammaB?: number;

  @Field({ nullable: true })
  adjBrightnessR?: number;

  @Field({ nullable: true })
  adjBrightnessG?: number;

  @Field({ nullable: true })
  adjBrightnessB?: number;

  @Field({ nullable: true })
  adjContrastR?: number;

  @Field({ nullable: true })
  adjContrastG?: number;

  @Field({ nullable: true })
  adjContrastB?: number;

  @Field({ nullable: true })
  adjChroma?: number;

  @Field({ nullable: true })
  icmMethod?: number;

  @Field({ nullable: true })
  createdAt?: string;

  @Field({ nullable: true })
  updatedAt?: string;
}
