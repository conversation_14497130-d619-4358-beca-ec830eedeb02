import { UseGuards } from '@nestjs/common';
import { Context, Query, Resolver } from '@nestjs/graphql';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { PrintSetting } from 'src/modules/common/entity/printSetting.entity';
import {
  AppPrintSettingResponse,
} from './printSetting.input';
import { PrintSettingService } from './printSetting.service';

@Resolver(() => PrintSetting)
@UseGuards(ClientAppAuthGuard)
export class PrintSettingResolver {
  constructor(private printSettingService: PrintSettingService) {}

  @Query(() => AppPrintSettingResponse)
  async clientAppPrintSetting(
    @Context() context: any,
  ): Promise<AppPrintSettingResponse | null> {
    return await this.printSettingService.findAll(
      context.req.user.machineId,
      context.req.user.id,
    );
  }
}
