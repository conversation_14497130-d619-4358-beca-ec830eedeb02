type AppPrintSettingResponse {
  id: ID!
  machineIds: String
  type: String
  adjustColor: Int
  printRetry: Int
  border: Int
  sharpness: Int
  overcoatFinish: Int
  printerQuality: Int
  yResolution: Int
  adjGammaR: Int
  adjGammaG: Int
  adjGammaB: Int
  adjBrightnessR: Int
  adjBrightnessG: Int
  adjBrightnessB: Int
  adjContrastR: Int
  adjContrastG: Int
  adjContrastB: Int
  adjChroma: Int
  icmMethod: Int
}

type Query {
  clientAppPrintSetting: AppPrintSettingResponse
}
