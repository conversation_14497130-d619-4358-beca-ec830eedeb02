import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { PrintSetting } from 'src/modules/common/entity/printSetting.entity';
import { Repository } from 'typeorm';
import { AppPrintSettingResponse } from './printSetting.input';
import { TPrintSettingResponse } from './printSetting.type';
import { EPrintSettingType } from 'src/enum';

@Injectable()
export class PrintSettingService {
  constructor(
    @InjectRepository(PrintSetting)
    private printSettingRepository: Repository<PrintSetting>,
  ) {}

  async findAll(
    machineId: string,
    clientId: string,
  ): Promise<AppPrintSettingResponse | null> {
    try {
      const customPrintSetting = await this.printSettingRepository
        .createQueryBuilder('printSetting')
        .where('printSetting.type = :printType', {
          printType: EPrintSettingType.Custom,
        })
        .andWhere('printSetting.clientId = :clientId', { clientId })
        .andWhere('printSetting.machineId = :machineId', { machineId })
        .getOne();

      if (customPrintSetting) {
        return customPrintSetting;
      }

      const defaultPrintSetting = await this.printSettingRepository
        .createQueryBuilder('printSetting')
        .where('printSetting.clientId = :clientId', { clientId })
        .andWhere('printSetting.type = :printType', {
          printType: EPrintSettingType.Default,
        })
        .getOne();
      return defaultPrintSetting;
    } catch (error) {
      console.log(error);
      throw new Error('Error finding print setting');
    }
  }

  async findOneById(id: string): Promise<TPrintSettingResponse> {
    const res = await this.printSettingRepository.findOneBy({ id });
    return res;
  }
}
