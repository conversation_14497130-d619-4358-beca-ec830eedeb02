import { Modu<PERSON> } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Client } from 'src/modules/common/entity/client.entity';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { PrintSetting } from 'src/modules/common/entity/printSetting.entity';
import { FileService } from 'src/modules/common/module/file/file.service';
import { SBLogger } from 'src/modules/logger/logger.service';
import { PrintSettingResolver } from './printSetting.resolver';
import { PrintSettingService } from './printSetting.service';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      PrintSetting,
      Client,
      Machine,
    ]),
  ],
  providers: [
    JwtService,
    PrintSettingService,
    PrintSettingResolver,
    SBLogger,
    FileService,
  ],
  exports: [PrintSettingService],
})
export class PrintSettingModule {}
