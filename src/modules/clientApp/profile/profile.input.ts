import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType()
export class ClientAppProfileClientInfo {
  @Field()
  id: string;

  @Field()
  name: string;

  @Field({ nullable: true })
  ownerName?: string;

  @Field()
  email: string;

  @Field({ nullable: true })
  phone?: string;

  @Field({ nullable: true })
  address?: string;
}

@ObjectType()
export class ClientAppProfileMachineInfo {
  @Field()
  id: string;

  @Field()
  machineCode: string;

  @Field({ nullable: true })
  location?: string;

  @Field({ nullable: true })
  description?: string;

  @Field({ nullable: true })
  pendingPrints?: number;

  @Field({ nullable: true })
  remainingMedia?: number;

  @Field({ nullable: true })
  remainingInk?: number;

  @Field({ nullable: true })
  state?: string;

  @Field({ nullable: true })
  lastPingAt?: string;

  @Field({ nullable: true })
  createdAt?: Date;
}

@ObjectType()
export class ClientAppProfileResponse {
  @Field(() => ClientAppProfileMachineInfo)
  machine: ClientAppProfileMachineInfo;

  @Field(() => ClientAppProfileClientInfo)
  client: ClientAppProfileClientInfo;
}
