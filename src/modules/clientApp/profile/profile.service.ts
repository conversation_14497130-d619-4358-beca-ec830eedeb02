import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Machine } from '../../common/entity/machine.entity';
import { Client } from '../../common/entity/client.entity';
import { ClientAppProfileResponse } from './profile.input';

@Injectable()
export class ProfileService {
  constructor(
    @InjectRepository(Machine)
    private machineRepository: Repository<Machine>,
    @InjectRepository(Client)
    private clientRepository: Repository<Client>,
  ) {}

  async getProfile(machineId: string): Promise<ClientAppProfileResponse> {
    // Get machine information
    const machine = await this.machineRepository.findOne({
      where: { id: machineId },
    });

    if (!machine) {
      throw new NotFoundException('Machine not found');
    }

    // Get client information using userId from machine
    const client = await this.clientRepository.findOne({
      where: { id: machine.userId },
    });

    if (!client) {
      throw new NotFoundException(
        'Client information not found for this machine',
      );
    }

    return {
      machine: {
        id: machine.id,
        machineCode: machine.machineCode,
        location: machine.location,
        description: machine.description,
        pendingPrints: machine.pendingPrints,
        remainingMedia: machine.remainingMedia,
        remainingInk: machine.remainingInk,
        state: machine.state,
        lastPingAt: machine.lastPingAt,
        createdAt: this.parseDate(machine.createdAt),
      },
      client: {
        id: client.id,
        name: client.name,
        ownerName: client.ownerName,
        email: client.email,
        phone: client.phone,
        address: client.address,
      },
    };
  }

  private parseDate(dateString: string): Date | null {
    if (!dateString) {
      return null;
    }

    try {
      // Try parsing as timestamp first
      const timestamp = parseInt(dateString);
      if (!isNaN(timestamp) && timestamp > 0) {
        return new Date(timestamp);
      }

      // Try parsing as ISO string
      const date = new Date(dateString);
      if (!isNaN(date.getTime())) {
        return date;
      }

      return null;
    } catch (error) {
      console.error('Failed to parse date:', dateString, error);
      return null;
    }
  }
}
