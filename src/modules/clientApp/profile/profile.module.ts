import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { Client } from 'src/modules/common/entity/client.entity';
import { ProfileResolver } from './profile.resolver';
import { ProfileService } from './profile.service';

@Module({
  imports: [TypeOrmModule.forFeature([Machine, Client])],
  providers: [ProfileService, JwtService, SBLogger, ProfileResolver],
  exports: [ProfileService],
})
export class ProfileModule {}
