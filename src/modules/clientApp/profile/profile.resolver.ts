import { Resolver, Context, Query } from '@nestjs/graphql';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { UseGuards } from '@nestjs/common';
import { ProfileService } from './profile.service';
import { ClientAppProfileResponse } from './profile.input';

@Resolver()
@UseGuards(ClientAppAuthGuard)
export class ProfileResolver {
  constructor(private readonly profileService: ProfileService) {}

  @Query(() => ClientAppProfileResponse)
  async clientAppGetProfile(
    @Context() context: any,
  ): Promise<ClientAppProfileResponse> {
    return this.profileService.getProfile(context.req.user.machineId);
  }
}
