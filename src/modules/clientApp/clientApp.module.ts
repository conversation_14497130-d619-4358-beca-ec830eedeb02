import { Module } from '@nestjs/common';
import { AuthModule } from './session/auth.module';
import { WaitingScreenModule } from './waitingScreen/waitingScreen.module';
import { TopicModule } from './topic/topic.module';
import { LayoutModule } from './layout/layout.module';
import { SettingSizeModule } from './settingSize/settingSize.module';
import { FrameModule } from './frame/frame.module';
import { OrderModule } from './order/order.module';
import { PromotionModule } from './promotion/promotion.module';
import { StickerModule } from './sticker/sticker.module';
import { MachineModule } from './machine/machine.module';
import { AppearanceSettingModule } from './appearanceSetting/appearanceSetting.module';
import { ClientAppBackgroundImageModule } from './backgroundImage/backgroundImage.module';
import { LogPublisherModule } from './logPublisher/logPublisher.module';
import { ImageModule } from './uploadImage/image.module';
import { UploadOfflineLogModule } from './uploadOfflineLog/uploadOfflineLog.module';
import { MachineStatusModule } from './machineStatus/machineStatus.module';
import { PrintImageModule } from './printImage/printImage.module';
import { PrintSettingModule } from './printSetting/printSetting.module';
import { ProfileModule } from './profile/profile.module';
@Module({
  imports: [
    AuthModule,
    WaitingScreenModule,
    TopicModule,
    LayoutModule,
    SettingSizeModule,
    FrameModule,
    OrderModule,
    StickerModule,
    PromotionModule,
    MachineModule,
    AppearanceSettingModule,
    ClientAppBackgroundImageModule,
    LogPublisherModule,
    ImageModule,
    UploadOfflineLogModule,
    MachineStatusModule,
    PrintImageModule,
    PrintSettingModule,
    ProfileModule,
  ],
})
export class ClientAppModule {}
