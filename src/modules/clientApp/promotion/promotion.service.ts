import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Promotion } from 'src/modules/common/entity/promotion.entity';
import { PromotionCode } from 'src/modules/common/entity/promotionCode.entity';
import { AppPromotionResponse } from './promotion.response';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';
import { EOrderAppStatus } from '../../../enum/index';
import * as moment from 'moment';

@Injectable()
export class PromotionService {
  constructor(
    private readonly firestoreService: FirestoreService,
    @InjectRepository(Promotion)
    private readonly promotionRepository: Repository<Promotion>,
    @InjectRepository(PromotionCode)
    private readonly promotionCodeRepository: Repository<PromotionCode>,
  ) {}

  async checkPromotion(code: string, req: any): Promise<AppPromotionResponse> {
    const { machineId } = req.user;
    const promotionCode = await this.findPromotionCode(code);
    const promotion = await this.findPromotion(
      promotionCode.promotionId,
      machineId,
    );

    if (promotionCode.isUsed) {
      return this.buildPromotionResponse(promotion, false);
    }

    const isOngoing = this.isPromotionOngoing(promotion);
    const totalDeliveredOrders = await this.getDeliveredOrdersCount(
      req.user.id,
      promotionCode.id,
    );

    const totalUsed = totalDeliveredOrders + promotionCode.numberUsed;
    const canUse = isOngoing && totalUsed < promotion.usageLimitPerCode;

    return this.buildPromotionResponse(promotion, canUse);
  }

  private async findPromotionCode(code: string): Promise<PromotionCode> {
    const promotionCode = await this.promotionCodeRepository.findOne({
      where: { code: code },
    });

    if (!promotionCode) {
      throw new NotFoundException('Promotion code not found');
    }

    return promotionCode;
  }

  private async findPromotion(
    id: string,
    machineId: string,
  ): Promise<Promotion> {
    const promotion = await this.promotionRepository.findOne({
      where: { id },
    });

    if (
      !promotion ||
      !promotion.machineIds
        .split(',')
        .map((id) => id.trim())
        .includes(machineId)
    ) {
      throw new NotFoundException('Promotion not found');
    }

    return promotion;
  }

  private isPromotionOngoing(promotion: Promotion): boolean {
    const now = moment.utc();
    const startDate = moment.utc(Number(promotion.startDate));
    const endDate = moment.utc(Number(promotion.endDate));

    return startDate.isSameOrBefore(now) && endDate.isSameOrAfter(now);
  }

  private async getDeliveredOrdersCount(
    userId: string,
    promotionCodeId: string,
  ): Promise<number> {
    const validStatuses = [
      EOrderAppStatus.PENDING,
      EOrderAppStatus.ACCEPTED,
      EOrderAppStatus.REJECTED,
    ];

    const deliveredOrders = await this.firestoreService.getDocumentsByCondition(
      userId,
      [
        { field: 'status', operator: 'in', value: validStatuses },
        { field: 'promotionCodeId', operator: '==', value: promotionCodeId },
      ],
    );

    return deliveredOrders.length;
  }

  private buildPromotionResponse(
    promotion: Promotion,
    canUse: boolean,
  ): AppPromotionResponse {
    return {
      id: promotion.id,
      name: promotion.name,
      promotionCode: promotion.promotionCode,
      canUse,
      discountValue: promotion.discountValue,
    };
  }
}
