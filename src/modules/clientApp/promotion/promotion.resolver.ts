import { Query, Resolver, Args, Context } from '@nestjs/graphql';
import { PromotionService } from './promotion.service';
import { AppPromotionResponse } from './promotion.response';
import { ClientAppAuthGuard } from 'src/guards/clientAppAuth.guard';
import { UseGuards } from '@nestjs/common';

@Resolver(() => AppPromotionResponse)
@UseGuards(ClientAppAuthGuard)
export class PromotionResolver {
  constructor(private readonly promotionService: PromotionService) {}

  @Query(() => AppPromotionResponse)
  async clientAppCheckPromotion(
    @Args('promotionCode') promotionCode: string,
    @Context() context: any,
  ): Promise<AppPromotionResponse> {
    return this.promotionService.checkPromotion(promotionCode, context.req);
  }
}
