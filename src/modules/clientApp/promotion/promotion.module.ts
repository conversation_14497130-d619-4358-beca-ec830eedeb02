import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtService } from '@nestjs/jwt';
import { SBLogger } from 'src/modules/logger/logger.service';
import { Promotion } from '../../common/entity/promotion.entity';
import { PromotionResolver } from './promotion.resolver';
import { PromotionService } from './promotion.service';
import { PromotionCode } from 'src/modules/common/entity/promotionCode.entity';
import { FirestoreService } from 'src/modules/common/module/firestore/firestore.service';

@Module({
  imports: [TypeOrmModule.forFeature([Promotion, PromotionCode])],
  providers: [
    PromotionService,
    JwtService,
    SBLogger,
    PromotionResolver,
    FirestoreService,
  ],
  exports: [PromotionService],
})
export class PromotionModule {}
