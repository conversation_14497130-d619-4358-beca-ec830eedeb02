import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { JwtService } from '@nestjs/jwt';
import { jwtConstants } from 'src/constants';
import { SBLogger } from 'src/modules/logger/logger.service';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private loggerService: SBLogger,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context);
    const req = ctx.getContext().req;

    const authHeader = req.headers.authorization;
    if (!authHeader) {
      this.loggerService.log('ERROR - guard - empty authorization', req.body);
      throw new UnauthorizedException();
    }

    const token = authHeader.split(' ')[1];
    try {
      const decoded = await this.jwtService.verifyAsync(token, jwtConstants);
      req.user = decoded;
      return true;
    } catch (err) {
      this.loggerService.log(
        'ERROR - guard - UnauthorizedException',
        JSON.stringify(err),
      );
      throw new UnauthorizedException();
    }
  }
}
