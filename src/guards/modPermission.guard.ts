import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { GqlExecutionContext } from '@nestjs/graphql';
import { ERoles } from 'src/enum/role';
import {
  MOD_PERMISSIONS_KEY,
  ModPermission,
} from 'src/decorators/modPermissions.decorator';

@Injectable()
export class ModPermissionGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredPermissions = this.reflector.getAllAndOverride<
      ModPermission[]
    >(MOD_PERMISSIONS_KEY, [context.getHandler(), context.getClass()]);

    if (!requiredPermissions) {
      return true; // Không yêu cầu quyền cụ thể
    }

    const ctx = GqlExecutionContext.create(context);
    const { user } = ctx.getContext().req;

    // N<PERSON><PERSON> là ADMIN, cho phép tất cả các quyền (case-insensitive)
    if (
      user &&
      user.role &&
      user.role.toLowerCase() === ERoles.ADMIN.toLowerCase()
    ) {
      return true;
    }

    // Nếu là MOD, kiểm tra quyền cụ thể (case-insensitive)
    if (
      user &&
      user.role &&
      user.role.toLowerCase() === ERoles.MOD.toLowerCase()
    ) {
      // Kiểm tra xem có yêu cầu quyền ADMIN_ONLY không
      if (requiredPermissions.includes(ModPermission.ADMIN_ONLY)) {
        return false; // MOD không có quyền ADMIN_ONLY
      }

      // Hiện tại, MOD chỉ có các quyền cụ thể đã được định nghĩa
      // Trong tương lai, có thể mở rộng để lưu trữ quyền trong database
      const modPermissions = [
        ModPermission.VIEW_CLIENTS,
        ModPermission.VIEW_CLIENT_DETAILS,
        ModPermission.VIEW_CLIENT_ORDERS,
        ModPermission.UPLOAD_ORDER_IMAGES,
      ];

      return requiredPermissions.some((permission) =>
        modPermissions.includes(permission),
      );
    }

    return false;
  }
}
