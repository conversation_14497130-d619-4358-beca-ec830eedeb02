import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { JwtService } from '@nestjs/jwt';
import { jwtConstants } from 'src/constants';
import { EMachineStatus } from 'src/enum';
import { Machine } from 'src/modules/common/entity/machine.entity';
import { SBLogger } from 'src/modules/logger/logger.service';
import { DataSource } from 'typeorm';

@Injectable()
export class ClientAppAuthGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private loggerService: SBLogger,
    @Inject(DataSource) private readonly dataSource: DataSource,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context);
    const req = ctx.getContext().req;

    const authHeader = req.headers.authorization;
    if (!authHeader) {
      this.loggerService.log('ERROR - guard - empty authorization', req.body);
      throw new UnauthorizedException();
    }

    const token = authHeader.split(' ')[1];
    try {
      const decoded = await this.jwtService.verifyAsync(token, jwtConstants);
      const machine = await this.dataSource
        .getRepository(Machine)
        .createQueryBuilder('machine')
        .where('machine.id = :id', { id: decoded.machineId })
        .getOne();
      if (!machine || machine.status !== EMachineStatus.ACTIVE) {
        this.loggerService.log('ERROR - guard - machine not found', decoded);
        throw new UnauthorizedException();
      }
      req.user = decoded;
      return true;
    } catch (err) {
      this.loggerService.log(
        'ERROR - guard - UnauthorizedException',
        JSON.stringify(err),
      );
      throw new UnauthorizedException();
    }
  }
}
