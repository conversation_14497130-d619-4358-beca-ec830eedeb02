import {
  CanActivate,
  ExecutionContext,
  Inject,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { JwtService } from '@nestjs/jwt';
import { jwtConstants } from 'src/constants';
import { Client } from 'src/modules/common/entity/client.entity';
import { ClientAccount } from 'src/modules/common/entity/clientAccount.entity';
import { SBLogger } from 'src/modules/logger/logger.service';
import { DataSource } from 'typeorm';

@Injectable()
export class ClientAuthGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private loggerService: SBLogger,
    @Inject(DataSource) private readonly dataSource: DataSource,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const ctx = GqlExecutionContext.create(context);
    const req = ctx.getContext().req;

    const authHeader = req.headers.authorization;
    if (!authHeader) {
      this.loggerService.log('ERROR - guard - empty authorization', req.body);
      throw new UnauthorizedException();
    }

    const token = authHeader.split(' ')[1];
    try {
      const decoded = await this.jwtService.verifyAsync(token, jwtConstants);
      let client;
      if (decoded.accountType === 'CLIENT_ACCOUNT') {
        client = await this.dataSource
          .getRepository(Client)
          .createQueryBuilder('client')
          .where('client.id = :id', { id: decoded.clientId })
          .getOne();
        const clientAccount = await this.dataSource
          .getRepository(ClientAccount)
          .createQueryBuilder('clientAccount')
          .where('clientAccount.id = :id', { id: decoded.id })
          .getOne();
        client.loginAccount = clientAccount;
        client.loginRole = clientAccount.role;
      } else {
        client = await this.dataSource
          .getRepository(Client)
          .createQueryBuilder('client')
          .where('client.id = :id', { id: decoded.id })
          .getOne();
        client.loginAccount = client;
        client.loginRole = 'ADMIN';
      }
      if (!client) {
        this.loggerService.log('ERROR - guard - client not found', decoded);
        throw new UnauthorizedException();
      }
      req.user = client;
      return true;
    } catch (err) {
      this.loggerService.log(
        'ERROR - guard - UnauthorizedException',
        JSON.stringify(err),
      );
      throw new UnauthorizedException();
    }
  }
}
