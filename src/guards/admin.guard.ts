import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ERoles } from 'src/enum/role';

@Injectable()
export class AdminGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    return (
      user &&
      user.role &&
      (user.role.toLowerCase() === ERoles.ADMIN.toLowerCase() ||
        user.role.toLowerCase() === ERoles.MOD.toLowerCase())
    );
  }
}
