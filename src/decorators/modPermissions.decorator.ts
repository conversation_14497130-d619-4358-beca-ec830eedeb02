import { SetMetadata } from '@nestjs/common';

export enum ModPermission {
  VIEW_CLIENTS = 'view_clients',
  VIEW_CLIENT_DETAILS = 'view_client_details',
  VIEW_CLIENT_ORDERS = 'view_client_orders',
  UPLOAD_ORDER_IMAGES = 'upload_order_images',
  ADMIN_ONLY = 'admin_only',
}

export const MOD_PERMISSIONS_KEY = 'mod_permissions';
export const ModPermissions = (...permissions: ModPermission[]) =>
  SetMetadata(MOD_PERMISSIONS_KEY, permissions);
