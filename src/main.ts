import { Valida<PERSON>Pip<PERSON>, Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { graphqlUploadExpress } from 'graphql-upload';
import * as bodyParser from 'body-parser';
import { morganLogger } from './middlewares/morgan';
import * as express from 'express';
import { ExpressAdapter } from '@nestjs/platform-express';
import * as http from 'http';
import { AllExceptionsFilter } from './filters/all-exceptions.filter';

async function bootstrap() {
  const expressApp = express();

  // Khởi tạo NestJS với ExpressAdapter
  const app = await NestFactory.create(
    AppModule,
    new ExpressAdapter(expressApp),
    {
      logger: ['error', 'warn', 'log'],
      abortOnError: false, // Prevent NestJS from crashing on unhandled errors
    },
  );

  // Add global exception filter for unhandled errors
  process.on('unhandledRejection', (reason, promise) => {
    Logger.error(
      `Unhandled Rejection at: ${promise}, reason: ${reason}`,
      undefined,
      'Bootstrap',
    );
  });

  // Add handler for uncaught exceptions to prevent container crashes
  process.on('uncaughtException', (error) => {
    Logger.error(
      `Uncaught Exception: ${error.message}`,
      error.stack,
      'Bootstrap',
    );
    // Không thoát process để tránh container crash
    // Trong môi trường production, có thể cân nhắc việc gửi thông báo đến hệ thống monitoring
  });

  // Add handler for unhandled errors in streams
  process.on('warning', (warning) => {
    Logger.warn(
      `Process Warning: ${warning.name}: ${warning.message}`,
      warning.stack,
      'Bootstrap',
    );
  });

  // Body parser middleware
  expressApp.use(
    bodyParser.json({
      limit: '100mb',
      verify: (req: any, _res, buf) => {
        req.rawBody = buf;
      },
    }),
  );
  expressApp.use(bodyParser.urlencoded({ extended: true, limit: '100mb' }));

  // Morgan logger middleware
  expressApp.use(morganLogger());

  // GraphQL upload middleware with improved error handling
  expressApp.use(
    graphqlUploadExpress({
      maxFileSize: 100_000_000, // 100MB
      maxFieldSize: 100_000_000, // 100MB
      maxFiles: 10,
    }),
  );

  // Middleware to catch errors from graphql-upload
  expressApp.use((err: any, _req: any, res: any, next: any) => {
    if (err) {
      // Xử lý lỗi từ graphql-upload
      if (
        err.statusCode === 499 ||
        (err.message &&
          (err.message.includes('disconnect') ||
            err.message.includes('aborted') ||
            err.message.includes('closed')))
      ) {
        Logger.warn(`Upload error: ${err.message}`, err.stack, 'GraphQLUpload');

        // Trả về lỗi 499 Client Closed Request nếu response vẫn có thể ghi
        if (!res.headersSent && !res.writableEnded) {
          return res.status(499).json({
            errors: [
              {
                message: 'Client disconnected during file upload',
                extensions: {
                  code: 'CLIENT_DISCONNECT',
                  exception: {
                    statusCode: 499,
                    message: err.message,
                  },
                },
              },
            ],
          });
        }
      }

      // Log lỗi khác
      Logger.error(
        `GraphQL upload error: ${err.message}`,
        err.stack,
        'GraphQLUpload',
      );
    }

    next(err);
  });

  // Add middleware to handle client disconnects gracefully
  expressApp.use((req, res, next) => {
    req.on('close', () => {
      if (!res.writableEnded) {
        Logger.warn(
          `Client disconnected during request processing: ${req.method} ${req.url}`,
          'RequestHandler',
        );
      }
    });
    next();
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      exceptionFactory: (errors) => {
        return new Error(
          JSON.stringify({
            errors: errors.map((error) => ({
              field: error.property,
              constraints: error.constraints,
            })),
            message: 'Validation failed',
            code: 'VALIDATION_ERROR',
          }),
        );
      },
    }),
  );

  // Enable CORS
  app.enableCors({
    origin: true,
    credentials: true,
    exposedHeaders: ['X-Request-ID', 'X-Response-Time'],
  });

  // Đăng ký bộ lọc ngoại lệ toàn cục
  app.useGlobalFilters(new AllExceptionsFilter());

  await app.init();
  const port = process.env.PORT || 3000;

  // Fallback to HTTP server if HTTPS fails
  const httpServer = http.createServer(expressApp);
  httpServer.on('error', (err) => {
    Logger.error(`HTTP server error: ${err.message}`, err.stack, 'Bootstrap');
  });

  // Increase timeout for large file uploads
  httpServer.timeout = 300000; // 5 minutes
  httpServer.keepAliveTimeout = 65000; // 65 seconds
  httpServer.headersTimeout = 66000; // 66 seconds

  httpServer.listen(port, () => {
    Logger.log(
      `🚀 Fallback HTTP server running at http://localhost:${port}`,
      'Bootstrap',
    );
  });
}

bootstrap();
