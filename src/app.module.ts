import { Mo<PERSON><PERSON>, MiddlewareConsumer, NestModule } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { typeOrmConfig } from './configs/typeorm.config';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { GraphQLModule } from '@nestjs/graphql';
import { GraphQLError, GraphQLFormattedError } from 'graphql';
import {
  RequestIdMiddleware,
  getCurrentRequestId,
} from './middlewares/request-id.middleware';
import { LoggerContextMiddleware } from './middlewares/logger-context.middleware';

import { JwtService } from '@nestjs/jwt';
import { ConfigModule } from '@nestjs/config';
import { LoggerModule } from './modules/logger/logger.module';
import { AdminModule } from './modules/admin/admin.module';
import { ClientModule } from './modules/client/client.module';
import { FileModule } from './modules/common/module/file/file.module';
import { FrameGeneratorModule } from './modules/common/module/frameGenerator/frameGenerator.module';
import { QueueModule } from './modules/common/module/queue/queue.module';
import { ClientAppModule } from './modules/clientApp/clientApp.module';
import { ClientMemoryModule } from './modules/clientMemory/clientMemory.module';
import { PayosModule } from './modules/payos/payos.module';
import { MachineStatusSubscriber } from './modules/common/subscriber/machineStatus.subscriber';
import { AutobankModule } from './modules/autobank/autobank.module';
import { HealthModule } from './modules/health/health.module';

interface CustomGraphQLFormattedError extends GraphQLFormattedError {
  code?: string;
  statusCode?: number;
}

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true, // Làm cho ConfigModule có sẵn trong toàn bộ ứng dụng
    }),
    TypeOrmModule.forRoot(typeOrmConfig),
    GraphQLModule.forRoot<ApolloDriverConfig>({
      driver: ApolloDriver,
      autoSchemaFile: 'src/migrations/schema.gql',
      playground: true,
      debug: true,
      introspection: true,
      csrfPrevention: false,
      subscriptions: {
        'subscriptions-transport-ws': {
          keepAlive: 30000, // 30 seconds
          onConnect: (connectionParams) => {
            console.log('Client connected to WebSocket');
            return connectionParams;
          },
          onDisconnect: () => {
            console.log('Client disconnected from WebSocket');
          },
        },
      },
      formatError: (error: GraphQLError): CustomGraphQLFormattedError => {
        const exception: any = error.extensions?.exception || {};
        const response: any = exception.response || {};

        // Get the request ID from the global context
        const requestId = getCurrentRequestId();

        // Xử lý lỗi client disconnect
        if (
          error.message.includes('disconnect') ||
          error.message.includes('aborted') ||
          error.message.includes('closed') ||
          error.message.includes('Request disconnected')
        ) {
          // Log ở mức warning thay vì error
          console.warn(
            `GraphQL Client Disconnect [reqId:${requestId}]: ${error.message}`,
          );

          return {
            message: 'Client disconnected during file upload',
            code: 'CLIENT_DISCONNECT',
            statusCode: 499, // Client Closed Request
            path: error.path,
            extensions: {
              requestId,
              originalError: error.message,
            },
          };
        }

        // Log the error with the request ID for better debugging
        console.error(`GraphQL Error [reqId:${requestId}]: ${error.message}`);

        // Xác định status code từ response hoặc exception
        let statusCode = 500;

        // Nếu có response.statusCode, sử dụng nó
        if (
          response &&
          typeof response.statusCode === 'number' &&
          !isNaN(response.statusCode)
        ) {
          statusCode = response.statusCode;
        }
        // Nếu có response.status, sử dụng nó
        else if (
          response &&
          typeof response.status === 'number' &&
          !isNaN(response.status)
        ) {
          statusCode = response.status;
        }
        // Nếu có exception.statusCode, sử dụng nó
        else if (
          exception.statusCode &&
          typeof exception.statusCode === 'number' &&
          !isNaN(exception.statusCode)
        ) {
          statusCode = exception.statusCode;
        }
        // Nếu có exception.status, sử dụng nó
        else if (
          exception.status &&
          typeof exception.status === 'number' &&
          !isNaN(exception.status)
        ) {
          statusCode = exception.status;
        }

        // Đảm bảo lỗi Forbidden trả về 403, không phải 500
        if (
          error.message.includes('Forbidden') ||
          error.extensions?.code === 'FORBIDDEN'
        ) {
          statusCode = 403;
        }

        return {
          message: response.message || error.message,
          code: (error.extensions?.code as string) || 'INTERNAL_SERVER_ERROR',
          statusCode: statusCode,
          path: error.path,
          extensions: {
            ...error.extensions,
            requestId,
          },
        };
      },
      context: ({ req, res }) => {
        // Make request ID available in GraphQL context
        return { req, res, requestId: req.requestId };
      },
    }),
    LoggerModule,
    AdminModule,
    ClientModule,
    FileModule,
    FrameGeneratorModule,
    QueueModule,
    ClientAppModule,
    ClientMemoryModule,
    PayosModule,
    AutobankModule,
    HealthModule,
  ],
  controllers: [AppController],
  providers: [AppService, JwtService, MachineStatusSubscriber],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply middleware in the correct order:
    // 1. RequestIdMiddleware - generates and sets the request ID
    // 2. LoggerContextMiddleware - sets up logging with the request ID
    consumer
      .apply(RequestIdMiddleware)
      .forRoutes('*')
      .apply(LoggerContextMiddleware)
      .forRoutes('*');
  }
}
